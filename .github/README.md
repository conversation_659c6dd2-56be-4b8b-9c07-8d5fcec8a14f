# GitHub Actions - Pipeline Simple

## 📁 Fichiers créés

- `build.yml` : Pipeline principal (tests + build)
- `docker.yml` : Construction de l'image Docker

## 🚀 Ce que fait le pipeline

### Pipeline `build.yml`
✅ Se déclenche sur :
- Push sur la branche `main`
- Pull Request vers `main`

✅ Actions :
1. Récupère votre code
2. Installe Java 17
3. Lance les tests avec Maven
4. Construit l'application (fichier JAR)
5. Sauvegarde le JAR comme "artifact"

### Pipeline `docker.yml`
✅ Se déclenche sur :
- Push sur la branche `main` seulement

✅ Actions :
1. Récupère votre code
2. Construit l'image Docker

## 🔧 Pour activer Docker Hub (optionnel)

Si vous voulez pousser vos images Docker :

1. Allez dans votre repo GitHub
2. Settings > Secrets and variables > Actions
3. Ajoutez ces secrets :
   - `DOCKER_USERNAME` : votre nom d'utilisateur Docker Hub
   - `DOCKER_PASSWORD` : votre mot de passe Docker Hub
4. Décommentez les lignes dans `docker.yml`

## 📝 Comment ça marche

1. Quand vous faites un `git push` sur `main` → les 2 pipelines se lancent
2. Quand vous créez une Pull Request → seul le pipeline `build.yml` se lance
3. Vous pouvez voir les résultats dans l'onglet "Actions" de votre repo GitHub

## ✅ C'est tout !

Votre pipeline est prêt. Faites un push et regardez l'onglet "Actions" dans GitHub !
