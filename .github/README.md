# GitHub Actions Workflows

Ce répertoire contient tous les workflows GitHub Actions pour le projet trial-period-api.

## 📋 Vue d'ensemble des workflows

### 🚀 CI/CD Principal (`ci-cd.yml`)
**Déclencheurs :** Push sur `main`/`develop`, Pull Requests
- ✅ Tests unitaires avec JaCoCo
- 🏗️ Build Maven
- 🐳 Build et push Docker
- 🔒 Scan de sécurité avec Trivy
- 🚀 Déploiement automatique (staging/production)

### 🏷️ Release (`release.yml`)
**Déclencheurs :** Tags `v*`, Manuel
- 📦 Création de release GitHub
- 📝 Génération automatique du changelog
- 🐳 Build et tag Docker pour release
- 📎 Upload des artifacts JAR

### 📊 Analyse SonarQube (`sonar-analysis.yml`)
**Déclencheurs :** Push, PR, Hebdomadaire, Manuel
- 🔍 Analyse de qualité de code
- 📈 Couverture de tests
- 💬 Commentaires automatiques sur PR
- 🚪 Quality Gate check

### 🔒 Vérification des dépendances (`dependency-check.yml`)
**Déclencheurs :** <PERSON>ush, PR, Hebdomadaire, Manuel
- 🛡️ Scan OWASP des vulnérabilités
- ⚖️ Vérification des licences
- 📋 Rapport hebdomadaire des mises à jour
- 🎯 Issues automatiques pour les updates

### 🔍 Vérifications PR (`pr-checks.yml`)
**Déclencheurs :** Pull Requests
- ✅ Validation du titre PR (Conventional Commits)
- 🔍 Détection des changements d'API
- 📏 Vérification de la taille du JAR
- ⚡ Tests de performance (si label `performance`)
- 🎯 Analyse de qualité de code

## 🔧 Configuration requise

### Secrets GitHub à configurer

```bash
# Docker Hub
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# SonarCloud
SONAR_TOKEN=your-sonar-token
SONAR_ORGANIZATION=your-sonar-org
```

### Variables d'environnement

Les workflows utilisent les variables suivantes :
- `JAVA_VERSION`: Version Java (défaut: 17)
- `MAVEN_OPTS`: Options Maven (défaut: -Xmx1024m)
- `DOCKER_IMAGE_NAME`: Nom de l'image Docker

## 🏗️ Structure des jobs

### CI/CD Pipeline
```mermaid
graph TD
    A[Test] --> B[Build]
    B --> C[Docker Build]
    B --> D[Security Scan]
    C --> E[Deploy Staging]
    C --> F[Deploy Production]
    D --> E
    D --> F
```

### Conditions de déploiement
- **Staging** : Push sur `develop`
- **Production** : Push sur `main`
- **Release** : Tags `v*`

## 📝 Bonnes pratiques

### Titres de Pull Request
Utilisez le format Conventional Commits :
```
feat: add new authentication endpoint
fix: resolve memory leak in user service
docs: update API documentation
```

### Labels pour PR
- `performance` : Déclenche les tests de performance
- `dependencies` : Pour les mises à jour de dépendances
- `maintenance` : Pour les tâches de maintenance

### Gestion des versions
- **Patch** : `v1.0.1` - Corrections de bugs
- **Minor** : `v1.1.0` - Nouvelles fonctionnalités
- **Major** : `v2.0.0` - Breaking changes

## 🔍 Monitoring et rapports

### Artifacts générés
- Rapports de tests (Surefire)
- Couverture de code (JaCoCo)
- Rapports de sécurité (OWASP, Trivy)
- Rapports de qualité (SonarQube)

### Notifications
- Commentaires automatiques sur PR
- Issues pour les mises à jour de dépendances
- Rapports de sécurité dans l'onglet Security

## 🛠️ Maintenance

### Mise à jour des workflows
1. Testez les changements sur une branche de feature
2. Vérifiez que tous les secrets sont configurés
3. Documentez les changements dans ce README

### Dépannage courant
- **Échec SonarQube** : Vérifiez le token et l'organisation
- **Échec Docker** : Vérifiez les credentials Docker Hub
- **Tests lents** : Optimisez le cache Maven

## 📚 Ressources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Maven GitHub Actions](https://github.com/actions/setup-java)
- [Docker Build Push Action](https://github.com/docker/build-push-action)
- [SonarQube GitHub Action](https://github.com/SonarSource/sonarqube-github-action)
