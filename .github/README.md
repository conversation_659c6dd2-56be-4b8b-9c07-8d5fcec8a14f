# GitHub Actions - Pipeline Simple

## 📁 Fichiers créés

- `build.yml` : Pipeline principal (build seulement)
- `docker.yml` : Construction de l'image Docker
- `tests.yml` : Tests (optionnel, manuel)

## 🚀 Ce que fait le pipeline

### Pipeline `build.yml` ✅
Se déclenche sur :
- Push sur la branche `main`
- Pull Request vers `main`

Actions :
1. Récupère votre code
2. Installe Java 17
3. Donne les permissions à Maven
4. Construit l'application (fichier JAR)
5. Sauvegarde le JAR comme "artifact"

**Note :** Les tests sont temporairement désactivés car ils ont besoin de configuration supplémentaire.

### Pipeline `docker.yml` ✅
Se déclenche sur :
- Push sur la branche `main` seulement

Actions :
1. Récupère votre code
2. Construit l'image Docker

### Pipeline `tests.yml` ⚠️
Se déclenche :
- Manuellement seulement (pour l'instant)

Actions :
1. Lance les tests simples
2. Lance tous les tests (peut échouer)
3. Affiche les résultats

## 🔧 Pour activer Docker Hub (optionnel)

Si vous voulez pousser vos images Docker :

1. Allez dans votre repo GitHub
2. Settings > Secrets and variables > Actions
3. Ajoutez ces secrets :
   - `DOCKER_USERNAME` : votre nom d'utilisateur Docker Hub
   - `DOCKER_PASSWORD` : votre mot de passe Docker Hub
4. Décommentez les lignes dans `docker.yml`

## 📝 Comment ça marche

1. Quand vous faites un `git push` sur `main` → les 2 pipelines se lancent
2. Quand vous créez une Pull Request → seul le pipeline `build.yml` se lance
3. Vous pouvez voir les résultats dans l'onglet "Actions" de votre repo GitHub

## ✅ C'est tout !

Votre pipeline est prêt. Faites un push et regardez l'onglet "Actions" dans GitHub !

## 🔧 Pour activer les tests plus tard

Quand vous voudrez activer les tests :
1. Corrigez les problèmes de configuration des tests
2. Dans `tests.yml`, décommentez les lignes pour les Pull Requests
3. Dans `build.yml`, ajoutez une étape de test avant le build

## 📝 Problèmes de tests identifiés

- Tests Spring Boot : besoin de configuration base de données
- Tests de fichiers : problèmes d'accès aux ressources
- Configuration Jasypt : clés de chiffrement manquantes
