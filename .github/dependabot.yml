version: 2
updates:
  # Maven dependencies
  - package-ecosystem: "maven"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "06:00"
    open-pull-requests-limit: 5
    reviewers:
      - "your-team"  # Remplacez par votre équipe
    assignees:
      - "your-username"  # Remplacez par votre nom d'utilisateur
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "java"
    ignore:
      # Ignorer les mises à jour majeures pour certaines dépendances critiques
      - dependency-name: "org.springframework.boot:*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "org.springframework.security:*"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "06:00"
    open-pull-requests-limit: 3
    reviewers:
      - "your-team"
    assignees:
      - "your-username"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"

  # Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "06:00"
    open-pull-requests-limit: 2
    reviewers:
      - "your-team"
    assignees:
      - "your-username"
    commit-message:
      prefix: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
