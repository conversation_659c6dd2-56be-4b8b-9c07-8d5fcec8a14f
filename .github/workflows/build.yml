name: Build et Test

# Quand le pipeline se déclenche
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  # Job pour tester et construire l'application
  build:
    name: Build Spring Boot App
    runs-on: ubuntu-latest
    
    steps:
    # 1. Récupérer le code
    - name: <PERSON><PERSON><PERSON><PERSON>rer le code
      uses: actions/checkout@v4

    # 2. Installer Java 17
    - name: Installer Java 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    # 3. Donner les permissions au script Maven
    - name: Donner permissions à mvnw
      run: chmod +x ./mvnw

    # 4. Lancer les tests avec plus de détails
    - name: Lancer les tests
      run: ./mvnw test -X

    # 5. Construire l'application
    - name: Construire l'application
      run: ./mvnw clean package -DskipTests

    # 6. Garder le fichier JAR créé
    - name: Sauvegarder le JAR
      uses: actions/upload-artifact@v4
      with:
        name: app-jar
        path: target/*.jar
