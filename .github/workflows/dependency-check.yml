name: Dependency & Security Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 6 * * 1'  # Vérification hebdomadaire le lundi à 6h
  workflow_dispatch:

env:
  JAVA_VERSION: '17'

jobs:
  dependency-check:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run OWASP Dependency Check
      run: |
        ./mvnw org.owasp:dependency-check-maven:check \
          -DfailBuildOnCVSS=7 \
          -DsuppressionsLocation=.github/dependency-check-suppressions.xml

    - name: Upload dependency check results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: dependency-check-report
        path: target/dependency-check-report.html

    - name: Upload to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: target/dependency-check-report.sarif

  license-check:
    name: License Compliance Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Generate license report
      run: |
        ./mvnw license:aggregate-third-party-report

    - name: Upload license report
      uses: actions/upload-artifact@v4
      with:
        name: license-report
        path: target/site/aggregate-third-party-report.html

  update-dependencies:
    name: Check for Dependency Updates
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Check for dependency updates
      run: |
        ./mvnw versions:display-dependency-updates > dependency-updates.txt
        ./mvnw versions:display-plugin-updates > plugin-updates.txt

    - name: Create issue for dependency updates
      if: github.event_name == 'schedule'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          let dependencyUpdates = '';
          let pluginUpdates = '';
          
          try {
            dependencyUpdates = fs.readFileSync('dependency-updates.txt', 'utf8');
            pluginUpdates = fs.readFileSync('plugin-updates.txt', 'utf8');
          } catch (error) {
            console.log('No update files found');
            return;
          }
          
          const hasUpdates = dependencyUpdates.includes('The following dependencies') || 
                           pluginUpdates.includes('The following plugin updates');
          
          if (hasUpdates) {
            const issueBody = `## 📦 Weekly Dependency Update Report
          
          This is an automated report of available dependency and plugin updates.
          
          ### Dependency Updates
          \`\`\`
          ${dependencyUpdates}
          \`\`\`
          
          ### Plugin Updates
          \`\`\`
          ${pluginUpdates}
          \`\`\`
          
          Please review these updates and create a PR to update dependencies as needed.
          
          ---
          *This issue was automatically created by the dependency check workflow.*`;
          
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `📦 Weekly Dependency Updates - ${new Date().toISOString().split('T')[0]}`,
              body: issueBody,
              labels: ['dependencies', 'maintenance']
            });
          }
