name: Build Docker

# Se déclenche seulement quand on pousse sur main
on:
  push:
    branches: [ main ]

jobs:
  docker:
    name: Construire image Docker
    runs-on: ubuntu-latest
    
    steps:
    # 1. R<PERSON>cupérer le code
    - name: R<PERSON><PERSON><PERSON>rer le code
      uses: actions/checkout@v4

    # 2. Construire l'image Docker
    - name: Construire l'image Docker
      run: docker build -t trial-period-api:latest .

    # 3. (Optionnel) Pousser vers Docker Hub
    # Décommentez ces lignes si vous voulez pousser vers Docker Hub
    # - name: Se connecter à Docker Hub
    #   uses: docker/login-action@v3
    #   with:
    #     username: ${{ secrets.DOCKER_USERNAME }}
    #     password: ${{ secrets.DOCKER_PASSWORD }}
    #
    # - name: Pousser l'image
    #   run: docker push trial-period-api:latest
