name: Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened, ready_for_review]

env:
  JAVA_VERSION: '17'

jobs:
  pr-validation:
    name: PR Validation
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Validate PR title
      uses: amannn/action-semantic-pull-request@v5
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        types: |
          feat
          fix
          docs
          style
          refactor
          perf
          test
          chore
          ci
        requireScope: false
        disallowScopes: |
          release
        subjectPattern: ^(?![A-Z]).+$
        subjectPatternError: |
          The subject "{subject}" found in the pull request title "{title}"
          didn't match the configured pattern. Please ensure that the subject
          doesn't start with an uppercase character.

    - name: Check for breaking changes
      id: breaking-changes
      run: |
        # Vérifier s'il y a des changements dans les API publiques
        if git diff --name-only origin/${{ github.base_ref }}...HEAD | grep -E "(Controller|Service|Repository)\.java$"; then
          echo "api_changes=true" >> $GITHUB_OUTPUT
        else
          echo "api_changes=false" >> $GITHUB_OUTPUT
        fi

    - name: Comment on API changes
      if: steps.breaking-changes.outputs.api_changes == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## ⚠️ API Changes Detected
            
            This PR contains changes to API classes (Controllers, Services, or Repositories).
            Please ensure:
            - [ ] Backward compatibility is maintained
            - [ ] API documentation is updated
            - [ ] Integration tests are updated
            - [ ] Version bump is considered if breaking changes are introduced
            
            If this introduces breaking changes, please update the version accordingly.`
          })

  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run Checkstyle
      run: ./mvnw checkstyle:check
      continue-on-error: true

    - name: Run SpotBugs
      run: ./mvnw spotbugs:check
      continue-on-error: true

    - name: Run PMD
      run: ./mvnw pmd:check
      continue-on-error: true

    - name: Upload code quality reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: code-quality-reports
        path: |
          target/checkstyle-result.xml
          target/spotbugsXml.xml
          target/pmd.xml

  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false && contains(github.event.pull_request.labels.*.name, 'performance')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: ./mvnw clean package -DskipTests

    - name: Start application for performance testing
      run: |
        java -jar target/trial-period-api.jar --spring.profiles.active=test &
        sleep 30  # Attendre que l'application démarre

    - name: Run performance tests
      run: |
        # Exemple avec curl pour des tests de performance basiques
        echo "Running basic performance tests..."
        for i in {1..10}; do
          curl -w "@.github/curl-format.txt" -o /dev/null -s "http://localhost:8081/actuator/health"
        done

    - name: Stop application
      run: pkill -f "trial-period-api.jar"

  size-check:
    name: Bundle Size Check
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: ./mvnw clean package -DskipTests

    - name: Check JAR size
      run: |
        JAR_SIZE=$(stat -c%s target/trial-period-api.jar)
        JAR_SIZE_MB=$((JAR_SIZE / 1024 / 1024))
        echo "JAR size: ${JAR_SIZE_MB}MB"
        
        if [ $JAR_SIZE_MB -gt 100 ]; then
          echo "⚠️ Warning: JAR size is larger than 100MB"
          echo "jar_size_warning=true" >> $GITHUB_ENV
        fi

    - name: Comment on large JAR size
      if: env.jar_size_warning == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 📦 Large JAR Size Warning
            
            The built JAR file is larger than 100MB. This might impact:
            - Deployment time
            - Memory usage
            - Startup time
            
            Consider:
            - Removing unused dependencies
            - Using Spring Boot's layered JARs
            - Optimizing resource files
            
            Please review the dependencies and optimize if possible.`
          })
