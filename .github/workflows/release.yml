name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

env:
  JAVA_VERSION: '17'
  DOCKER_IMAGE_NAME: trial-period-api

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Get version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        else
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        fi

    - name: Generate changelog
      id: changelog
      run: |
        # Générer un changelog simple basé sur les commits
        if git describe --tags --abbrev=0 HEAD^ >/dev/null 2>&1; then
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^)
          CHANGELOG=$(git log --pretty=format:"- %s" $PREVIOUS_TAG..HEAD)
        else
          CHANGELOG=$(git log --pretty=format:"- %s")
        fi
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.version.outputs.version }}
        release_name: Release ${{ steps.version.outputs.version }}
        body: |
          ## Changes in this Release
          ${{ steps.changelog.outputs.changelog }}
          
          ## Docker Image
          ```bash
          docker pull ${{ env.DOCKER_IMAGE_NAME }}:${{ steps.version.outputs.version }}
          ```
        draft: false
        prerelease: ${{ contains(steps.version.outputs.version, '-') }}

  build-and-release:
    name: Build and Release Assets
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build application
      run: ./mvnw clean package -DskipTests

    - name: Upload JAR to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: target/trial-period-api.jar
        asset_name: trial-period-api-${{ needs.create-release.outputs.version }}.jar
        asset_content_type: application/java-archive

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ env.DOCKER_IMAGE_NAME }}:${{ needs.create-release.outputs.version }}
          ${{ env.DOCKER_IMAGE_NAME }}:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
