name: SonarQube Analysis

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1'  # Analyse hebdomadaire le lundi à 2h
  workflow_dispatch:

env:
  JAVA_VERSION: '17'

jobs:
  sonar-analysis:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache SonarQube packages
      uses: actions/cache@v4
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar

    - name: Cache Maven dependencies
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run tests and generate coverage
      run: ./mvnw clean test jacoco:report

    - name: Run SonarQube analysis
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: |
        ./mvnw sonar:sonar \
          -Dsonar.projectKey=trial-period-api \
          -Dsonar.organization=${{ secrets.SONAR_ORGANIZATION }} \
          -Dsonar.host.url=https://sonarcloud.io \
          -Dsonar.login=${{ secrets.SONAR_TOKEN }} \
          -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml

    - name: SonarQube Quality Gate check
      id: sonarqube-quality-gate-check
      uses: sonarqube-quality-gate-action@master
      timeout-minutes: 5
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      continue-on-error: true

    - name: Comment PR with SonarQube results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const qualityGateStatus = '${{ steps.sonarqube-quality-gate-check.outputs.quality-gate-status }}';
          const projectKey = 'trial-period-api';
          const sonarUrl = `https://sonarcloud.io/dashboard?id=${projectKey}`;
          
          let message = `## 📊 SonarQube Analysis Results\n\n`;
          message += `**Quality Gate Status:** ${qualityGateStatus === 'PASSED' ? '✅ PASSED' : '❌ FAILED'}\n\n`;
          message += `[📈 View detailed report on SonarCloud](${sonarUrl})\n\n`;
          
          if (qualityGateStatus !== 'PASSED') {
            message += `⚠️ **Action Required:** Please fix the issues identified by SonarQube before merging.`;
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: message
          });
