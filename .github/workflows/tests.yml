name: Tests (Optionnel)

# Ce workflow est optionnel et peut être lancé manuellement
on:
  workflow_dispatch:
  # Décommentez la ligne suivante quand les tests seront fixés
  # pull_request:
  #   branches: [ main ]

jobs:
  test:
    name: Lancer les tests
    runs-on: ubuntu-latest
    
    steps:
    # 1. <PERSON><PERSON><PERSON><PERSON><PERSON> le code
    - name: <PERSON><PERSON><PERSON><PERSON><PERSON> le code
      uses: actions/checkout@v4

    # 2. Installer Java 17
    - name: Installer Java 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    # 3. Donner les permissions au script Maven
    - name: Donner permissions à mvnw
      run: chmod +x ./mvnw

    # 4. Lancer les tests simples d'abord
    - name: Tests simples
      run: ./mvnw test -Dtest=SimpleTest
      continue-on-error: true

    # 5. Lancer tous les tests
    - name: Tous les tests
      run: ./mvnw test
      continue-on-error: true

    # 6. Afficher les résultats même en cas d'échec
    - name: Afficher les résultats des tests
      if: always()
      run: |
        echo "=== Résultats des tests ==="
        if [ -d "target/surefire-reports" ]; then
          find target/surefire-reports -name "*.txt" -exec echo "=== {} ===" \; -exec cat {} \;
        else
          echo "Aucun rapport de test trouvé"
        fi
