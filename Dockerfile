FROM maven:3.8.4-openjdk-17 AS builder
RUN mkdir -p /workspace
WORKDIR /workspace

# Copy the executable into the container at /app
ADD *.jar trial-period-api.jar
COPY ./src/main/resources/application-prod.properties /workspace/
COPY Cert.p12 /workspace/

COPY pom.xml /workspace
COPY src /workspace/src
RUN mvn -f pom.xml clean package -Dspring.profiles.active=prod

FROM openjdk:17-jdk-alpine
COPY --from=builder /workspace/target/*.jar app.jar

EXPOSE 8081
ENTRYPOINT ["java", "-jar", "-Dspring.profiles.active=prod", "app.jar"]