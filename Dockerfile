# Étape 1: Build avec Maven
FROM maven:3.8.4-openjdk-17 AS builder

# Créer le répertoire de travail
WORKDIR /workspace

# Copier les fichiers nécessaires pour le build
COPY pom.xml /workspace/
COPY src /workspace/src

# Construire l'application
RUN mvn clean package -DskipTests

# Étape 2: Image finale
FROM openjdk:17-jdk-alpine

# Copier le JAR depuis l'étape de build
COPY --from=builder /workspace/target/trial-period-api.jar app.jar

# Exposer le port
EXPOSE 8081

# Lancer l'application
ENTRYPOINT ["java", "-jar", "app.jar"]