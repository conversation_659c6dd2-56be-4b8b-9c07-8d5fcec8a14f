### Introduction

trial-period-api is a trial period management api developed using Spring Boot based on Maven.

### Environnement

-   **Java / JDK :**
    -   Version: openjdk version "17.0.3" 2022-04-19
    -   Runtime Environment: OpenJDK Runtime Environment Temurin-17.0.3+7
    -   Server VM: OpenJDK 64-Bit Server VM Temurin-17.0.3+7
        -   You can verify your Java version by running `java -version` in your terminal


-   **Jakarta :**
    -   Version: JAKARTA EE 9


-   **Maven (Bundled):**
    -   Version: 3.6.3
        -   You can check if <PERSON><PERSON> is installed by running `mvn -v` in your terminal.
    

-   **IDE:**
    -   IntelliJ IDEA Ultimate 2021.1.3


### Getting Started

**Building the application:**

1.  Clone this repository.
2.  Open a terminal in the project directory.
3.  Run: `mvn clean install`
    This command will download the required dependencies and build the application.
    
**Running the application:**

1.  Set the `JASYPT_PASSWORD` environment variable.
2.  Create a PostgreSQL database named `trial-period`.
3.  Run the application using `mvn spring-boot:run`.

### Additional Information

**Notes:**

Versions to install are in [Digicamp Teams Group]

*Path :* Documents > General > trial-period-api > doc