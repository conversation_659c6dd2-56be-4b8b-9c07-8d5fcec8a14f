#!/bin/bash

# Script pour valider les workflows GitHub Actions localement
# Nécessite: act (https://github.com/nektos/act)

set -e

echo "🔍 Validation des workflows GitHub Actions..."

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si act est installé
if ! command -v act &> /dev/null; then
    log_error "act n'est pas installé. Installez-le avec:"
    echo "  # macOS"
    echo "  brew install act"
    echo "  # Linux"
    echo "  curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash"
    echo "  # Windows"
    echo "  choco install act-cli"
    exit 1
fi

# Vérifier si Docker est en cours d'exécution
if ! docker info &> /dev/null; then
    log_error "Docker n'est pas en cours d'exécution. Démarrez Docker et réessayez."
    exit 1
fi

# Créer un fichier .secrets temporaire pour les tests
create_test_secrets() {
    log_info "Création du fichier .secrets pour les tests..."
    cat > .secrets << EOF
GITHUB_TOKEN=fake_token_for_testing
DOCKER_USERNAME=test_user
DOCKER_PASSWORD=test_password
SONAR_TOKEN=fake_sonar_token
SONAR_ORGANIZATION=test_org
EOF
}

# Nettoyer les fichiers temporaires
cleanup() {
    log_info "Nettoyage des fichiers temporaires..."
    rm -f .secrets
}

# Trap pour nettoyer en cas d'interruption
trap cleanup EXIT

# Créer les secrets de test
create_test_secrets

log_info "Validation de la syntaxe des workflows..."

# Valider chaque workflow
workflows=(
    ".github/workflows/ci-cd.yml"
    ".github/workflows/release.yml"
    ".github/workflows/sonar-analysis.yml"
    ".github/workflows/dependency-check.yml"
    ".github/workflows/pr-checks.yml"
)

for workflow in "${workflows[@]}"; do
    if [ -f "$workflow" ]; then
        log_info "Validation de $workflow..."
        
        # Vérifier la syntaxe YAML
        if command -v yamllint &> /dev/null; then
            yamllint "$workflow" || log_warn "Problèmes de style YAML dans $workflow"
        fi
        
        # Tester avec act (dry-run)
        workflow_name=$(basename "$workflow" .yml)
        log_info "Test dry-run pour $workflow_name..."
        
        case "$workflow_name" in
            "ci-cd")
                act push --dry-run --secret-file .secrets -W "$workflow" || log_warn "Problème potentiel dans $workflow"
                ;;
            "pr-checks")
                act pull_request --dry-run --secret-file .secrets -W "$workflow" || log_warn "Problème potentiel dans $workflow"
                ;;
            "release")
                act workflow_dispatch --dry-run --secret-file .secrets -W "$workflow" || log_warn "Problème potentiel dans $workflow"
                ;;
            *)
                act workflow_dispatch --dry-run --secret-file .secrets -W "$workflow" || log_warn "Problème potentiel dans $workflow"
                ;;
        esac
    else
        log_error "Workflow $workflow non trouvé!"
    fi
done

log_info "Validation des fichiers de configuration..."

# Valider dependabot.yml
if [ -f ".github/dependabot.yml" ]; then
    log_info "Validation de dependabot.yml..."
    if command -v yamllint &> /dev/null; then
        yamllint .github/dependabot.yml || log_warn "Problèmes dans dependabot.yml"
    fi
else
    log_warn "dependabot.yml non trouvé"
fi

# Vérifier les secrets requis
log_info "Vérification des secrets requis..."
required_secrets=(
    "DOCKER_USERNAME"
    "DOCKER_PASSWORD"
    "SONAR_TOKEN"
    "SONAR_ORGANIZATION"
)

echo "Secrets requis pour les workflows:"
for secret in "${required_secrets[@]}"; do
    echo "  - $secret"
done

log_info "✅ Validation terminée!"
echo ""
echo "📝 Prochaines étapes:"
echo "1. Configurez les secrets dans GitHub:"
echo "   Settings > Secrets and variables > Actions"
echo "2. Créez les environnements 'staging' et 'production':"
echo "   Settings > Environments"
echo "3. Configurez SonarCloud si nécessaire"
echo "4. Testez avec un push sur une branche de feature"
