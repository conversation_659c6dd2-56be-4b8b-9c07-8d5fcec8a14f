package com.capgemini.trialperiodapi;

import com.capgemini.trialperiodapi.config.AppConfigProperties;
import com.capgemini.trialperiodapi.model.Admin;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.util.PasswordGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.password.PasswordEncoder;

@SpringBootApplication
@EnableAspectJAutoProxy
@EnableConfigurationProperties(AppConfigProperties.class)
public class TrialPeriodApiApplication {

    @Value("${app.admin-email}")
    private String adminEmail;

    @Value("${app.admin-username}")
    private String adminUsername;

    public static void main(String[] args) {
        SpringApplication.run(TrialPeriodApiApplication.class, args);
    }

    @Bean
    @Profile("dev")
    CommandLineRunner commandLineRunner(
            UserRepository userRepository,
            PasswordEncoder passwordEncoder
    ) {
        return args -> {
            if (userRepository.findUserByUsername(adminUsername).isEmpty()) {
                Admin admin = Admin.builder()
                        .id(1L)
                        .username(adminUsername)
                        .email(adminEmail)
                        .firstname("Firstname")
                        .lastname("Lastname")
                        .password(passwordEncoder.encode(PasswordGenerator.generatePassword()))
                        .isEnabled(true)
                        .build();
                userRepository.save(admin);
            }
        };
    }
}
