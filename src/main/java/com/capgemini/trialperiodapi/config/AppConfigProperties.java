package com.capgemini.trialperiodapi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

@ConfigurationProperties(prefix = "app")
@PropertySource({"classpath:secrets.properties", "classpath:application.properties"})
public record AppConfigProperties(
        String name,
        String apiUrl,
        String mailAddress,
        String mailSupport,
        String adminEmail,
        String adminUsername,
        String frontendActivationUrl,
        String frontendResetPasswordUrl,
        String frontendLoginUrl,
        String emailServerHost,
        String emailServerProtocol,
        String emailServerPort,
        String allowedOrigins,
        String jwtSecretKey,
        String AccessTokenExpiration,
        String RefreshTokenExpiration,
        String resetPasswordTokenExpiration,
        String registrationAllowed
) {
}