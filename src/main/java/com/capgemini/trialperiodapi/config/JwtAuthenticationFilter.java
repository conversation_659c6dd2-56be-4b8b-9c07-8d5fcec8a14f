package com.capgemini.trialperiodapi.config;

import com.capgemini.trialperiodapi.service.auth.IJwtService;
import com.capgemini.trialperiodapi.service.auth.VerificationTokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final IJwtService jwtService;
    private final UserDetailsService userDetailsService;
    private final VerificationTokenService tokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        log.trace("JwtAuthenticationFilter::doFilterInternal Processing authentication for '{}'", request.getRequestURI());

        try {
            // Exclude authentication paths
            if (request.getServletPath() != null && request.getServletPath().contains("/auth")) {
                log.trace("Skipping authentication for '{}'", request.getRequestURI());
                filterChain.doFilter(request, response);
                return;
            }

            log.trace("JwtAuthenticationFilter::doFilterInternal Checking if the JWT exist");

            final String authHeader = request.getHeader("Authorization");
            final String jwt;
            final String username;

            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                log.trace("JwtAuthenticationFilter::doFilterInternal Invalid or missing Authorization header for '{}'", request.getRequestURI());
                filterChain.doFilter(request, response);
                return;
            }

            // Extract 'Bearer ' from bearer token
            jwt = authHeader.substring(7);
            username = jwtService.extractUsername(jwt);

            log.trace("JwtAuthenticationFilter::doFilterInternal Checking if the user isn't authenticated");

            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                log.trace("Fetching user information");
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);

                if (jwtService.isTokenValid(jwt, userDetails) && isTokenValid(jwt)) {

                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                    );
                    authToken.setDetails(
                            new WebAuthenticationDetailsSource().buildDetails(request)
                    );
                    log.trace("JwtAuthenticationFilter::doFilterInternal Update the SecurityContextHolder");

                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    log.debug("User '{}' successfully authenticated", username);

                } else {
                    log.warn("JwtAuthenticationFilter::doFilterInternal Invalid JWT token for user '{}'", username);
                }
            }

        } catch (Exception e) {
            log.error("JwtAuthenticationFilter::doFilterInternal An error occurred during JWT authentication for '{}' '{}';", request.getRequestURI(), e.getMessage());
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "An error occurred during JWT authentication");
            return;
        }

        log.trace("JwtAuthenticationFilter::doFilterInternal Call to the next filter in the chain");
        filterChain.doFilter(request, response);
    }

    private boolean isTokenValid(String jwt) {
        boolean isTokenValid;
        isTokenValid = !tokenService.getVerificationTokenByToken(jwt).isExpired()
                && !tokenService.getVerificationTokenByToken(jwt).isRevoked();
        log.trace("JwtAuthenticationFilter::isTokenValid '{}';", isTokenValid);
        return isTokenValid;
    }
}
