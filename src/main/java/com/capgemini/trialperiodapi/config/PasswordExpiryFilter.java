package com.capgemini.trialperiodapi.config;

import com.capgemini.trialperiodapi.service.auth.PasswordService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.PrintWriter;

@Component
@RequiredArgsConstructor
public class PasswordExpiryFilter implements Filter {
    public static final String API_CHANGE_PASSWORD = "/api/v1/password/change-password";
    private final PasswordService passwordService;
    private final MessageSourceUtil messageSourceUtil;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {}

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Fi<PERSON><PERSON>hain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        boolean isPasswordExpired = passwordService.isPasswordExpired(httpRequest.getUserPrincipal());

        // Allowed paths even if password has expired
        String currentPath = httpRequest.getRequestURI();

        // If the password has expired and the request is not to change the password
        if (isPasswordExpired && !currentPath.equals(API_CHANGE_PASSWORD)) {
            httpResponse.setStatus(419); // Status HTTP pour expiration de mot de passe

            // Send a JSON message
            httpResponse.setContentType("application/json");
            PrintWriter out = httpResponse.getWriter();
            out.print("{\"message\":\"" + messageSourceUtil.getMessage("password.expired") + "\"}");
            out.flush();
            return; // Block request
        }

        chain.doFilter(request, response);  // Continue filter chain if all goes well
    }

    @Override
    public void destroy() {}
}
