package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.UserSortField;
import com.capgemini.trialperiodapi.dto.request.AdminCreateRequestDTO;
import com.capgemini.trialperiodapi.dto.request.AdminRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.service.IAdminService;
import com.capgemini.trialperiodapi.service.IUserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/admins")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final MessageSourceUtil messageSourceUtil;
    private final IAdminService adminService;
    private final IUserService userService;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Page<UserResponseDTO>> getAllAdmins(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") UserSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword, Principal principal) {

        log.debug("AdminController::getAllAdmins started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(
                page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<UserResponseDTO> admins = adminService.getAllAdmins(keyword, pageable, principal);

        log.debug("AdminController::getAllAdmins completed; returned admins '{}';", admins.getSize());

        return new ResponseEntity<>(admins, HttpStatus.OK);
    }

    @GetMapping("/{adminId}")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<UserResponseDTO> getAdminById(@PathVariable Long adminId, Principal principal) {

        log.debug("AdminController::getAdminById started; admin ID '{}';", adminId);

        UserResponseDTO admin = adminService.getAdminDTOById(adminId, principal);

        log.debug("AdminController::getAdminById completed; admin ID '{}';", admin.getId());

        return new ResponseEntity<>(admin, HttpStatus.OK);
    }

    @PostMapping("/validate")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> createAdmin(
            @Valid @RequestBody AdminRequestDTO request) {

        log.debug("AdminController::createAdmin started; username '{}';", request.getUsername());

        userService.validateUser(request.getUsername(), request.getEmail());

        log.debug("AdminController::createAdmin completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("admin.created"));
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> createAdmin(
            @Valid @RequestBody AdminCreateRequestDTO request,
            Principal principal) {

        log.debug("AdminController::createAdmin started; username '{}';", request.getUsername());

        adminService.saveAdmin(request, principal, request.getConnectedUserSessionPassword());

        log.debug("AdminController::createAdmin completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("admin.created"));
    }

    @PutMapping("/{adminId}")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> updateAdmin(
            @PathVariable Long adminId, @Valid @RequestBody AdminRequestDTO request) {

        log.debug("AdminController::updateAdmin started; admin ID '{}';", adminId);

        adminService.updateAdmin(adminId, request);

        log.debug("AdminController::updateAdmin completed; admin ID '{}';", adminId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("admin.updated"));
    }

    @DeleteMapping("/{username}")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> deleteAdmin(
            @PathVariable String username, @RequestBody String connectedUserSessionPassword,
            Principal principal) {

        log.debug("AdminController::deleteAdmin started; username '{}';", username);

        adminService.deleteAdminByUsername(username, connectedUserSessionPassword, principal);

        log.debug("AdminController::deleteAdmin completed; username '{}';", username);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("admin.deleted"));
    }
}