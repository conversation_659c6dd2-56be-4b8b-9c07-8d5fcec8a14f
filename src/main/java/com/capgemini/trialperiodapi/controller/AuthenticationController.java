package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.request.AuthenticationRequestDTO;
import com.capgemini.trialperiodapi.dto.request.RegisterRequestDTO;
import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.service.auth.IAuthenticationService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthenticationController {

    private final IAuthenticationService authenticationService;
    private final MessageSourceUtil messageSourceUtil;

    @PostMapping("/authenticate")
    public ResponseEntity<AuthenticationResponseDTO> authenticate(
            @Valid @RequestBody AuthenticationRequestDTO request) {

        log.debug("AuthenticationController::authenticate started; username '{}';", request.getUsername());

        AuthenticationResponseDTO response = authenticationService.authenticate(request);

        log.debug("AuthenticationController::authenticate completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().body(response);
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<Void> refreshToken(HttpServletRequest request,
                                             HttpServletResponse response) {

        log.debug("AuthenticationController::refreshToken started; session ID '{}';", request.getSession().getId());

        authenticationService.refreshToken(request, response);

        log.debug("AuthenticationController::refreshToken completed; session ID '{}';", request.getSession().getId());

        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @ConditionalOnExpression("${app.registration-allowed}")
    @PostMapping("/register")
    public ResponseEntity<AuthenticationResponseDTO> register(
            @Valid @RequestBody RegisterRequestDTO request) {

        log.debug("AuthenticationController::register started; request '{}';", request.getUsername());

        AuthenticationResponseDTO response = authenticationService.register(request);

        log.debug("AuthenticationController::register completed; request '{}';", request.getUsername());

        return ResponseEntity.ok().body(response);
    }

    @ConditionalOnExpression("${app.registration-allowed}")
    @PostMapping("/register/verify-email")
    public ResponseEntity<Map<String, String>> verifyEmail(@RequestParam String code) {

        log.debug("AuthenticationController::verifyEmail started;");

        authenticationService.verifyEmail(code);

        log.debug("AuthenticationController::verifyEmail completed;");

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("user.email-verified"));
    }
}
