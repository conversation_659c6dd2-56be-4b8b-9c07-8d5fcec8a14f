package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.CollaboratorCriteria;
import com.capgemini.trialperiodapi.dto.CollaboratorSortField;
import com.capgemini.trialperiodapi.dto.request.CollaboratorRequestDTO;
import com.capgemini.trialperiodapi.dto.response.CollaboratorResponseDTO;
import com.capgemini.trialperiodapi.dto.response.TrialPeriodResponseDTO;
import com.capgemini.trialperiodapi.service.ICollaboratorService;
import com.capgemini.trialperiodapi.service.ITrialPeriodService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.Principal;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/api/v1/collaborators")
@RequiredArgsConstructor
@Transactional
@Slf4j
public class CollaboratorController {
    private final ICollaboratorService collaboratorService;
    private final ITrialPeriodService trialPeriodService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<CollaboratorResponseDTO>> getAllCollaborators(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "LAST_UPDATE_ON") CollaboratorSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String globalGrade,
            @RequestParam(required = false) String localGrade,
            @RequestParam(required = false) Set<String> trialPeriodStatus,
            @RequestParam(required = false) String assignmentStatus,
            @RequestParam(required = false) String status,
            Principal principal) {

        log.debug("CollaboratorController::getAllCollaborators started; " +
                        "page '{}', itemsPerPage '{}', sortField '{}', " +
                        "sortDirection '{}', keyword '{}', globalGrade '{}', localGrade '{}', " +
                        "status '{}', assignmentStatus '{}', collaboratorStatus '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword,
                globalGrade, localGrade, trialPeriodStatus, assignmentStatus, status);

        Pageable pageable = PageRequest.of(page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        CollaboratorCriteria criteria = new CollaboratorCriteria(
                keyword, globalGrade, localGrade, trialPeriodStatus, assignmentStatus, status);
        Page<CollaboratorResponseDTO> collaborators = collaboratorService
                .getAllCollaborators(criteria, principal, pageable);

        log.debug("CollaboratorController::getAllCollaborators completed; returned collaborators '{}';",
                collaborators.getSize());

        return ResponseEntity.ok().body(collaborators);
    }

    @GetMapping("/{collaboratorId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<CollaboratorResponseDTO> getCollaboratorById(
            @PathVariable Long collaboratorId, Principal principal) {

        log.debug("CollaboratorController::getCollaboratorById started; collaborator ID '{}';", collaboratorId);

        CollaboratorResponseDTO collaborator = collaboratorService
                .getCollaboratorDTOById(collaboratorId, principal);

        log.debug("CollaboratorController::getCollaboratorById completed; collaborator GGID '{}';",
                collaborator.getGgid());

        return ResponseEntity.ok().body(collaborator);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Map<String, String>> createCollaborator(
            @Valid @RequestBody CollaboratorRequestDTO request) {

        log.debug("CollaboratorController::createCollaborator started; collaborator GGID '{}';", request.getGgid());

        collaboratorService.saveCollaborator(request);

        log.debug("CollaboratorController::createCollaborator completed; collaborator GGID '{}';", request.getGgid());

        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(messageSourceUtil.buildResponse("collaborator.created"));
    }

    @PutMapping("/{collaboratorId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Map<String, String>> updateCollaborator(
            @PathVariable Long collaboratorId,
            @Valid @RequestBody CollaboratorRequestDTO request, Principal principal) {

        log.debug("CollaboratorController::updateCollaborator started; collaborator ID '{}';", collaboratorId);

        collaboratorService.updateCollaborator(collaboratorId, request, principal);

        log.debug("CollaboratorController::updateCollaborator completed; collaborator ID '{}';", collaboratorId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("collaborator.updated"));
    }

    @DeleteMapping("/{collaboratorGgid}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Map<String, String>> deleteCollaborator(
            @PathVariable String collaboratorGgid, Principal principal) {

        log.debug("CollaboratorController::deleteCollaborator started; collaborator ID '{}';", collaboratorGgid);

        collaboratorService.deleteCollaboratorByGgid(collaboratorGgid, principal);

        log.debug("CollaboratorController::deleteCollaborator completed; collaborator ID '{}';", collaboratorGgid);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("collaborator.deleted"));
    }

    @PostMapping(value = "/upload", consumes = {"multipart/form-data"})
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> uploadCollaborators(
            @RequestPart("file") MultipartFile file) {

        log.debug("CollaboratorController::uploadCollaborators started; '{}';", file.getOriginalFilename());

        Integer uploadedCollaboratorCount = collaboratorService.uploadCollaborators(file);

        log.debug("CollaboratorController::uploadCollaborators completed; uploaded collaborators '{}';", uploadedCollaboratorCount);

        return ResponseEntity.status(HttpStatus.OK)
                .body(messageSourceUtil.buildResponseWithObject(
                        "collaborator.imported", uploadedCollaboratorCount)
                );
    }

    @GetMapping("/{collaboratorId}/trial-period")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<TrialPeriodResponseDTO> getTrialPeriod(
            @PathVariable Long collaboratorId, Principal principal) {

        log.debug("CollaboratorController::getTrialPeriod started; collaborator ID '{}';", collaboratorId);

        TrialPeriodResponseDTO trialPeriod =
                trialPeriodService.getTrialPeriodByCollaboratorId(collaboratorId, principal);

        log.debug("CollaboratorController::getTrialPeriod completed; trial period ID '{}';", trialPeriod.getId());

        return ResponseEntity.ok().body(trialPeriod);
    }

    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile() throws IOException {

        log.debug("CollaboratorController::downloadFile started;");

        ByteArrayInputStream byteArrayInputStream = collaboratorService.convertCollaboratorsToCSV();

        HttpHeaders headers = new HttpHeaders();
        headers.add(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=projects.csv"
        );

        log.debug("CollaboratorController::downloadFile completed;");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("text/csv"))
                .body(new InputStreamResource(byteArrayInputStream));
    }
}
