package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.UserSortField;
import com.capgemini.trialperiodapi.dto.request.HrCreateRequestDTO;
import com.capgemini.trialperiodapi.dto.request.HrRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.service.IHrService;
import com.capgemini.trialperiodapi.service.IUserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/hrs")
@RequiredArgsConstructor
@Slf4j
public class HrController {
    private final IHrService hrService;
    private final IUserService userService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<UserResponseDTO>> searchHrsByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("HrController::searchHrsByKeyword started; keyword '{}';", keyword);

        List<UserResponseDTO> hrs = hrService.searchHrsByKeyword(keyword, principal);

        log.debug("HrController::searchHrsByKeyword completed; returned results '{}';", hrs.size());

        return ResponseEntity.ok().body(hrs);
    }

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<UserResponseDTO>> getHrs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") UserSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword, Principal principal) {

        log.debug("HrController::getHrs started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(
                page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<UserResponseDTO> hrs = hrService.getAllHrs(keyword, pageable, principal);

        log.debug("HrController::getHrs completed; returned HR(s) '{}';", hrs.getSize());

        return new ResponseEntity<>(hrs, HttpStatus.OK);
    }

    @GetMapping("{hrId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<UserResponseDTO> getHrById(
            @PathVariable Long hrId, Principal principal) {

        log.debug("HrController::getHrById started; hrId '{}';", hrId);

        UserResponseDTO hr = hrService.getHrDtoById(hrId, principal);

        log.debug("HrController::getHrById completed; hrId  '{}';", hr.getId());

        return ResponseEntity.ok().body(hr);
    }

    @PostMapping("/validate")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> validateHr(
            @Valid @RequestBody HrRequestDTO request) {

        log.debug("HrController::validateHr started; hrUsername '{}';", request.getUsername());

        userService.validateUser(request.getUsername(), request.getEmail());

        log.debug("HrController::getHrById completed;");

        return ResponseEntity.ok().build();
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createHr(
            @Valid @RequestBody HrCreateRequestDTO request,
            Principal principal) {

        log.debug("HrController::createHr started; hrEmail '{}';", request.getUsername());

        hrService.saveHr(request, principal, request.getConnectedUserSessionPassword());

        log.debug("HrController::createHr completed; hrEmail '{}';", request.getUsername());

        return ResponseEntity.status(HttpStatus.CREATED).body(messageSourceUtil.buildResponse("hr.created"));
    }

    @PutMapping("{hrId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updateHr(
            @Valid @RequestBody HrRequestDTO request,
            @PathVariable Long hrId, Principal principal) {

        log.debug("HrController::updateHr started; hrId '{}';", hrId);

        hrService.updateHr(hrId, request, principal);

        log.debug("HrController::updateHr completed; hrId '{}';", hrId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("hr.updated"));
    }

    @DeleteMapping("{hrEmail}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deleteHr(
            @PathVariable String hrEmail,
            @RequestBody String connectedUserSessionPassword,
            Principal principal) {

        log.debug("HrController::deleteHr started; hrEmail '{}';", hrEmail);

        hrService.deleteHrByUsername(hrEmail, principal, connectedUserSessionPassword);

        log.debug("HrController::deleteHr completed; hrEmail '{}';", hrEmail);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("hr.deleted"));
    }
}
