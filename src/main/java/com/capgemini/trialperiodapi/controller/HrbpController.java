package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.UserSortField;
import com.capgemini.trialperiodapi.dto.request.HrbpCreateRequestDTO;
import com.capgemini.trialperiodapi.dto.request.HrbpRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.service.IHrbpService;
import com.capgemini.trialperiodapi.service.IUserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/hrbps")
@RequiredArgsConstructor
@Slf4j
public class HrbpController {
    private final MessageSourceUtil messageSourceUtil;
    private final IHrbpService hrbpService;
    private final IUserService userService;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<UserResponseDTO>> getAllHrbps(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") UserSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword, Principal principal) {

        log.debug("HrbpController::getAllHrbps started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(
                page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<UserResponseDTO> users = hrbpService.getAllHrbps(keyword, pageable, principal);

        log.debug("HrbpController::getAllHrbps completed; returned users '{}';", users.getSize());

        return new ResponseEntity<>(users, HttpStatus.OK);
    }

    @GetMapping("/{userId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<UserResponseDTO> getHrbpById(@PathVariable Long userId, Principal principal) {

        log.debug("HrbpController::getHrbpById started; user ID '{}';", userId);

        UserResponseDTO user = hrbpService.getHrbpDTOById(userId, principal);

        log.debug("HrbpController::getHrbpById completed; user ID '{}';", user.getId());

        return new ResponseEntity<>(user, HttpStatus.OK);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<UserResponseDTO>> searchHrbpsByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("HrbpController::searchHrbpsByKeyword started; keyword '{}';", keyword);

        List<UserResponseDTO> users =
                hrbpService.searchHrbpsByKeyword(keyword, principal);

        log.debug("HrbpController::searchHrbpsByKeyword completed; returned users '{}';", users.size());

        return ResponseEntity.ok().body(users);
    }

    @PostMapping("/validate")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> validateHrbp(
            @Valid @RequestBody HrbpRequestDTO request) {

        log.debug("HrbpController::validateHrbp started; username '{}';", request.getUsername());

        userService.validateUser(request.getUsername(), request.getEmail());

        log.debug("HrbpController::validateHrbp completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().build();
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> createHrbp(
            @Valid @RequestBody HrbpCreateRequestDTO request,
            Principal principal) {

        log.debug("HrbpController::createHrbp started; username '{}';", request.getUsername());

        hrbpService.saveHrbp(request, principal, request.getConnectedUserSessionPassword());

        log.debug("HrbpController::createHrbp completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("hrbp.created"));
    }

    @PutMapping("/{userId}")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> updateHrbp(
            @PathVariable Long userId, @Valid @RequestBody HrbpRequestDTO request) {

        log.debug("HrbpController::updateHrbp started; user ID '{}';", userId);

        hrbpService.updateHrbp(userId, request);

        log.debug("HrbpController::updateHrbp completed; user ID '{}';", userId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("hrbp.updated"));
    }

    @DeleteMapping("/{username}")
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> deleteHrbp(
            @PathVariable String username,
            @RequestBody String connectedUserSessionPassword,
            Principal principal
    ) {

        log.debug("HrbpController::deleteHrbp started; username '{}';", username);

        hrbpService.deleteHrbpByUsername(username, connectedUserSessionPassword, principal);

        log.debug("HrbpController::deleteHrbp completed; username '{}';", username);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("hrbp.deleted"));
    }
}