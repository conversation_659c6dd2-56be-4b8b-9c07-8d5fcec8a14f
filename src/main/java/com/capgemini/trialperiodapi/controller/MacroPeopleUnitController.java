package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.request.MacroPeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.MacroPeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import com.capgemini.trialperiodapi.mapper.MacroPeopleUnitMapper;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.service.IMacroPeopleUnitService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/macro-people-units")
@RequiredArgsConstructor
@Slf4j
public class MacroPeopleUnitController {
    private final IMacroPeopleUnitService macroPeopleUnitService;
    private final MacroPeopleUnitMapper macroPeopleUnitMapper;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping("/{macroPeopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<MacroPeopleUnitResponseDTO> getMacroPeopleUnitByName(
            @PathVariable String macroPeopleUnitName, Principal principal) {

        log.debug("MacroPeopleUnitController::getMacroPeopleUnitByName started; macro PU Name '{}';", macroPeopleUnitName);

        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService
                .getMacroPeopleUnitByName(macroPeopleUnitName, principal);

        log.debug("MacroPeopleUnitController::getMacroPeopleUnitByName completed; macro PU Name '{}';", macroPeopleUnit.getName());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(macroPeopleUnitMapper.toMacroPeopleUnitDTO(macroPeopleUnit));
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<String>> searchMacroPeopleUnitNamesByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("MacroPeopleUnitController::searchMacroPeopleUnitsNameByKeyword started; keyword '{}';", keyword);

        List<String> macroPeopleUnitNames =
                macroPeopleUnitService.searchMacroPeopleUnitNamesByKeyword(keyword, principal);

        log.debug("MacroPeopleUnitController::searchMacroPeopleUnitsNameByKeyword completed; returned results '{}';", macroPeopleUnitNames.size());

        return ResponseEntity.ok().body(macroPeopleUnitNames);
    }

    @GetMapping("/{macroPeopleUnitName}/spocs")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<SpocsHrPumResponseDTO> getPeopleUnitByMacroPeopleUnitName(
            @PathVariable String macroPeopleUnitName) {

        log.debug("PeopleUnitController::getPeopleUnitByMacroPeopleUnitName started; macro PU name '{}';", macroPeopleUnitName);

        SpocsHrPumResponseDTO spocsHrPumResponseDTO =
                macroPeopleUnitService.getSpocsHrPumByMacroPeopleUnitName(macroPeopleUnitName);

        log.debug("PeopleUnitController::getPeopleUnitByMacroPeopleUnitName completed; macro PU name '{}';", macroPeopleUnitName);

        return ResponseEntity.ok().body(spocsHrPumResponseDTO);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createMacroPeopleUnit(
            @Valid @RequestBody MacroPeopleUnitRequestDTO request) {

        log.debug("MacroPeopleUnitController::createMacroPeopleUnit started; macro PU name '{}';", request.getName());

        macroPeopleUnitService.saveMacroPeopleUnit(request);

        log.debug("MacroPeopleUnitController::createMacroPeopleUnit completed; macro PU name '{}';", request.getName());

        return ResponseEntity.status(HttpStatus.CREATED).body(
                messageSourceUtil.buildResponse("macro-people-unit.created")
        );
    }

    @PutMapping("{macroPeopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updateMacroPeopleUnit(
            @PathVariable String macroPeopleUnitName,
            @Valid @RequestBody MacroPeopleUnitRequestDTO request, Principal principal) {

        log.debug("MacroPeopleUnitController::updateMacroPeopleUnit started; macro PU ID '{}';", macroPeopleUnitName);

        macroPeopleUnitService.updateMacroPeopleUnit(macroPeopleUnitName, request, principal);

        log.debug("MacroPeopleUnitController::updateMacroPeopleUnit completed; macro PU ID '{}';", macroPeopleUnitName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("macro-people-unit.updated"));
    }

    @DeleteMapping("{macroPeopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deleteMacroPeopleUnit(
            @PathVariable String macroPeopleUnitName, Principal principal) {

        log.debug("MacroPeopleUnitController::deleteMacroPeopleUnit started; macro PU ID '{}';", macroPeopleUnitName);

        macroPeopleUnitService.deleteMacroPeopleUnitByName(macroPeopleUnitName, principal);

        log.debug("MacroPeopleUnitController::deleteMacroPeopleUnit completed; macro PU ID '{}';", macroPeopleUnitName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("macro-people-unit.deleted"));
    }
}
