package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.request.ChangePasswordRequestDTO;
import com.capgemini.trialperiodapi.dto.request.ResetPasswordRequestDTO;
import com.capgemini.trialperiodapi.service.auth.IPasswordService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/password")
@RequiredArgsConstructor
@Slf4j
public class PasswordController {

    private final MessageSourceUtil messageSourceUtil;
    private final IPasswordService passwordService;

    @PatchMapping("/change-password")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Map<String, String>> changePassword(
            @Valid @RequestBody ChangePasswordRequestDTO passwordRequest, Principal principal) {

        log.debug("PasswordController::changePassword started; principal name '{}';", principal.getName());

        passwordService.changePassword(passwordRequest, principal);

        log.debug("PasswordController::changePassword completed; principal name '{}';", principal.getName());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("password.updated"));
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<Map<String, String>> forgotPassword(
            @RequestParam String email,
            @RequestBody String connectedUserSessionPassword) {

        log.debug("PasswordController::forgotPassword started; email '{}';", email);

        passwordService.forgotPassword(email, connectedUserSessionPassword);

        log.debug("PasswordController::forgotPassword completed; email '{}';", email);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("password.verification-link.sent"));
    }

    @PutMapping("/reset-password")
    public ResponseEntity<Map<String, String>> resetPassword(
            @Valid @RequestBody ResetPasswordRequestDTO passwordRequest,
            @RequestParam String code) {

        log.debug("PasswordController::resetPassword started;");

        passwordService.resetPassword(passwordRequest, code);

        log.debug("PasswordController::resetPassword completed;");

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("password.reset"));
    }

    @GetMapping("/is-code-valid")
    public ResponseEntity<Boolean> isCodeValid(@RequestParam String code) {

        log.debug("PasswordController::resetPassword started; code '{}';", code);

        boolean isTokenValid = passwordService.getValidPasswordResetTokenByCode(code) != null;

        log.debug("PasswordController::resetPassword completed; isTokenValid '{}';", isTokenValid);

        return ResponseEntity.ok().body(isTokenValid);
    }

    @GetMapping("/check-expiry")
    public ResponseEntity<Boolean> checkPasswordExpiringSoon(Principal principal) {

        log.debug("PasswordController::checkPasswordExpiringSoon started; connected user '{}';", principal.getName());

        boolean isExpired = passwordService.isPasswordExpiringSoon(principal);

        log.debug("PasswordController::checkPasswordExpiringSoon completed; connected user '{}';", principal.getName());

        return ResponseEntity.ok().body(isExpired);
    }
}
