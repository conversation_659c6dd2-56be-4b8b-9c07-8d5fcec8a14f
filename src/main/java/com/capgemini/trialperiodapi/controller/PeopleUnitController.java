package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.request.PeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.service.IPeopleUnitService;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/people-units")
@RequiredArgsConstructor
@Slf4j
public class PeopleUnitController {
    private final IPeopleUnitService peopleUnitService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<SpocsHrPumResponseDTO>> getSpocsHrPum(Principal principal) {

        log.debug("PeopleUnitController::getAllPeopleUnits started;");

        List<SpocsHrPumResponseDTO> peopleUnits = peopleUnitService.getSpocsHrPum(principal);

        log.debug("PeopleUnitController::getAllPeopleUnits completed; returned PU(s) '{}';", peopleUnits.size());

        return new ResponseEntity<>(peopleUnits, HttpStatus.OK);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<String>> searchPeopleUnitsNameByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("PeopleUnitController::searchPeopleUnitsNameByKeyword started; keyword '{}';", keyword);

        List<String> peopleUnitNames =
                peopleUnitService.searchPeopleUnitNamesByKeyword(keyword, principal);

        log.debug("PeopleUnitController::searchPeopleUnitsNameByKeyword completed: returned results '{}';", peopleUnitNames.size());

        return ResponseEntity.ok().body(peopleUnitNames);
    }

    @PostMapping(value = "/upload", consumes = {"multipart/form-data"})
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> uploadPeopleUnits(
            @RequestPart("file") MultipartFile file,
            @RequestPart("connectedUserSessionPassword") String connectedUserSessionPassword,
            Principal principal) {

        log.debug("PeopleUnitController::uploadPeopleUnits started; file '{}';", file.getOriginalFilename());

        Integer uploadedPeopleUnitCount = peopleUnitService.uploadPeopleUnits(file, principal, connectedUserSessionPassword);

        log.debug("PeopleUnitController::uploadPeopleUnits completed '{}';", uploadedPeopleUnitCount);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponseWithObject(
                "people-unit.imported", uploadedPeopleUnitCount));
    }

    @GetMapping("/{peopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<PeopleUnitResponseDTO> getPeopleUnitByName(
            @PathVariable String peopleUnitName, Principal principal) {

        log.debug("PeopleUnitController::getPeopleUnitByName started; PU ID '{}';", peopleUnitName);

        PeopleUnitResponseDTO peopleUnit =
                peopleUnitService.getPeopleUnitDTOByName(peopleUnitName, principal);

        log.debug("PeopleUnitController::getPeopleUnitByName completed; PU ID '{}';", peopleUnit.getId());

        return ResponseEntity.ok().body(peopleUnit);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN','HRBP')")
    public ResponseEntity<Map<String, String>> createNewPeopleUnit(
            @Valid @RequestBody PeopleUnitRequestDTO request) {

        log.debug("PeopleUnitController::createNewPeopleUnit started; PU name '{}';", request.getName());

        peopleUnitService.savePeopleUnit(request);

        log.debug("PeopleUnitController::createNewPeopleUnit completed; PU name '{}';", request.getName());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(messageSourceUtil.buildResponse("people-unit.created"));
    }

    @PutMapping("{peopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN','HRBP')")
    public ResponseEntity<Map<String, String>> updatePeopleUnit(
            @PathVariable String peopleUnitName,
            @Valid @RequestBody PeopleUnitRequestDTO request,
            Principal principal) {

        log.debug("PeopleUnitController::updatePeopleUnit started; PU ID '{}';", peopleUnitName);

        peopleUnitService.updatePeopleUnit(peopleUnitName, request, principal);

        log.debug("PeopleUnitController::updatePeopleUnit completed; PU ID '{}';", peopleUnitName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("people-unit.updated"));
    }

    @DeleteMapping("{peopleUnitName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deletePeopleUnit(
            @PathVariable String peopleUnitName,
            Principal principal) {

        log.debug("PeopleUnitController::deletePeopleUnit started; PU name '{}';", peopleUnitName);

        peopleUnitService.deletePeopleUnitByName(peopleUnitName, principal);

        log.debug("PeopleUnitController::deletePeopleUnit completed; PU name '{}';", peopleUnitName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("people-unit.deleted"));
    }

    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile() throws IOException {

        log.debug("PeopleUnitController::downloadFile started;");

        ByteArrayInputStream byteArrayInputStream = peopleUnitService.convertPeopleUnitsToCSV();

        HttpHeaders headers = new HttpHeaders();
        headers.add(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=SpocsHR-PUM.csv"
        );

        log.debug("PeopleUnitController::downloadFile completed;");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("text/csv"))
                .body(new InputStreamResource(byteArrayInputStream));
    }
}