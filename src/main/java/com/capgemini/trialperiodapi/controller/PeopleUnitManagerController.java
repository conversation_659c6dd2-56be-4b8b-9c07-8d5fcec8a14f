package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.EmployeeSortField;
import com.capgemini.trialperiodapi.dto.request.PeopleUnitManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.capgemini.trialperiodapi.service.IPeopleUnitManagerService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/pums")
@RequiredArgsConstructor
@Slf4j
public class PeopleUnitManagerController {
    private final IPeopleUnitManagerService peopleUnitManagerService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<PeopleUnitManagerResponseDTO>> getPums(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") EmployeeSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword,
            Principal principal) {

        log.debug("PeopleUnitManagerController::getPums started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(
                page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<PeopleUnitManagerResponseDTO> pums =
                peopleUnitManagerService.getPums(keyword, pageable, principal);

        log.debug("PeopleUnitManagerController::getPums completed; returned PUM(s) '{}';", pums.getSize());

        return new ResponseEntity<>(pums, HttpStatus.OK);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<PeopleUnitManagerResponseDTO>> searchPumsByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("PeopleUnitManagerController::searchPumsByKeyword started; keyword '{}';", keyword);

        List<PeopleUnitManagerResponseDTO> pums =
                peopleUnitManagerService.searchPumsByKeyword(keyword, principal);

        log.debug("PeopleUnitManagerController::searchPumsByKeyword completed; returned PUMs '{}';", pums.size());

        return ResponseEntity.ok().body(pums);
    }

    @GetMapping("/{pumId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<PeopleUnitManagerResponseDTO> getPumById(
            @PathVariable Long pumId, Principal principal) {

        log.debug("PeopleUnitManagerController::getPumById request pumID '{}';", pumId);

        PeopleUnitManagerResponseDTO pumDTO =
                peopleUnitManagerService.getPumDTOById(pumId, principal);

        log.debug("PeopleUnitManagerController::getPumById response '{}';", pumDTO);

        return ResponseEntity.ok().body(pumDTO);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createPum(
            @Valid @RequestBody PeopleUnitManagerRequestDTO request) {

        log.debug("PeopleUnitManagerController::createPum started; pum lastname '{}';", request.getLastname());

        peopleUnitManagerService.savePeopleUnitManager(request);

        log.debug("PeopleUnitManagerController::createPum completed; pum lastname '{}';", request.getLastname());

        return ResponseEntity.status(HttpStatus.CREATED).body(messageSourceUtil.buildResponse("pum.created"));
    }

    @PutMapping("/{pumId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updatePum(
            @PathVariable Long pumId,
            @Valid @RequestBody PeopleUnitManagerRequestDTO request,
            Principal principal) {

        log.debug("PeopleUnitManagerController::updatePum started; pumId '{}';", pumId);

        peopleUnitManagerService.updatePum(pumId, request, principal);

        log.debug("PeopleUnitManagerController::updatePum completed; pumId '{}';", pumId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("pum.updated"));
    }

    @DeleteMapping("/{pumEmail}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deletePumByEmail(
            @PathVariable String pumEmail,
            Principal principal) {

        log.debug("PeopleUnitManagerController::deletePumByEmail started; pumEmail '{}';", pumEmail);

        peopleUnitManagerService.deletePumByEmail(pumEmail, principal);

        log.debug("PeopleUnitManagerController::deletePumByEmail started; pumEmail '{}';", pumEmail);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("pum.deleted"));
    }
}