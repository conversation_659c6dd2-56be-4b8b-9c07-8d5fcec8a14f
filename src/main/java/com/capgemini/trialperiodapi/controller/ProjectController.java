package com.capgemini.trialperiodapi.controller;


import com.capgemini.trialperiodapi.dto.ProjectSortField;
import com.capgemini.trialperiodapi.dto.request.ProjectRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectResponseDTO;
import com.capgemini.trialperiodapi.service.IProjectService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/projects")
@RequiredArgsConstructor
@Slf4j
public class ProjectController {

    private final IProjectService projectService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<ProjectResponseDTO>> getProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") ProjectSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword) {

        log.debug("ProjectController::getProjects started; " +
                        "page '{}', itemsPerPage '{}', sortField '{}', " +
                        "sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest
                .of(page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<ProjectResponseDTO> projects = projectService.getAllProjects(keyword, pageable);

        log.debug("ProjectController::getProjects completed; returned project(s) '{}';", projects.getSize());

        return ResponseEntity.ok().body(projects);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<String>> searchProjectNames(@RequestParam String keyword) {

        log.debug("ProjectController::searchProjectNames request keyword '{}';", keyword);

        List<String> projectNames = projectService.searchProjectNamesByKeyword(keyword);

        log.debug("ProjectController::searchProjectNames completed; returned project(s) '{}';", projectNames.size());

        return ResponseEntity.ok().body(projectNames);
    }

    @PostMapping(value = "/upload", consumes = {"multipart/form-data"})
    @PreAuthorize("hasAnyRole('ADMIN')")
    public ResponseEntity<Map<String, String>> uploadProjects(
            @RequestPart("file") MultipartFile file) {

        log.debug("ProjectController::uploadProjects started; file '{}';", file.getOriginalFilename());

        Integer uploadedProjectCount = projectService.uploadProjects(file);

        log.debug("ProjectController::uploadProjects completed; uploaded project(s) '{}';", uploadedProjectCount);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponseWithObject(
                        "project.imported", uploadedProjectCount));
    }

    @GetMapping("{projectName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<ProjectResponseDTO> getProjectById(
            @PathVariable String projectName) {

        log.debug("ProjectController::getProjectById started; project ID '{}';", projectName);

        ProjectResponseDTO projectDTO = projectService.getProjectDTOByName(projectName);

        log.debug("ProjectController::getProjectById completed; project name '{}';", projectDTO.getName());

        return ResponseEntity.ok().body(projectDTO);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createNewProject(
            @Valid @RequestBody ProjectRequestDTO request) {

        log.debug("ProjectController::createNewProject started; project name '{}';", request.getName());

        projectService.saveProject(request);

        log.debug("ProjectController::createNewProject completed; project name '{}';", request.getName());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(messageSourceUtil.buildResponse("project.created"));
    }

    @PutMapping("{projectName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updateProject(
            @PathVariable String projectName,
            @Valid @RequestBody ProjectRequestDTO request) {

        log.debug("ProjectController::updateProject started; project ID '{}';", projectName);

        projectService.updateProject(projectName, request);

        log.debug("ProjectController::updateProject completed; project ID '{}';", projectName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("project.updated"));
    }

    @DeleteMapping("{projectName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deleteProject(
            @PathVariable String projectName) {

        log.debug("ProjectController::deleteProject started; project ID '{}';", projectName);

        projectService.deleteProjectByProjectName(projectName);

        log.debug("ProjectController::deleteProject completed; project ID '{}';", projectName);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("project.deleted"));
    }

    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile() throws IOException {

        log.debug("ProjectController::downloadFile started;");

        ByteArrayInputStream byteArrayInputStream = projectService.convertProjectsToCSV();

        HttpHeaders headers = new HttpHeaders();
        headers.add(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=projects.csv"
        );

        log.debug("ProjectController::downloadFile completed;");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("text/csv"))
                .body(new InputStreamResource(byteArrayInputStream));
    }
}