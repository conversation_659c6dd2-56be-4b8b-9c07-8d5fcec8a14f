package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.EmployeeSortField;
import com.capgemini.trialperiodapi.dto.request.ProjectManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import com.capgemini.trialperiodapi.service.IProjectManagerService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/project-managers")
@RequiredArgsConstructor
@Slf4j
public class ProjectManagerController {
    private final IProjectManagerService projectManagerService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<ProjectManagerResponseDTO>> getProjectManagers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") EmployeeSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword) {

        log.debug("ProjectManagerController::getProjectManagers started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<ProjectManagerResponseDTO> projectManagers = projectManagerService.filterProjectManagers(keyword, pageable);

        log.debug("ProjectManagerController::getProjectManagers completed; returned project manager(s) '{}';",
                projectManagers.getSize());

        return new ResponseEntity<>(projectManagers, HttpStatus.OK);
    }

    @GetMapping("{projectManagerId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<ProjectManagerResponseDTO> getProjectManagerById(
            @PathVariable Long projectManagerId) {

        log.debug("ProjectManagerController::getProjectManagerById started; project manager ID '{}';", projectManagerId);

        ProjectManagerResponseDTO projectManagerDTO =
                projectManagerService.getProjectManagerDTOById(projectManagerId);

        log.debug("ProjectManagerController::getProjectManagerById completed; project manager ID '{}';", projectManagerDTO.getId());

        return ResponseEntity.ok().body(projectManagerDTO);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<ProjectManagerResponseDTO>> searchProjectManagersByKeyword(
            @RequestParam String keyword) {

        log.debug("ProjectManagerController::searchProjectManagersByKeyword started; keyword '{}';", keyword);

        List<ProjectManagerResponseDTO> projectManagers = projectManagerService.searchProjectManagersByKeyword(keyword);

        log.debug("ProjectManagerController::searchProjectManagersByKeyword completed; returned project manager(s) '{}';", projectManagers.size());

        return ResponseEntity.ok().body(projectManagers);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createProjectManager(
            @Valid @RequestBody ProjectManagerRequestDTO request) {

        log.debug("ProjectManagerController::createProjectManager started; project manager lastname '{}';", request.getLastname());

        projectManagerService.saveProjectManager(request);

        log.debug("ProjectManagerController::createProjectManager completed; project manager lastname '{}';", request.getLastname());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("project-manager.created"));
    }

    @PutMapping("{projectManagerId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updateProjectManager(
            @Valid @RequestBody ProjectManagerRequestDTO request,
            @PathVariable Long projectManagerId) {

        log.debug("ProjectManagerController::updateProjectManager started; project manager ID '{}';", projectManagerId);

        projectManagerService.updateProjectManager(projectManagerId, request);

        log.debug("ProjectManagerController::updateProjectManager completed; project manager ID '{}';", projectManagerId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("project-manager.updated"));
    }

    @DeleteMapping("{projectManagerEmail}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deleteProjectManager(
            @PathVariable String projectManagerEmail) {

        log.debug("ProjectManagerController::deleteProjectManager started; project manager email '{}';", projectManagerEmail);

        projectManagerService.deleteProjectManagerByEmail(projectManagerEmail);

        log.debug("ProjectManagerController::deleteProjectManager completed; project manager email '{}';", projectManagerEmail);

        return ResponseEntity.ok().body(
                messageSourceUtil.buildResponse("project-manager.deleted")
        );
    }
}