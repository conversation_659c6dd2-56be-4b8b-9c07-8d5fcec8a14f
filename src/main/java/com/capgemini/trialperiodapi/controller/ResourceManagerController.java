package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.EmployeeSortField;
import com.capgemini.trialperiodapi.dto.request.ResourceManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ResourceManagerResponseDTO;
import com.capgemini.trialperiodapi.service.IResourceManagerService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/resource-managers")
@RequiredArgsConstructor
@Slf4j
public class ResourceManagerController {

    private final IResourceManagerService resourceManagerService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<ResourceManagerResponseDTO>> searchResourceManagersByKeyword(
            @RequestParam String keyword, Principal principal) {

        log.debug("ResourceManagerController::searchResourceManagersByKeyword started; keyword '{}';", keyword);

        List<ResourceManagerResponseDTO> resourceManagers =
                resourceManagerService.filterResourceManagers(keyword, principal);

        log.debug("ResourceManagerController::searchResourceManagersByKeyword completed; returned resource manager(s) '{}';", resourceManagers.size());

        return ResponseEntity.ok().body(resourceManagers);
    }

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Page<ResourceManagerResponseDTO>> getResourceManagers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int itemsPerPage,
            @RequestParam(defaultValue = "CREATED_ON") EmployeeSortField sortField,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(required = false) String keyword, Principal principal) {

        log.debug("ResourceManagerController::getResourceManagers started; page '{}', itemsPerPage '{}', " +
                        "sortField '{}', sortDirection '{}', keyword '{}';",
                page, itemsPerPage, sortField, sortDirection, keyword);

        Pageable pageable = PageRequest.of(
                page, itemsPerPage, sortDirection, sortField.getDatabaseFieldName());

        Page<ResourceManagerResponseDTO> resourceManagers =
                resourceManagerService.getResourceManager(keyword, pageable, principal);

        log.debug("ResourceManagerController::getResourceManagers completed; returned resource manager(s) '{}';", resourceManagers.getSize());

        return new ResponseEntity<>(resourceManagers, HttpStatus.OK);
    }

    @GetMapping("{resourceManagerId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<ResourceManagerResponseDTO> getResourceManagerById(
            @PathVariable Long resourceManagerId, Principal principal) {

        log.debug("ResourceManagerController::getResourceManagerById started; resourceManagerId '{}';", resourceManagerId);

        ResourceManagerResponseDTO resourceManager = resourceManagerService.getResourceManagerDtoById(
                resourceManagerId, principal);

        log.debug("ResourceManagerController::getResourceManagerById completed; resourceManagerId '{}';", resourceManager.getId());

        return ResponseEntity.ok().body(resourceManager);
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> createResourceManager(
            @Valid @RequestBody ResourceManagerRequestDTO request) {

        log.debug("ResourceManagerController::createResourceManager started;");

        resourceManagerService.saveResourceManager(request);

        log.debug("ResourceManagerController::createResourceManager completed;");

        return ResponseEntity.status(HttpStatus.CREATED).body(
                messageSourceUtil.buildResponse("resource-manager.created")
        );
    }

    @PutMapping("{resourceManagerId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> updateResourceManager(
            @PathVariable Long resourceManagerId,
            @Valid @RequestBody ResourceManagerRequestDTO request,
            Principal principal) {

        log.debug("ResourceManagerController::updateResourceManager started; resourceManagerId '{}';", resourceManagerId);

        resourceManagerService.updateResourceManager(resourceManagerId, request, principal);

        log.debug("ResourceManagerController::updateResourceManager completed; resourceManagerId '{}';", resourceManagerId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("resource-manager.updated"));
    }

    @DeleteMapping("{resourceManagerEmail}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP')")
    public ResponseEntity<Map<String, String>> deleteResourceManager(
            @PathVariable String resourceManagerEmail,
            Principal principal) {

        log.debug("ResourceManagerController::deleteResourceManager started; resourceManagerEmail '{}';", resourceManagerEmail);

        resourceManagerService.deleteResourceManagerByEmail(resourceManagerEmail, principal);

        log.debug("ResourceManagerController::deleteResourceManager completed; resourceManagerEmail '{}';", resourceManagerEmail);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("resource-manager.deleted"));
    }
}
