package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.response.TrialPeriodResponseDTO;
import com.capgemini.trialperiodapi.service.ITrialPeriodService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/trial-periods")
@RequiredArgsConstructor
@Transactional
@Slf4j
public class TrialPeriodController {
    private final ITrialPeriodService trialPeriodService;
    private final MessageSourceUtil messageSourceUtil;

    @GetMapping
    public ResponseEntity<List<TrialPeriodResponseDTO>> getImminentTrialPeriods(Principal principal) {

        log.debug("TrialPeriodController::getImminentTrialPeriods started;");

        List<TrialPeriodResponseDTO> imminentTrialPeriods =
                trialPeriodService.getImminentTrialPeriodDTOs(principal);

        log.debug("TrialPeriodController::getImminentTrialPeriods completed;");

        return ResponseEntity.ok().body(imminentTrialPeriods);
    }

    @GetMapping("/notification-count")
    public ResponseEntity<Integer> getNotificationCount(Principal principal) {

        log.debug("TrialPeriodController::getNotificationCount started;");

        Integer notificationCount = trialPeriodService.getNotificationCount(principal);

        log.debug("TrialPeriodController::getNotificationCount completed; returned notifications '{}';", notificationCount);

        return ResponseEntity.ok().body(notificationCount);
    }

    @PutMapping("/mark-as-hidden")
    public ResponseEntity<Map<String, String>> markAllTrialPeriodsAsInactive(
            @RequestParam List<Long> trialPeriodIds) {

        log.debug("TrialPeriodController::markAllTrialPeriodsAsInactive started; notification IDs '{}';", trialPeriodIds);

        trialPeriodService.hideFeedbackRequests(trialPeriodIds);

        log.debug("TrialPeriodController::markAllTrialPeriodsAsInactive completed; notification IDs '{}';", trialPeriodIds);

        return ResponseEntity.ok().body(
                messageSourceUtil.buildResponse(
                        trialPeriodIds.size() == 1 ? "notification.hidden" : "notifications.hidden"
                )
        );
    }

    @PostMapping("/notify")
    public ResponseEntity<Map<String, String>> notifyDecisionMakerByIds(
            @RequestParam List<Long> trialPeriodIds,
            @RequestBody String connectedUserSessionPassword,
            Principal principal) {

        log.debug("TrialPeriodController::getNotificationCount started; notification IDs '{}';", trialPeriodIds);

        trialPeriodService.notifyDecisionMakerByTrialPeriodIds(trialPeriodIds, connectedUserSessionPassword, principal);

        log.debug("TrialPeriodController::getNotificationCount completed; notification IDs '{}';", trialPeriodIds);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("trial-period.feedback-request.sent"));
    }

    @PostMapping("/notify/{notificationId}")
    public ResponseEntity<Map<String, String>> notifyDecisionMakerByTrialPeriodId(
            @PathVariable Long notificationId,
            @RequestBody String connectedUserSessionPassword, Principal principal) {

        log.debug("TrialPeriodController::notifyDecisionMakerByTrialPeriodId started; notification ID '{}';", notificationId);

        trialPeriodService.notifyDecisionMakerByNotificationId(notificationId, connectedUserSessionPassword, principal);

        log.debug("TrialPeriodController::notifyDecisionMakerByTrialPeriodId completed; notification ID '{}';", notificationId);

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("trial-period.feedback-request.sent"));
    }
}