package com.capgemini.trialperiodapi.controller;

import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.dto.request.UserRequestDTO;
import com.capgemini.trialperiodapi.service.IPeopleUnitService;
import com.capgemini.trialperiodapi.service.IUserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    private final IPeopleUnitService peopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final IUserService userService;

    @PutMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<Map<String, String>> updateProfile(
            @Valid @RequestBody UserRequestDTO request, Principal principal) {

        log.debug("UserController::updateProfile started; username '{}';", request.getUsername());

        userService.updateConnectedUser(request, principal);

        log.debug("UserController::updateProfile completed; username '{}';", request.getUsername());

        return ResponseEntity.ok().body(messageSourceUtil.buildResponse("user.profile-updated"));
    }

    @GetMapping("/current-user")
    public ResponseEntity<UserResponseDTO> getConnectedUser(Principal principal) {

        log.debug("UserController::getConnectedUser started; principal name '{}';", principal.getName());

        UserResponseDTO connectedUser = userService.getConnectedUser(principal);

        log.debug("UserController::getConnectedUser completed; username '{}';", connectedUser.getUsername());

        return ResponseEntity.ok().body(connectedUser);
    }

    @GetMapping("/current-user/people-units")
    @PreAuthorize("hasAnyRole('ADMIN', 'HRBP', 'HR')")
    public ResponseEntity<List<PeopleUnitResponseDTO>> getConnectedUserPeopleUnits(
            Principal principal) {

        log.debug("UserController::getConnectedUserPeopleUnits started; principal name '{}';", principal.getName());

        List<PeopleUnitResponseDTO> peopleUnits = peopleUnitService.getConnectedUserPeopleUnits(principal);

        log.debug("UserController::getConnectedUserPeopleUnits completed; returned PU(s) '{}';", peopleUnits.size());

        return ResponseEntity.ok().body(peopleUnits);
    }
}