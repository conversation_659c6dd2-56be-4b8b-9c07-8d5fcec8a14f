package com.capgemini.trialperiodapi.dto;

import com.opencsv.bean.CsvBindByPosition;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CollaboratorCsvRepresentation {

    @CsvBindByPosition(position = 0)
    private String peopleUnitName;

    @CsvBindByPosition(position = 1)
    private String ggid;

    @CsvBindByPosition(position = 2)
    private String status;

    @CsvBindByPosition(position = 3)
    private String lastname;

    @CsvBindByPosition(position = 4)
    private String firstname;

    @CsvBindByPosition(position = 5)
    private String localGrade;

    @CsvBindByPosition(position = 6)
    private String projectName;

    @CsvBindByPosition(position = 7)
    private String assignmentStatus;

    @CsvBindByPosition(position = 8)
    private String entryDate;

    @CsvBindByPosition(position = 9)
    private String firstTrialPeriodEndDate;

    @CsvBindByPosition(position = 10)
    private String firstTrialPeriodFeedbackRequestSentOn;

    @CsvBindByPosition(position = 11)
    private String firstTrialPeriodLastReminderDate;

    @CsvBindByPosition(position = 12)
    private String secondTrialPeriodEndDate;

    @CsvBindByPosition(position = 13)
    private String secondTrialPeriodFeedbackRequestSentOn;

    @CsvBindByPosition(position = 14)
    private String secondTrialPeriodLastReminderDate;

    @CsvBindByPosition(position = 15)
    private String decision;

    @CsvBindByPosition(position = 16)
    private String tenureDate;

    @CsvBindByPosition(position = 17)
    private String decisionMaker;

    @CsvBindByPosition(position = 18)
    private String scorecardSent;

    @CsvBindByPosition(position = 19)
    private String interviewed;

    @CsvBindByPosition(position = 20)
    private String collaboratorEmailSent;

    @CsvBindByPosition(position = 21)
    private String comment;
}
