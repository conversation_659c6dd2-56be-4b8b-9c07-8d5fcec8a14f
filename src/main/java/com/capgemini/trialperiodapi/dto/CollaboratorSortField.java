package com.capgemini.trialperiodapi.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CollaboratorSortField {
    GGID("ggid"),
    FIRSTNAME("firstname"),
    LASTNAME("lastname"),
    EMAIL("email"),
    ENTRY_DATE("entryDate"),
    STATUS("status"),
    ASSIGNMENT_STATUS("assignmentStatus"),
    LOCAL_GRADE("localGrade"),
    GLOBAL_GRADE("globalGrade"),
    TRIAL_PERIOD_STATUS("trialPeriodStatus"),
    CREATED_ON("createdOn"),
    LAST_UPDATE_ON("lastUpdatedOn");

    private final String databaseFieldName;
}
