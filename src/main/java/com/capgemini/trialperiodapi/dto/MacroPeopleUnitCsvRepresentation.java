package com.capgemini.trialperiodapi.dto;

import com.opencsv.bean.CsvBindByPosition;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MacroPeopleUnitCsvRepresentation {

    @CsvBindByPosition(position = 0)
    private String macroPeopleUnitName;

    @CsvBindByPosition(position = 1)
    private String peopleUnitNames;

    @CsvBindByPosition(position = 2)
    private String hrs;

    @CsvBindByPosition(position = 3)
    private String hrbps;

    @CsvBindByPosition(position = 4)
    private String practice;
}
