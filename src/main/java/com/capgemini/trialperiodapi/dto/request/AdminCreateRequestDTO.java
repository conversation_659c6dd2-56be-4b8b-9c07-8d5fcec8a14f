package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class AdminCreateRequestDTO extends AdminRequestDTO {
    @NotBlank(message = "{validation.password-required}")
    private String connectedUserSessionPassword;
}
