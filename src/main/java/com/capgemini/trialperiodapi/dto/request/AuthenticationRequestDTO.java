package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class AuthenticationRequestDTO {
    @NotBlank(message = "{validation.username-required}")
    private String username;

    @NotBlank(message = "{validation.password-required}")
    private String password;
}
