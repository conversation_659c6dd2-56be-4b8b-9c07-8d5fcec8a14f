package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangePasswordRequestDTO {
    @NotBlank(message = "{validation.current-password-required}")
    private String currentPassword;

    @NotBlank(message = "{validation.new-password-required}")
    @Pattern(regexp = "^(?=.*[A-Z])(?=.*\\d)(?=.*[@#$%^&+=!.]).{12,}$",
            message = "{validation.password-pattern}")
    private String newPassword;

    @NotBlank(message = "{validation.confirm-password-required}")
    private String confirmPassword;

    @AssertTrue(message = "{validation.passwords-not-match}")
    private boolean isPasswordMatching() {
        return newPassword != null && newPassword.equals(confirmPassword);
    }
}
