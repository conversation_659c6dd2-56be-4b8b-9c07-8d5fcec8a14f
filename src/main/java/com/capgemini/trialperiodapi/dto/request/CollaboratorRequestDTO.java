package com.capgemini.trialperiodapi.dto.request;

import com.capgemini.trialperiodapi.validator.ValidEntryDate;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@Builder
public class CollaboratorRequestDTO {
    @NotNull(message = "{validation.ggid-required}")
    @Pattern(regexp = "^\\d{4,12}$", message = "{validation.ggid-pattern}")
    private String ggid;

    @NotBlank(message = "{validation.firstname-required}")
    @Size(min = 2, message = "{validation.firstname-too-short}")
    @Size(max = 30, message = "{validation.firstname-too-long}")
    private String firstname;

    @NotBlank(message = "{validation.lastname-required}")
    @Size(min = 2, message = "{validation.lastname-too-short}")
    @Size(max = 30, message = "{validation.lastname-too-long}")
    private String lastname;

    @Size(max = 100, message = "{validation.email-too-long}")
    @Email(message = "{validation.email-pattern}")
    @Pattern(regexp = "^(|[A-Za-z0-9._%+-]+@capgemini\\.com)$",
            message = "{validation.email-pattern}")
    private String email;

    @NotNull(message = "{validation.entry-date-required}")
    @ValidEntryDate
    private LocalDate entryDate;

    private LocalDate tenureDate;

    @NotEmpty(message = "{validation.collaborator-status-required}")
    private String status;

    @NotEmpty(message = "{validation.global-grade-required}")
    private String globalGrade;

    @NotEmpty(message = "{validation.local-grade-required}")
    private String localGrade;

    @NotEmpty(message = "{validation.assignment-status-required}")
    private String assignmentStatus;

    private String projectName;

    @NotEmpty(message = "{validation.people-unit-required}")
    private String peopleUnitName;

    private boolean interviewed;

    private boolean scorecardSent;

    private boolean collaboratorEmailSent;

    @NotEmpty(message = "{validation.trial-period-status-required}")
    private String trialPeriodStatus;

    @Size(max = 2000, message = "{validation.email-too-long}")
    private String comment;
}
