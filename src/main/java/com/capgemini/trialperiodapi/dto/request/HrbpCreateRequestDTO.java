package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class HrbpCreateRequestDTO extends HrbpRequestDTO {
    @NotBlank(message = "{validation.password-required}")
    private String connectedUserSessionPassword;
}
