package com.capgemini.trialperiodapi.dto.request;

import com.capgemini.trialperiodapi.validator.ListLength;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MacroPeopleUnitRequestDTO {
    @NotBlank(message = "{validation.macro-people-unit-required}")
    @Size(min = 2, message = "{validation.macro-people-unit-name-too-short}")
    @Size(max = 100, message = "{validation.macro-people-unit-name-too-long}")
    @Pattern(regexp = "^[^/\\\\]*$", message = "{validation.name-no-slash}")
    private String name;

    @NotNull(message = "{validation.pu-list-is-empty}")
    private Set<String> peopleUnitNames = new HashSet<>();

    @NotNull(message = "{validation.hr-list-is-empty}")
    @ListLength(message = "{validation.hr-list-is-empty}")
    private Set<String> hrEmails = new HashSet<>();

    @NotNull(message = "{validation.hrbp-list-is-empty}")
    @ListLength(message = "{validation.hrbp-list-is-empty}")
    private Set<String> hrbpEmails = new HashSet<>();

    @NotNull(message = "{validation.people-unit-manager-list-is-empty}")
    @ListLength(message = "{validation.people-unit-manager-list-is-empty}")
    private Set<String> pumEmails = new HashSet<>();

    private Set<String> resourceManagerEmails = new HashSet<>();
}
