package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PeopleUnitRequestDTO {
    @NotBlank(message = "{validation.people-unit-required}")
    @Size(min = 2, message = "{validation.peopleUnit-name-too-short}")
    @Size(max = 100, message = "{validation.peopleUnit-name-too-long}")
    @Pattern(regexp = "^[^/\\\\]*$", message = "{validation.name-no-slash}")
    private String name;

    @NotBlank(message = "{validation.macro-people-unit-name-required}")
    @Size(min = 2, message = "{validation.macro-people-unit-name-too-short}")
    @Size(max = 100, message = "{validation.macro-people-unit-name-too-long}")
    private String macroPeopleUnitName;
}
