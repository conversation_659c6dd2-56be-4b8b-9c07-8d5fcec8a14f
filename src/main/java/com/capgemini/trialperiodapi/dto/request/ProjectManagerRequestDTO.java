package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@Builder
public class ProjectManagerRequestDTO {
    @NotBlank(message = "{validation.firstname-required}")
    @Size(min = 4, message = "{validation.firstname-too-short}")
    @Size(max = 50, message = "{validation.firstname-too-long}")
    private String firstname;

    @NotBlank(message = "{validation.lastname-required}")
    @Size(min = 4, message = "{validation.lastname-too-short}")
    @Size(max = 50, message = "{validation.lastname-too-long}")
    private String lastname;

    @NotBlank(message = "{validation.full-name-required}")
    @Size(max = 100, message = "Email must be at most 100 characters")
    @Email(message = "Invalid email format")
    @Pattern(regexp = "^[A-Za-z0-9._%+-]+@capgemini\\.com$",
            message = "{validation.email-pattern}")
    private String email;

    private Set<String> projectNames;
}
