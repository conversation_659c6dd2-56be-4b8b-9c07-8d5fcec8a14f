package com.capgemini.trialperiodapi.dto.request;


import com.capgemini.trialperiodapi.validator.ListLength;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@Builder
public class ProjectRequestDTO {
    @NotBlank(message = "{validation.project-name-required}")
    @Size(min = 2, message = "{validation.project-name-too-short}")
    @Size(max = 100, message = "{validation.project-name-too-long}")
    @Pattern(regexp = "^[^/\\\\]*$", message = "{validation.name-no-slash}")
    private String name;

    @ListLength(message = "{validation.project-manager-list-is-empty}")
    private Set<String> projectManagerEmails;
}
