package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class RegisterRequestDTO {
    @NotBlank(message = "{validation.firstname-required}")
    @Size(min = 4, message = "{validation.firstname-too-short}")
    @Size(max = 50, message = "{validation.firstname-too-long}")
    private String firstname;

    @NotBlank(message = "{validation.lastname-required}")
    @Size(min = 4, message = "{validation.lastname-too-short}")
    @Size(max = 50, message = "{validation.lastname-too-long}")
    private String lastname;

    @NotBlank(message = "{validation.email-required}")
    @Size(max = 100, message = "{validation.email-too-long}")
    @Email(message = "{validation.email-pattern}")
    @Pattern(regexp = "^[A-Za-z0-9._%+-]+@capgemini\\.com$",
            message = "{validation.email-pattern}")
    private String email;

    @NotBlank(message = "{validation.username-required}")
    private String username;

    @NotBlank(message = "{validation.password-required}")
    @Pattern(regexp = "^(?=.*[A-Z])(?=.*\\d)(?=.*[@#$%^&+=!.]).{12,}$",
            message = "{validation.password-pattern}")
    private String password;

    @NotBlank(message = "{validation.confirm-password-required}")
    private String confirmPassword;

    @AssertTrue(message = "{validation.passwords-not-match}")
    private boolean isPasswordMatching() {
        return confirmPassword != null && confirmPassword.equals(password);
    }
}
