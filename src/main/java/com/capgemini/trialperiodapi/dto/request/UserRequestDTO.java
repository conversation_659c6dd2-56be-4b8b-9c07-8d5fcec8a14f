package com.capgemini.trialperiodapi.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class UserRequestDTO {
    @NotBlank(message = "{validation.firstname-required}")
    @Size(min = 4, message = "{validation.firstname-too-short}")
    @Size(max = 50, message = "{validation.firstname-too-long}")
    private String firstname;

    @NotBlank(message = "{validation.lastname-required}")
    @Size(min = 4, message = "{validation.lastname-too-short}")
    @Size(max = 50, message = "{validation.lastname-too-long}")
    private String lastname;

    @NotBlank(message = "{validation.username-required}")
    @Size(min = 3, message = "{validation.username-too-short}")
    @Size(max = 25, message = "{validation.username-too-long}")
    private String username;

    @NotBlank(message = "{validation.email-required}")
    @Size(max = 100, message = "{validation.email-too-long}")
    @Email(message = "{validation.email-pattern}")
    @Pattern(regexp = "^[A-Za-z0-9._%+-]+@capgemini\\.com$",
            message = "{validation.email-pattern}")
    private String email;
}
