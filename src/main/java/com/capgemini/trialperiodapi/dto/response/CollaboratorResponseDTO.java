package com.capgemini.trialperiodapi.dto.response;

import com.capgemini.trialperiodapi.model.AssignmentStatus;
import com.capgemini.trialperiodapi.model.CollaboratorStatus;
import com.capgemini.trialperiodapi.model.GlobalGrade;
import com.capgemini.trialperiodapi.model.LocalGrade;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollaboratorResponseDTO {
    private Long id;
    private String ggid;
    private String firstname;
    private String lastname;
    private String email;
    private String phoneNumber;
    private LocalDate entryDate;
    private LocalDate tenureDate;
    private boolean interviewed;
    private boolean scorecardSent;
    private boolean collaboratorEmailSent;
    private String comment;
    private CollaboratorStatus status;
    private AssignmentStatus assignmentStatus;
    private LocalGrade localGrade;
    private GlobalGrade globalGrade;
    private String projectName;
    private String peopleUnitName;
    private TrialPeriodResponseDTO trialPeriod;
}
