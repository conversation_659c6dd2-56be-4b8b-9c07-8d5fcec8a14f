package com.capgemini.trialperiodapi.dto.response;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MacroPeopleUnitResponseDTO {
    private Long id;
    private String name;
    private Set<String> peopleUnitNames;
    private Set<UserResponseDTO> hrs;
    private Set<UserResponseDTO> hrbps;
    private Set<PeopleUnitManagerResponseDTO> pums;
    private Set<ResourceManagerResponseDTO> resourceManagers;
    private LocalDateTime createdOn;
    private LocalDateTime lastUpdatedOn;
}
