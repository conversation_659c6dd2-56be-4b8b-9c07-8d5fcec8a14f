package com.capgemini.trialperiodapi.dto.response;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectResponseDTO {
    private Long id;
    private String name;
    private Set<ProjectManagerResponseDTO> projectManagers;
    private LocalDateTime createdOn;
    private LocalDateTime lastUpdatedOn;
}
