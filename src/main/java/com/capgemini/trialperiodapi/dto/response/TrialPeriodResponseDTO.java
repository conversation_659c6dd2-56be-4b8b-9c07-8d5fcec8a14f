package com.capgemini.trialperiodapi.dto.response;

import com.capgemini.trialperiodapi.model.TrialPeriodStatus;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrialPeriodResponseDTO {
    private Long id;
    private LocalDate firstTrialPeriodEndDate;
    private LocalDate firstTrialPeriodNotificationDate;
    private LocalDateTime firstTrialPeriodLastReminderDate;
    private LocalDateTime firstTrialPeriodFeedbackRequestSentOn;
    private LocalDate secondTrialPeriodEndDate;
    private LocalDate secondTrialPeriodNotificationDate;
    private LocalDateTime secondTrialPeriodLastReminderDate;
    private LocalDateTime secondTrialPeriodFeedbackRequestSentOn;
    private TrialPeriodStatus status;
    private boolean scorecardSent;
    private boolean collaboratorEmailSent;
    private LocalDateTime lastComputedOn;
    private boolean isFirstTrialPeriod;
    private boolean interviewed;
    private String comment;
    private Long decisionMakerId;
    private Long collaboratorId;
}