package com.capgemini.trialperiodapi.dto.response;

import com.capgemini.trialperiodapi.model.auth.Role;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserResponseDTO {
    private Long id;
    private String firstname;
    private String lastname;
    private String username;
    private String email;
    private Role role;
    private Set<String> macroPeopleUnitNames;
    private LocalDateTime lastPasswordUpdatedOn;
}
