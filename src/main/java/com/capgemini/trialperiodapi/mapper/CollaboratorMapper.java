package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.CollaboratorResponseDTO;
import com.capgemini.trialperiodapi.model.Collaborator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CollaboratorMapper {

    private final TrialPeriodMapper trialPeriodMapper;

    public CollaboratorResponseDTO toCollaboratorResponseDTO(Collaborator collaborator) {
        return CollaboratorResponseDTO.builder()
                .id(collaborator.getId())
                .ggid(collaborator.getGgid())
                .firstname(collaborator.getFirstname())
                .lastname(collaborator.getLastname())
                .email(collaborator.getEmail())
                .phoneNumber(collaborator.getPhoneNumber())
                .entryDate(collaborator.getEntryDate())
                .tenureDate(collaborator.getTenureDate())
                .interviewed(collaborator.isInterviewed())
                .scorecardSent(collaborator.getTrialPeriod() != null && collaborator.getTrialPeriod().isScorecardSent())
                .collaboratorEmailSent(collaborator.getTrialPeriod() != null && collaborator.getTrialPeriod().isCollaboratorEmailSent())
                .comment(collaborator.getTrialPeriod() == null ?
                        null : collaborator.getTrialPeriod().getComment())
                .interviewed(collaborator.isInterviewed())
                .status(collaborator.getStatus())
                .assignmentStatus(collaborator.getAssignmentStatus())
                .localGrade(collaborator.getLocalGrade())
                .globalGrade(collaborator.getGlobalGrade())
                .projectName(collaborator.getProject() == null ?
                        null : collaborator.getProject().getName())
                .peopleUnitName(collaborator.getPeopleUnit() == null ?
                        null : collaborator.getPeopleUnit().getName())
                .trialPeriod(collaborator.getTrialPeriod() == null ?
                        null : trialPeriodMapper.toTrialPeriodResponseDTO(
                        collaborator.getTrialPeriod()))
                .build();
    }
}
