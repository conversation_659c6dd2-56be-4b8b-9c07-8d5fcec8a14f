package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.request.MacroPeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.MacroPeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import com.capgemini.trialperiodapi.service.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class MacroPeopleUnitMapper {
    private final IPeopleUnitManagerService peopleUnitManagerService;
    private final IResourceManagerService resourceManagerService;
    private final ResourceManagerMapper resourceManagerMapper;
    private final IPeopleUnitService peopleUnitService;
    private final PeopleUnitManagerMapper pumMapper;
    private final IHrbpService hrbpService;
    private final UserMapper userMapper;
    private final IHrService hrService;

    public MacroPeopleUnitMapper(IPeopleUnitManagerService peopleUnitManagerService,
                                 IResourceManagerService resourceManagerService,
                                 ResourceManagerMapper resourceManagerMapper,
                                 @Lazy IPeopleUnitService peopleUnitService,
                                 PeopleUnitManagerMapper pumMapper,
                                 IHrbpService hrbpService, UserMapper userMapper,
                                 @Lazy IHrService hrService) {
        this.peopleUnitManagerService = peopleUnitManagerService;
        this.resourceManagerService = resourceManagerService;
        this.resourceManagerMapper = resourceManagerMapper;
        this.peopleUnitService = peopleUnitService;
        this.pumMapper = pumMapper;
        this.hrbpService = hrbpService;
        this.userMapper = userMapper;
        this.hrService = hrService;
    }

    public MacroPeopleUnitResponseDTO toMacroPeopleUnitDTO(MacroPeopleUnit macroPeopleUnit) {
        return MacroPeopleUnitResponseDTO.builder()
                .id(macroPeopleUnit.getId())
                .name(macroPeopleUnit.getName())
                .peopleUnitNames(macroPeopleUnit.getPeopleUnits()
                        .stream()
                        .map(PeopleUnit::getName)
                        .collect(Collectors.toSet()))
                .hrs(macroPeopleUnit.getHrs()
                        .stream()
                        .map(userMapper::toHrDTO)
                        .collect(Collectors.toSet()))
                .hrbps(macroPeopleUnit.getHrbps()
                        .stream()
                        .map(userMapper::toHrbpDTO)
                        .collect(Collectors.toSet()))
                .pums(macroPeopleUnit.getPums()
                        .stream()
                        .map(pumMapper::toPeopleUnitManagerDTO)
                        .collect(Collectors.toSet()))
                .resourceManagers(macroPeopleUnit.getResourceManagers()
                        .stream()
                        .map(resourceManagerMapper::toResourceManagerDTO)
                        .collect(Collectors.toSet()))
                .createdOn(macroPeopleUnit.getCreatedOn())
                .lastUpdatedOn(macroPeopleUnit.getLastUpdatedOn())
                .build();
    }

    public MacroPeopleUnit toMacroPeopleUnitEntity(MacroPeopleUnitRequestDTO request) {
        return MacroPeopleUnit.builder()
                .name(request.getName())
                .peopleUnits(request.getPeopleUnitNames().stream()
                        .map(peopleUnitService::getPeopleUnitByName)
                        .collect(Collectors.toSet()))
                .hrs(request.getHrEmails().stream()
                        .map(hrService::getHrByEmail)
                        .collect(Collectors.toSet()))
                .hrbps(request.getHrbpEmails().stream()
                        .map(hrbpService::getHrbpByEmail)
                        .collect(Collectors.toSet()))
                .pums(request.getPumEmails().stream()
                        .map(peopleUnitManagerService::getPeopleUnitManagerByEmail)
                        .collect(Collectors.toSet()))
                .resourceManagers(request.getResourceManagerEmails().stream()
                        .map(resourceManagerService::getResourceManagerByEmail)
                        .collect(Collectors.toSet()))
                .build();
    }
}
