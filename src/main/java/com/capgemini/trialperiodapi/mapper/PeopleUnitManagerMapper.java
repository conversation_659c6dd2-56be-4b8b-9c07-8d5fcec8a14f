package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class PeopleUnitManagerMapper {
    public PeopleUnitManagerResponseDTO toPeopleUnitManagerDTO(PeopleUnitManager peopleUnitManager){
        return PeopleUnitManagerResponseDTO.builder()
                .id(peopleUnitManager.getId())
                .firstname(peopleUnitManager.getFirstname())
                .lastname(peopleUnitManager.getLastname())
                .email(peopleUnitManager.getEmail())
                .macroPeopleUnitNames(peopleUnitManager.getMacroPeopleUnits()
                        .stream()
                        .map(MacroPeopleUnit::getName)
                        .collect(Collectors.toSet()))
                .build();
    }
}
