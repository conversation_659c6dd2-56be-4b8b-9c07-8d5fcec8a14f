package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import org.springframework.stereotype.Component;

@Component
public class PeopleUnitMapper {

    public PeopleUnitResponseDTO toPeopleUnitDTO(PeopleUnit peopleUnit) {
        return PeopleUnitResponseDTO.builder()
                .id(peopleUnit.getId())
                .name(peopleUnit.getName())
                .macroPeopleUnitName(peopleUnit.getMacroPeopleUnit() != null ?
                        peopleUnit.getMacroPeopleUnit().getName() : null)
                .createdOn(peopleUnit.getCreatedOn())
                .lastUpdatedOn(peopleUnit.getLastUpdatedOn())
                .build();
    }
}
