package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.ProjectManager;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class ProjectManagerMapper {

    public ProjectManagerResponseDTO toProjectManagerResponseDTO(ProjectManager projectManager) {
        return ProjectManagerResponseDTO.builder()
                .id(projectManager.getId())
                .firstname(projectManager.getFirstname())
                .lastname(projectManager.getLastname())
                .email(projectManager.getEmail())
                .projectNames(projectManager.getProjects()
                        .stream()
                        .map(Project::getName)
                        .collect(Collectors.toSet()))
                .build();
    }
}
