package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.ProjectResponseDTO;
import com.capgemini.trialperiodapi.model.Project;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ProjectMapper {

    private final ProjectManagerMapper projectManagerMapper;

    public ProjectResponseDTO toProjectResponseDTO(Project project) {
        return ProjectResponseDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .projectManagers(project.getProjectManagers()
                        .stream()
                        .map(projectManagerMapper::toProjectManagerResponseDTO)
                        .collect(Collectors.toSet()))
                .createdOn(project.getCreatedOn())
                .lastUpdatedOn(project.getLastUpdatedOn())
                .build();
    }
}
