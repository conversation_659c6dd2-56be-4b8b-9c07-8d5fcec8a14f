package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.ResourceManagerResponseDTO;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.ResourceManager;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class ResourceManagerMapper {
    public ResourceManagerResponseDTO toResourceManagerDTO(ResourceManager resourceManager) {
        return ResourceManagerResponseDTO.builder()
                .id(resourceManager.getId())
                .firstname(resourceManager.getFirstname())
                .lastname(resourceManager.getLastname())
                .email(resourceManager.getEmail())
                .macroPeopleUnitNames(resourceManager.getMacroPeopleUnits()
                        .stream()
                        .map(MacroPeopleUnit::getName)
                        .collect(Collectors.toSet()))
                .build();
    }
}
