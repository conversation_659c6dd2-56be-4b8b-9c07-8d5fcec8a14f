package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.TrialPeriodResponseDTO;
import com.capgemini.trialperiodapi.model.TrialPeriod;
import org.springframework.stereotype.Component;

@Component
public class TrialPeriodMapper {

    public TrialPeriodResponseDTO toTrialPeriodResponseDTO(TrialPeriod trialPeriod) {
        return TrialPeriodResponseDTO.builder()
                .id(trialPeriod.getId())
                .firstTrialPeriodEndDate(trialPeriod.getFirstTrialPeriodEndDate())
                .firstTrialPeriodNotificationDate(trialPeriod.getFirstTrialPeriodNotificationDate())
                .firstTrialPeriodLastReminderDate(trialPeriod.getFirstTrialPeriodLastReminderDate())
                .secondTrialPeriodEndDate(trialPeriod.getSecondTrialPeriodEndDate())
                .secondTrialPeriodNotificationDate(trialPeriod.getSecondTrialPeriodNotificationDate())
                .secondTrialPeriodLastReminderDate(trialPeriod.getSecondTrialPeriodLastReminderDate())
                .scorecardSent(trialPeriod.isScorecardSent())
                .collaboratorEmailSent(trialPeriod.isCollaboratorEmailSent())
                .firstTrialPeriodFeedbackRequestSentOn(trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn())
                .secondTrialPeriodFeedbackRequestSentOn(trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn())
                .lastComputedOn(trialPeriod.getLastComputedOn())
                .comment(trialPeriod.getComment())
                .isFirstTrialPeriod(trialPeriod.isFirstTrialPeriod())
                .collaboratorId(trialPeriod.getCollaborator().getId())
                .status(trialPeriod.getStatus())
                .build();
    }
}
