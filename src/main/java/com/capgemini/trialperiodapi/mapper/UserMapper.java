package com.capgemini.trialperiodapi.mapper;

import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class UserMapper {
    public UserResponseDTO toAdminDTO(AppUser user) {
        return UserResponseDTO.builder()
                .id(user.getId())
                .firstname(user.getFirstname())
                .lastname(user.getLastname())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .lastPasswordUpdatedOn(user.getLastPasswordUpdatedOn())
                .build();
    }

    public UserResponseDTO toHrbpDTO(AppUser user) {
        return UserResponseDTO.builder()
                .id(user.getId())
                .firstname(user.getFirstname())
                .lastname(user.getLastname())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .lastPasswordUpdatedOn(user.getLastPasswordUpdatedOn())
                .macroPeopleUnitNames(((Hrbp) user).getMacroPeopleUnits()
                        .stream()
                        .map(MacroPeopleUnit::getName)
                        .collect(Collectors.toSet()))
                .build();
    }

    public UserResponseDTO toHrDTO(AppUser user) {
        return UserResponseDTO.builder()
                .id(user.getId())
                .firstname(user.getFirstname())
                .lastname(user.getLastname())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .lastPasswordUpdatedOn(user.getLastPasswordUpdatedOn())
                .macroPeopleUnitNames(((Hr) user).getMacroPeopleUnits()
                        .stream()
                        .map(MacroPeopleUnit::getName)
                        .collect(Collectors.toSet()))
                .build();
    }
}
