package com.capgemini.trialperiodapi.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@AllArgsConstructor
@Getter
@Slf4j
public enum AssignmentStatus {
    FIRM_PROJECT("Firm Project"),
    SHADOW("Shadow"),
    OPPORTUNITY_0("Opportunity 0%");

    private final String label;

    public static AssignmentStatus fromLabel(String label) {
        if (isEmpty(label)) {
            log.warn("TrialPeriodStatus::fromLabel label is blank;");
            throw new IllegalArgumentException("Label cannot be blank:");
        }
        for (AssignmentStatus status : AssignmentStatus.values()) {
            if (status.getLabel().equalsIgnoreCase(label)) {
                return status;
            }
        }
        log.warn("AssignmentStatus::fromLabel no enum constant with label '{}';", label);
        throw new IllegalArgumentException("No enum constant with label: " + label);
    }
}
