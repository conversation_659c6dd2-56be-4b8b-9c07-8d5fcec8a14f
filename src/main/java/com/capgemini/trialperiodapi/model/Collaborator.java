package com.capgemini.trialperiodapi.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "collaborators", uniqueConstraints = {@UniqueConstraint(columnNames = {"ggid", "deleted_on"})})
@SQLDelete(sql = "UPDATE collaborators SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class Collaborator {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ggid", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "ggid",
            read = "pgp_sym_decrypt(ggid::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String ggid;

    @Column(name = "firstname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "firstname",
            read = "pgp_sym_decrypt(firstname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String firstname;

    @Column(name = "lastname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "lastname",
            read = "pgp_sym_decrypt(lastname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String lastname;

    @Column(name = "email", columnDefinition = "bytea")
    @ColumnTransformer(
            forColumn = "email",
            read = "pgp_sym_decrypt(email::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String email;

    @Column(name = "phone_number", columnDefinition = "bytea")
    @ColumnTransformer(
            forColumn = "phone_number",
            read = "pgp_sym_decrypt(phone_number::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String phoneNumber;

    @Column(nullable = false)
    private LocalDate entryDate;

    private LocalDate tenureDate;

    private boolean interviewed;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private CollaboratorStatus status;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private AssignmentStatus assignmentStatus;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private GlobalGrade globalGrade;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private LocalGrade localGrade;

    @ManyToOne
    @JoinColumn(name = "project_id")
    private Project project;

    @ManyToOne(optional = false)
    @JoinColumn(name = "people_unit_id", nullable = false)
    private PeopleUnit peopleUnit;

    @OneToOne(mappedBy = "collaborator", cascade = CascadeType.ALL, optional = false)
    private TrialPeriod trialPeriod;


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    public String fullName() {
        return this.firstname + " " + this.lastname;
    }

    public boolean isInFirmProject() {
        return this.getAssignmentStatus() == AssignmentStatus.FIRM_PROJECT;
    }

    public boolean isInShadowOrOpp0() {
        return this.getAssignmentStatus() == AssignmentStatus.OPPORTUNITY_0
                || this.getAssignmentStatus() == AssignmentStatus.SHADOW;
    }

    public int trialPeriodDuration() {
        return this.status == CollaboratorStatus.EMPLOYEE ? 45 : 90;
    }

    @PrePersist
    @PreUpdate
    private void trim() {
        firstname = firstname.trim();
        lastname = lastname.trim();
        if (email != null) {
            email = email.trim();
        }
    }
}

