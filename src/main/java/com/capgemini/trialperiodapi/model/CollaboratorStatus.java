package com.capgemini.trialperiodapi.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@RequiredArgsConstructor
@Getter
@Slf4j
public enum CollaboratorStatus {
    CADRE("Cadre"),
    EMPLOYEE("Employé");

    private final String label;

    public static CollaboratorStatus fromLabel(String label) {
        if (isEmpty(label)) {
            log.warn("TrialPeriodStatus::fromLabel label is blank;");
            throw new IllegalArgumentException("Label cannot be blank:");
        }
        for (CollaboratorStatus status : CollaboratorStatus.values()) {
            if (status.getLabel().equalsIgnoreCase(label)) {
                return status;
            }
        }
        log.warn("CollaboratorStatus::fromLabel no enum constant with label '{}';", label);
        throw new IllegalArgumentException("No enum constant with label: " + label);
    }
}