package com.capgemini.trialperiodapi.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@SQLRestriction("deleted_on is NULL")
public class DecisionMaker {
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE)
    private Long id;

    @Column(name = "firstname", nullable = false)
    private String firstname;

    @Column(name = "lastname", nullable = false)
    private String lastname;

    @Column(name = "email", nullable = false)
    private String email;


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    @PrePersist
    @PreUpdate
    private void trim() {
        firstname = firstname.trim();
        lastname = lastname.trim();
        if (email != null) {
            email = email.trim();
        }
    }
}