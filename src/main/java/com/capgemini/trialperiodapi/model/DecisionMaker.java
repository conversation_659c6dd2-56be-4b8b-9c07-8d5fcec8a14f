package com.capgemini.trialperiodapi.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@SQLRestriction("deleted_on is NULL")
public class DecisionMaker {
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE)
    private Long id;

    @Column(name = "firstname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "firstname",
            read = "pgp_sym_decrypt(firstname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String firstname;

    @Column(name = "lastname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "lastname",
            read = "pgp_sym_decrypt(lastname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String lastname;

    @Column(name = "email", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "email",
            read = "pgp_sym_decrypt(email::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String email;


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    @PrePersist
    @PreUpdate
    private void trim() {
        firstname = firstname.trim();
        lastname = lastname.trim();
        if (email != null) {
            email = email.trim();
        }
    }
}