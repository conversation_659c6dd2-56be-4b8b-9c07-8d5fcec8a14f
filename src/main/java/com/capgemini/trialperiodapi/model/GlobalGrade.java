package com.capgemini.trialperiodapi.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum GlobalGrade {

    A(new LocalGrade[]{LocalGrade.A1, LocalGrade.A2}),
    B(new LocalGrade[]{LocalGrade.B1, LocalGrade.B2}),
    C(new LocalGrade[]{LocalGrade.C}),
    D(new LocalGrade[]{LocalGrade.D}),
    E(new LocalGrade[]{LocalGrade.E});

    private final LocalGrade[] localGrades;

    public boolean containsLocalGrade(LocalGrade localGrade) {
        for (LocalGrade grade : localGrades) {
            if (grade == localGrade) {
                return true;
            }
        }
        return false;
    }

    public static GlobalGrade fromLocalGrade(LocalGrade localGrade) {
        for (GlobalGrade globalGrade : GlobalGrade.values()) {
            for (LocalGrade lg : globalGrade.getLocalGrades()) {
                if (lg == localGrade) {
                    return globalGrade;
                }
            }
        }
        throw new IllegalArgumentException("No matching GlobalGrade found for LocalGrade: " + localGrade);
    }
}