package com.capgemini.trialperiodapi.model;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToMany;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@DiscriminatorValue("HR")
public class Hr extends AppUser {
    @ManyToMany(mappedBy = "hrs")
    private Set<MacroPeopleUnit> macroPeopleUnits = new HashSet<>();
}

