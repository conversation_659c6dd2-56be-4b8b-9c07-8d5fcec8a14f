package com.capgemini.trialperiodapi.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Table(name = "macro_people_units", uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "deleted_on"})})
@SQLDelete(sql = "UPDATE macro_people_units SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class MacroPeopleUnit implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "macro_people_unit_id")
    private Set<PeopleUnit> peopleUnits = new HashSet<>();

    @ManyToMany
    @JoinTable(
            name = "hrbps_macro_people_units",
            joinColumns = @JoinColumn(name = "macro_people_unit_id"),
            inverseJoinColumns = @JoinColumn(name = "user_id"))
    private Set<Hrbp> hrbps = new HashSet<>();

    @ManyToMany
    @JoinTable(
            name = "hrs_macro_people_units",
            joinColumns = @JoinColumn(name = "macro_people_unit_id"),
            inverseJoinColumns = @JoinColumn(name = "hr_id"))
    private Set<Hr> hrs = new HashSet<>();

    @ManyToMany
    @JoinTable(
            name = "resource_managers_macro_people_units",
            joinColumns = @JoinColumn(name = "macro_people_unit_id"),
            inverseJoinColumns = @JoinColumn(name = "resource_manager_id"))
    private Set<ResourceManager> resourceManagers = new HashSet<>();

    @ManyToMany
    @JoinTable(
            name = "pums_macro_people_units",
            joinColumns = @JoinColumn(name = "macro_people_unit_id"),
            inverseJoinColumns = @JoinColumn(name = "pum_id"))
    private Set<PeopleUnitManager> pums = new HashSet<>();


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    @PrePersist
    private void prePersist() {
        name = name.trim();
        name = name.replace("/", "|");
        if (peopleUnits == null){
            setPeopleUnits(new HashSet<>());
        }
        if (hrbps == null){
            setHrbps(new HashSet<>());
        }
        if (hrs == null){
            setHrs(new HashSet<>());
        }
        if (resourceManagers == null){
            setResourceManagers(new HashSet<>());
        }
        if (pums == null){
            setPums(new HashSet<>());
        }
    }

    @PreUpdate
    private void preUpdate() {
        name = name.trim();
        name = name.replace("/", "|");
    }
}
