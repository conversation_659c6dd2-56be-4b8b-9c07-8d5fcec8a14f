package com.capgemini.trialperiodapi.model;

import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "people_units", uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "deleted_on"})})
@SQLDelete(sql = "UPDATE people_units SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class PeopleUnit implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToOne
    @JoinColumn(name = "macro_people_unit_id")
    private MacroPeopleUnit macroPeopleUnit;

    @OneToMany(mappedBy = "peopleUnit")
    private Set<Collaborator> collaborators;

    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    @PrePersist
    @PreUpdate
    private void preUpdate() {
        name = name.trim();
        name = name.replace("/", "|");
    }
}
