package com.capgemini.trialperiodapi.model;

import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "projects", uniqueConstraints = {@UniqueConstraint(columnNames = {"name", "deleted_on"})})
@SQLDelete(sql = "UPDATE projects SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class Project {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToMany
    @JoinTable(
            name = "project_project_manager",
            joinColumns = @JoinColumn(name = "project_id"),
            inverseJoinColumns = @JoinColumn(name = "project_manager_id"))
    private Set<ProjectManager> projectManagers = new HashSet<>();

    @OneToMany(mappedBy = "project")
    private Set<Collaborator> collaborators;


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    @PrePersist
    @PreUpdate
    private void preUpdate() {
        name = name.trim();
        name = name.replace("/", "|");
    }
}