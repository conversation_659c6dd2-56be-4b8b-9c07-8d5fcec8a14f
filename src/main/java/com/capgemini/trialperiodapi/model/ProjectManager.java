package com.capgemini.trialperiodapi.model;

import jakarta.persistence.Entity;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;

import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "project_managers", uniqueConstraints = {@UniqueConstraint(columnNames = {"email", "deleted_on"})})
@SQLDelete(sql = "UPDATE project_managers SET deleted_on = now() WHERE ID = ?")
public class ProjectManager extends DecisionMaker {

    @ManyToMany(mappedBy = "projectManagers")
    private Set<Project> projects = new HashSet<>();

}

