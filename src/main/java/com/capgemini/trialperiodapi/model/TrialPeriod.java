package com.capgemini.trialperiodapi.model;

import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "trial_periods")
@SQLDelete(sql = "UPDATE trial_periods SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class TrialPeriod {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private LocalDate firstTrialPeriodEndDate;

    private LocalDate firstTrialPeriodNotificationDate;

    private LocalDateTime firstTrialPeriodFeedbackRequestSentOn;

    private LocalDateTime firstTrialPeriodLastReminderDate;

    private LocalDate secondTrialPeriodEndDate;

    private LocalDate secondTrialPeriodNotificationDate;

    private LocalDateTime secondTrialPeriodFeedbackRequestSentOn;

    private LocalDateTime secondTrialPeriodLastReminderDate;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TrialPeriodStatus status;

    @OneToOne
    @JoinColumn(name = "collaborator_id", referencedColumnName = "id")
    private Collaborator collaborator;

    private boolean scorecardSent;

    private boolean hidden;

    private boolean collaboratorEmailSent;

    private LocalDateTime lastComputedOn;

    @Column(name = "comment", columnDefinition = "bytea")
    @ColumnTransformer(
            forColumn = "comment",
            read = "pgp_sym_decrypt(comment::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String comment;


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;

    public boolean isFirstTrialPeriod() {
        return status.equals(TrialPeriodStatus.IN_PROGRESS);
    }
}

