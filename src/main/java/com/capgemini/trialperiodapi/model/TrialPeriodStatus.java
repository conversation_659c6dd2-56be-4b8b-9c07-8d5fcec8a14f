package com.capgemini.trialperiodapi.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@AllArgsConstructor
@Getter
@Slf4j
public enum TrialPeriodStatus {
    CONFIRMED("Confirmation"),
    RENEWED("Renouvellement"),
    IN_PROGRESS("PE en cours"),
    REJECTED("Rejet"),
    DEPARTURE("Départ");

    private final String label;

    public static TrialPeriodStatus fromLabel(String label) {
        if (isEmpty(label)) {
            log.warn("TrialPeriodStatus::fromLabel label is blank;");
            throw new IllegalArgumentException("Label cannot be blank:");
        }
        for (TrialPeriodStatus status : TrialPeriodStatus.values()) {
            if (status.getLabel().equalsIgnoreCase(label)) {
                return status;
            }
        }
        log.warn("TrialPeriodStatus::fromLabel no enum constant with label '{}';", label);
        throw new IllegalArgumentException("No enum constant with label: " + label);
    }

}
