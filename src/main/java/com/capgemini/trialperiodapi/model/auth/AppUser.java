package com.capgemini.trialperiodapi.model.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Table(name = "users", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"username", "deleted_on"})})
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "role", discriminatorType = DiscriminatorType.STRING)
@SQLDelete(sql = "UPDATE users SET deleted_on = now() WHERE ID = ?")
@SQLRestriction("deleted_on is NULL")
public class AppUser implements UserDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "firstname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "firstname",
            read = "pgp_sym_decrypt(firstname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String firstname;

    @Column(name = "lastname", columnDefinition = "bytea", nullable = false)
    @ColumnTransformer(
            forColumn = "lastname",
            read = "pgp_sym_decrypt(lastname::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String lastname;

    @Column(name = "username", nullable = false, unique = true)
    private String username;

    @Column(name = "email", columnDefinition = "bytea", nullable = false, unique = true)
    @ColumnTransformer(
            forColumn = "email",
            read = "pgp_sym_decrypt(email::bytea, 'password')",
            write = "pgp_sym_encrypt(?, 'password')"
    )
    private String email;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Column(name = "password", nullable = false)
    private String password;

    @Column(name = "role", insertable = false, updatable = false, nullable = false)
    @Enumerated(EnumType.STRING)
    private Role role;

    private boolean isEnabled;

    private LocalDateTime lastPasswordUpdatedOn;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<VerificationToken> tokens = new ArrayList<>();


    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    private LocalDateTime deletedOn;


    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return role.getAuthorities();
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return isEnabled;
    }

    public boolean isAdmin() {
        return role == Role.ADMIN;
    }

    public boolean isHrbp() {
        return role == Role.HRBP;
    }

    public boolean isHr() {
        return role == Role.HR;
    }

    @PrePersist
    private void prePersist() {
        firstname = firstname.trim();
        lastname = lastname.trim();
        username = username.trim();
        username = username.toLowerCase();
        email = email.trim();
        lastPasswordUpdatedOn = LocalDateTime.now();
    }

    @PreUpdate
    private void preUpdate() {
        firstname = firstname.trim();
        lastname = lastname.trim();
        username = username.trim();
        username = username.toLowerCase();
        email = email.trim();
    }
}