package com.capgemini.trialperiodapi.model.auth;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import java.util.Date;

@Entity
@Getter
@Setter
@RequiredArgsConstructor
@Table(name = "password_reset_tokens", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"token", "code"})})
public class PasswordResetToken {

    private static final long TOKEN_EXPIRATION = 3600000; // one hour

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "token", nullable = false)
    private String token;

    @Column(name = "code", nullable = false)
    private String code;

    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private AppUser user;

    @Column(nullable = false)
    private Date expirationTime;

    public PasswordResetToken(String token, String code, AppUser user) {
        this.token = token;
        this.code = code;
        this.user = user;
        this.expirationTime = new Date(System.currentTimeMillis() + TOKEN_EXPIRATION);
    }
}