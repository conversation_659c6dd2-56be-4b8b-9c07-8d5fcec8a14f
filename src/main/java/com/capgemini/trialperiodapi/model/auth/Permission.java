package com.capgemini.trialperiodapi.model.auth;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum Permission {

    ADMIN_UPDATE("admin:update"),
    ADMIN_CREATE("admin:create"),
    ADMIN_DELETE("admin:delete"),
    ADMIN_READ("admin:read"),

    HRBP_READ("rhbp:read"),
    HRBP_UPDATE("rhbp:update"),
    HRBP_CREATE("rhbp:create"),
    HRBP_DELETE("rhbp:delete"),

    HR_READ("rh:read"),
    HR_UPDATE("rh:update"),
    HR_CREATE("rh:create"),
    HR_DELETE("rh:delete");

    @Getter
    private final String name;
}