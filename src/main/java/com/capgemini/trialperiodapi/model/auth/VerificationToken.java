package com.capgemini.trialperiodapi.model.auth;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "verification_tokens", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"token", "code"})})
public class VerificationToken implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String token;

    @Column(nullable = false)
    private String code;

    @Column(nullable = false)
    private Date expirationTime;

    private boolean revoked;

    private boolean expired;

    private static final int EXPIRATION_TIME = 10;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private AppUser user;

    @CreationTimestamp(source = SourceType.DB)
    private LocalDateTime createdOn;

    @UpdateTimestamp(source = SourceType.DB)
    private LocalDateTime lastUpdatedOn;

    public VerificationToken(String token) {
        super();
        this.token = token;
        this.expirationTime = getTokenExpirationTime();
    }

    public VerificationToken(AppUser user, String token, String code) {
        super();
        this.token = token;
        this.code = code;
        this.user = user;
        this.expirationTime = getTokenExpirationTime();
    }

    private Date getTokenExpirationTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(new Date().getTime());
        calendar.add(Calendar.MINUTE, EXPIRATION_TIME);
        return new Date(calendar.getTime().getTime());
    }
}
