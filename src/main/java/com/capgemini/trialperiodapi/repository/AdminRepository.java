package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface AdminRepository extends JpaRepository<Admin, Long>, JpaSpecificationExecutor<Admin> {
    @Query("SELECT admin FROM Admin admin " +
            "WHERE UPPER(TRIM(admin.username)) = UPPER(TRIM(:username))")
    Optional<Admin> findAdminByUsername(@Param("username") String username);

    @Query("SELECT admin FROM Admin admin WHERE admin.id = :id")
    Optional<Admin> findAdminById(@Param("id") Long id);
}