package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Collaborator;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface CollaboratorRepository extends JpaRepository<Collaborator, Long>, JpaSpecificationExecutor<Collaborator> {
    @Query("SELECT c FROM Collaborator c WHERE (c.id = :id)")
    Optional<Collaborator> findCollaboratorById(@Param("id") Long id);

    @Query("SELECT c FROM Collaborator c " +
            "WHERE (:email IS NOT NULL) AND (UPPER(TRIM(c.email)) = UPPER(TRIM(:email)))")
    Optional<Collaborator> findCollaboratorByEmail(@Param("email") String email);

    @Query("SELECT c FROM Collaborator c WHERE (c.ggid = :ggid)")
    Optional<Collaborator> findCollaboratorByGgid(@Param("ggid") String ggid);
}