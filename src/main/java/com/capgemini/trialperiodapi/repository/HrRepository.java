package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Hr;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface HrRepository extends JpaRepository<Hr, Long>, JpaSpecificationExecutor<Hr> {
    @Query("SELECT hr FROM Hr hr " +
            "WHERE UPPER(TRIM(hr.email)) = UPPER(TRIM(:email))")
    Optional<Hr> findHrByEmail(@Param("email") String email);

    @Query("SELECT hr FROM Hr hr JOIN hr.macroPeopleUnits m " +
            "WHERE UPPER(TRIM(m.name)) = UPPER(TRIM(:name))")
    List<Hr> findAllHrsByMacroPeopleUnitName(@Param("name") String macroPeopleUnitName);

    @Query("SELECT hr FROM Hr hr " +
            "WHERE (LOWER(hr.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hr.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hr.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<Hr> findHrsByKeyword(String keyword);

    @Query("SELECT hr FROM Hr hr " +
            "WHERE (LOWER(hr.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hr.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hr.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Hr> findAllHrsByKeyword(String keyword, Pageable pageable);

    @Query("SELECT hr FROM Hr hr " +
            "WHERE (UPPER(TRIM(hr.email)) = UPPER(TRIM(:email)) OR UPPER(hr.username) = UPPER(:username))")
    Optional<Hr> findHrByEmailOrUsername(@Param("email") String email, @Param("username") String username);

    @Query("SELECT hr FROM Hr hr WHERE UPPER(TRIM(hr.username)) = UPPER(TRIM(:username))")
    Optional<Hr> findHrByUsername(@Param("username") String username);
}