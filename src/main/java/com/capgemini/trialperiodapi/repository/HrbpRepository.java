package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Hrbp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface HrbpRepository extends JpaRepository<Hrbp, Long>, JpaSpecificationExecutor<Hrbp> {
    @Query("SELECT hrbp FROM Hrbp hrbp " +
            "WHERE UPPER(TRIM(hrbp.username)) = UPPER(TRIM(:username))")
    Optional<Hrbp> findHrbpByUsername(@Param("username") String username);

    @Query("SELECT hrbp FROM Hrbp hrbp " +
            "WHERE (UPPER(TRIM(hrbp.email)) = UPPER(TRIM(:email)) OR UPPER(hrbp.username) = UPPER(:username))")
    Optional<Hrbp> findHrbpByEmailOrUsername(
            @Param("email") String email, @Param("username") String username);

    @Query("SELECT hrbp FROM Hrbp hrbp JOIN hrbp.macroPeopleUnits m " +
            "WHERE UPPER(TRIM(m.name)) = UPPER(TRIM(:macroPeopleUnitName))")
    List<Hrbp> findAllHrbpsByMacroPeopleUnitName(
            @Param("macroPeopleUnitName") String macroPeopleUnitName);

    @Query("SELECT hrbp FROM Hrbp hrbp WHERE hrbp.id = :id")
    Optional<Hrbp> findHrbpById(@Param("id") Long id);

    @Query("SELECT hrbp FROM Hrbp hrbp " +
            "WHERE (LOWER(hrbp.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hrbp.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(hrbp.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<Hrbp> findHrbpsByKeyword(String keyword);

    @Query("SELECT hrbp FROM Hrbp hrbp " +
            "WHERE UPPER(TRIM(hrbp.email)) = UPPER(TRIM(:email))")
    Optional<Hrbp> findHrbpByEmail(@Param("email") String email); //move to other repo
}