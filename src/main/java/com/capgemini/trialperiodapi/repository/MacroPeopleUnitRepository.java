package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface MacroPeopleUnitRepository extends JpaRepository<MacroPeopleUnit, Long> {
    @Query("SELECT m FROM MacroPeopleUnit m WHERE UPPER(TRIM(m.name)) = UPPER(TRIM(:name))")
    Optional<MacroPeopleUnit> findMacroPeopleUnitByName(@Param("name") String name);

    @Query("SELECT m.name FROM MacroPeopleUnit m " +
            "WHERE (LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<String> findMacroPeopleUnitNamesByKeyword(@Param("keyword") String keyword);
}