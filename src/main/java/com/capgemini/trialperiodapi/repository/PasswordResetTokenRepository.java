package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.PasswordResetToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, Long> {
    @Query("SELECT p FROM PasswordResetToken p WHERE p.code = :code")
    PasswordResetToken findPasswordResetTokenByCode(@Param("code") String code);

    @Modifying
    @Query("DELETE FROM PasswordResetToken p WHERE p.user.id = :id")
    void deleteAllPasswordResetTokensByUserId(@Param("id") Long id);

    @Query("SELECT p FROM PasswordResetToken p WHERE p.user.id = :id")
    Optional<PasswordResetToken> findPasswordResetTokenByUserId(@Param("id") Long id);
}
