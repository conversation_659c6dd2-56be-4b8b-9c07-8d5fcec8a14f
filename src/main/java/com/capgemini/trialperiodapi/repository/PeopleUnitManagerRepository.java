package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface PeopleUnitManagerRepository extends JpaRepository<PeopleUnitManager, Long> {
    @Query("SELECT pum FROM PeopleUnitManager pum " +
            "WHERE UPPER(TRIM(pum.email)) = UPPER(TRIM(:email))")
    Optional<PeopleUnitManager> findPumByEmail(@Param("email") String email);

    @Query("SELECT pum FROM PeopleUnitManager pum JOIN pum.macroPeopleUnits m " +
            "WHERE UPPER(TRIM(m.name)) = UPPER(TRIM(:name))")
    List<PeopleUnitManager> findAllPumsByMacroPeopleUnitName(@Param("name") String macroPeopleUnitName);

    @Query("SELECT pum FROM PeopleUnitManager pum " +
            "WHERE (LOWER(pum.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<PeopleUnitManager> findPeopleUnitManagersByKeyword(String keyword);

    @Query("SELECT pum FROM PeopleUnitManager pum " +
            "WHERE (LOWER(pum.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<PeopleUnitManager> findPumsByKeyword(String keyword, Pageable pageable);

    @Query("SELECT pum FROM Hrbp u " +
            "JOIN u.macroPeopleUnits mpu " +
            "JOIN mpu.pums pum " +
            "WHERE u.id = :userId " +
            "AND (LOWER(pum.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<PeopleUnitManager> getPumsByConnectedHrbpId(@Param("keyword") String keyword, Long userId, Pageable pageable);

    @Query("SELECT pum FROM Hr hr " +
            "JOIN hr.macroPeopleUnits mpu " +
            "JOIN mpu.pums pum " +
            "WHERE hr.id = :userId " +
            "AND (LOWER(pum.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(pum.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<PeopleUnitManager> getPumsByConnectedHrId(@Param("keyword") String keyword, Long userId, Pageable pageable);
}