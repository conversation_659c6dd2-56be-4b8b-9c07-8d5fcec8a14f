package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.PeopleUnit;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface PeopleUnitRepository extends JpaRepository<PeopleUnit, Long> {
    @Query("SELECT pu FROM PeopleUnit pu WHERE pu.id = :id")
    Optional<PeopleUnit> findPeopleUnitById(@Param("id") Long id);

    @Query("SELECT pu FROM PeopleUnit pu WHERE UPPER(TRIM(pu.name)) = UPPER(TRIM(:name))")
    Optional<PeopleUnit> findPeopleUnitByName(@Param("name") String peopleUnitName);

    @Query("SELECT pu.name FROM PeopleUnit pu " +
            "WHERE (LOWER(pu.name) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<String> findPeopleUnitNamesByKeyword(@Param("keyword") String keyword);

    @Query("SELECT pu FROM PeopleUnit pu " +
            "WHERE UPPER(TRIM(pu.macroPeopleUnit.name)) = UPPER(TRIM(:name))")
    List<PeopleUnit> findAllPeopleUnitByMacroPeopleUnitName(@Param("name") String macroPeopleUnitName);

    @Modifying
    @Transactional
    @Query("UPDATE PeopleUnit p SET p.deletedOn = CURRENT_TIMESTAMP where p.id = :id")
    void deletePeopleUnitById(@Param("id") Long id);
}