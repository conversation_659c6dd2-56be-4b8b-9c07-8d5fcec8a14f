package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.ProjectManager;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ProjectManagerRepository extends JpaRepository<ProjectManager, Long>, JpaSpecificationExecutor<ProjectManager> {
    @Query("SELECT c FROM ProjectManager c JOIN c.projects p " +
            "WHERE UPPER(TRIM(p.name)) = UPPER(TRIM(:projectName))")
    Set<ProjectManager> findAllProjectManagersByProjectName(@Param("projectName") String projectName);

    @Query("SELECT c FROM ProjectManager c WHERE UPPER(TRIM(c.email)) = UPPER(TRIM(:email))")
    Optional<ProjectManager> findProjectManagerByEmail(@Param("email") String email);

    @Query("SELECT c FROM ProjectManager c " +
            "WHERE (LOWER(c.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(c.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(c.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<ProjectManager> findProjectManagersByKeyword(String keyword);

    @Transactional
    @Modifying
    @Query("UPDATE ProjectManager c SET c.deletedOn = FUNCTION('CURRENT_TIMESTAMP') where c.email = :email")
    void deleteProjectManagerByEmail(@Param("email") String email);

    @Query("SELECT c FROM ProjectManager c " +
            "WHERE (LOWER(c.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(c.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(c.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ProjectManager> findAllProjectManagersByKeyword(@Param("keyword") String keyword, Pageable pageable);
}