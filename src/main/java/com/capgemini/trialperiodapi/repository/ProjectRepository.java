package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ProjectRepository extends JpaRepository<Project, Long>, JpaSpecificationExecutor<Project> {
    @Query("SELECT p FROM Project p WHERE (UPPER(TRIM(p.name)) = UPPER(TRIM(:projectName)))")
    Optional<Project> findProjectByName(String projectName);

    @Query("SELECT p.name FROM Project p " +
            "WHERE (LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<String> findProjectNamesByKeyword(@Param("keyword") String keyword);
}