package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.ResourceManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ResourceManagerRepository extends JpaRepository<ResourceManager, Long> {
    @Query("SELECT rm FROM ResourceManager rm WHERE UPPER(TRIM(rm.email)) = UPPER(TRIM(:email))")
    Optional<ResourceManager> findResourceManagerByEmail(String email);

    @Query("SELECT rm FROM ResourceManager rm JOIN rm.macroPeopleUnits m " +
            "WHERE (UPPER(TRIM(m.name)) = UPPER(TRIM(:macroPeopleUnitName)))")
    List<ResourceManager> findResourceManagersByMacroPeopleUnitName(@Param("macroPeopleUnitName") String macroPeopleUnitName);

    @Query("SELECT rm FROM ResourceManager rm " +
            "WHERE (LOWER(rm.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<ResourceManager> findResourceManagersByKeyword(String keyword);

    @Query("SELECT rm FROM ResourceManager rm " +
            "WHERE (LOWER(rm.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ResourceManager> findResourceManagersByKeyword(String keyword, Pageable pageable);

    @Query("SELECT rm FROM Hrbp u " +
            "JOIN u.macroPeopleUnits mpu " +
            "JOIN mpu.resourceManagers rm " +
            "WHERE u.id = :userId " +
            "AND (LOWER(rm.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ResourceManager> getResourceManagersByConnectedHrbpId(@Param("keyword") String keyword, Long userId, Pageable pageable);

    @Query("SELECT rm FROM Hr hr " +
            "JOIN hr.macroPeopleUnits mpu " +
            "JOIN mpu.resourceManagers rm " +
            "WHERE hr.id = :userId " +
            "AND (LOWER(rm.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(rm.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ResourceManager> getResourceManagersByConnectedHrId(@Param("keyword") String keyword, Long userId, Pageable pageable);
}
