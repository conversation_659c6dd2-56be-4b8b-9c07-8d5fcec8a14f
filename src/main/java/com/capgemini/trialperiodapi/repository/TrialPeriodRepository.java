package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.TrialPeriod;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface TrialPeriodRepository extends JpaRepository<TrialPeriod, Long>, JpaSpecificationExecutor<TrialPeriod> {
    @Query("SELECT tp FROM TrialPeriod tp WHERE tp.collaborator.id = :collaboratorId")
    Optional<TrialPeriod> findTrialPeriodByCollaboratorId(@Param("collaboratorId") Long collaboratorId);
}
