package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends JpaRepository<AppUser, Long>, JpaSpecificationExecutor<AppUser> {
    @Query("SELECT user FROM AppUser user " +
            "WHERE UPPER(TRIM(user.username)) = UPPER(TRIM(:username))")
    Optional<AppUser> findUserByUsername(@Param("username") String username);

    @Query("SELECT hrbp FROM AppUser hrbp " +
            "WHERE UPPER(TRIM(hrbp.email)) = UPPER(TRIM(:email))")
    Optional<AppUser> findUserByEmail(@Param("email") String email);

    @Query("SELECT u FROM AppUser u WHERE u.id = :id")
    Optional<AppUser> findUserById(@Param("id") Long id);

    @Query("SELECT u FROM AppUser u " +
            "WHERE (LOWER(u.firstname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(u.lastname) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<AppUser> findUsersByKeyword(String keyword);
}