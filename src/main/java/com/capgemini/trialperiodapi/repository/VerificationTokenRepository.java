package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface VerificationTokenRepository extends JpaRepository<VerificationToken, Long> {
    @Query(value = """
            SELECT t FROM VerificationToken t INNER JOIN AppUser u\s
            ON t.user.id = u.id\s
            WHERE u.id = :id AND (t.expired = false AND t.revoked = false)\s
            """)
    List<VerificationToken> findValidVerificationTokensByUserId(Long id);

    @Modifying
    @Query("DELETE FROM VerificationToken vt WHERE UPPER(TRIM(vt.token)) = UPPER(TRIM(:token))")
    void deleteVerificationTokenByToken(@Param("token") String token);

    @Query("SELECT vt FROM VerificationToken vt WHERE vt.code = :code")
    Optional<VerificationToken> findVerificationTokenByCode(@Param("code") String code);

    @Query("SELECT vt FROM VerificationToken vt WHERE UPPER(TRIM(vt.token)) = UPPER(TRIM(:token))")
    Optional<VerificationToken> findVerificationTokenByToken(@Param("token") String token);

    @Modifying
    @Query("DELETE FROM VerificationToken vt WHERE vt.user.id = :token")
    void deleteAllVerificationTokensByUserId(@Param("token") Long id);

    @Query("SELECT vt FROM VerificationToken vt WHERE vt.user.id = :id")
    List<VerificationToken> findAllVerificationTokensByUserId(@Param("id") Long id);
}
