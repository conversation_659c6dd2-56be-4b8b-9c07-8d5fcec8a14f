package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public abstract class AbstractUploadService {
    protected MessageSourceUtil messageUtil;
    protected FileUtil fileUtil;

    protected abstract Integer parseCsv(MultipartFile file);

    protected Integer upload(MultipartFile file) {
        if (file.isEmpty()) {
            log.warn("AbstractUploadService::upload empty file;");
            throw new IllegalStateException(messageUtil.getMessage("exception.file.not-found"));
        }

        // validate csv format
        fileUtil.checkFileExtension(file);

        // validate UTF_8 encoding
        fileUtil.checkFileEncoding(file);

        log.debug("AbstractUploadService::upload completed;");
        return parseCsv(file);
    }
}