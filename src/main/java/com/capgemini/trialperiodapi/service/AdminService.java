package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.AdminRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.Admin;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.repository.AdminRepository;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.capgemini.trialperiodapi.util.PasswordGenerator;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.List;

@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class AdminService implements IAdminService {
    private final MessageSourceUtil messageSourceUtil;
    private final PasswordEncoder passwordEncoder;
    private final AdminRepository adminRepository;
    private final UserRepository userRepository;
    private final IEmailService emailService;
    private final IUserService userService;
    private final UserMapper userMapper;

    @Override
    public Admin getAdminById(Long id) {
        log.debug("AdminService::getAdminById fetching admin by ID '{}';", id);
        return adminRepository.findAdminById(id).orElseThrow(
                () -> {
                    log.warn("AdminService::getAdminById admin by ID '{}' not found;", id);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "user.by-email.not-found", id));
                }
        );
    }

    @Override
    public UserResponseDTO getAdminDTOById(Long adminId, Principal principal) {
        // Verify authorization
        if (!canHandleAdmin(principal, adminId)) {
            log.warn("AdminService::getAdminDTOById cannot handle admin; adminId '{}', connectedUser '{}';",
                    adminId, principal.getName());
            throw new UnauthorizedException();
        }

        Admin admin = getAdminById(adminId);
        UserResponseDTO adminDTO = userMapper.toAdminDTO(admin);
        log.info("AdminService::getAdminDTOById completed; connected user '{}', adminId '{}';", principal.getName(), adminId);
        return adminDTO;
    }

    @Override
    @Transactional
    public void saveAdmin(AdminRequestDTO request, Principal principal, String connectedUserSessionPassword) {
        String username = request.getUsername();
        String firstname = request.getFirstname();
        String lastname = request.getLastname();
        String email = request.getEmail();

        // Validate uniqueness of username and email
        userService.validateUser(username, email);

        String adminPassword = PasswordGenerator.generatePassword();
        log.debug("AdminService::saveAdmin password generated for admin; username '{}';", username);
        Admin admin = Admin.builder()
                .firstname(firstname)
                .lastname(lastname)
                .username(username)
                .email(email)
                .password(passwordEncoder.encode(adminPassword))
                .isEnabled(true)
                .build();
        adminRepository.save(admin);
        log.debug("AdminService::saveAdmin admin created; username '{}';", username);

        // Send credentials email
        emailService.sendWelcomeEmail(
                email,
                firstname,
                EmailTemplateName.WELCOME_USER,
                username,
                adminPassword,
                principal,
                connectedUserSessionPassword
        );
        log.debug("AdminService::saveAdmin credentials email sent; connected user '{}', username '{}';",
                principal.getName(), username);
        log.info("AdminService::saveAdmin completed; connected user '{}', username '{}';",
                principal.getName(), username);
    }

    @Override
    public Page<UserResponseDTO> getAllAdmins(String keyword, Pageable pageable, Principal principal) {
        Page<Admin> adminsPage = adminRepository.findAll(withFilters(keyword), pageable);
        List<UserResponseDTO> admins = adminsPage.getContent()
                .stream()
                .filter(admin -> canHandleAdmin(principal, admin.getId()))
                .map(userMapper::toAdminDTO)
                .toList();
        log.info("AdminService::getAllAdmins completed; connected user '{}', keyword '{}';",
                principal.getName(), keyword);
        return new PageImpl<>(admins, pageable, adminsPage.getTotalElements());
    }

    private Specification<Admin> withFilters(String keyword) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(true));
            if (keyword != null && !keyword.isEmpty()) {
                String likePattern = "%" + keyword.toLowerCase().trim() + "%";
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.or(
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("firstname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("lastname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("username")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("email")), likePattern)
                        ));
            }
            log.debug("AdminService::withFilters completed; keyword '{}';", keyword);
            return predicate;
        };
    }

    @Override
    public void updateAdmin(Long adminId, AdminRequestDTO request) {
        Admin existAdmin = getAdminById(adminId);

        // Validate uniqueness of username and email
        userService.validateUserForUpdate(adminId, request.getUsername(), request.getEmail());

        existAdmin.setFirstname(request.getFirstname());
        existAdmin.setLastname(request.getLastname());
        existAdmin.setUsername(request.getUsername());
        existAdmin.setEmail(request.getEmail());
        userRepository.save(existAdmin);
        log.info("AdminService::updateAdmin completed; adminId '{}';", adminId);
    }

    @Override
    @Transactional
    public void deleteAdminByUsername(String adminUsername,
                                      String connectedUserSessionPassword,
                                      Principal principal) {
        Admin admin = getAdminByUsername(adminUsername);
        // Verify authorization
        if (!canHandleAdmin(principal, admin.getId())) {
            log.warn("AdminService::deleteAdminByUsername cannot handle admin; adminUsername '{}', connectedUser '{}';",
                    adminUsername, principal.getName());
            throw new UnauthorizedException();
        }

        userRepository.delete(admin);
        log.debug("AdminService::deleteAdminByUsername admin deleted successfully;" +
                "adminUsername '{}';", adminUsername);

        // Send deleted account email
        emailService.sendAccountDeletedEmail(
                admin.getEmail(),
                admin.getFirstname(),
                principal,
                connectedUserSessionPassword
        );
        log.debug("AdminService::deleteAdminByUsername email sent to admin; adminUsername '{}';", adminUsername);
        log.info("AdminService::deleteAdminByUsername completed; adminUsername '{}'; connectedUser '{}';",
                adminUsername, principal.getName());
    }

    private boolean canHandleAdmin(Principal principal, Long adminId) {
        Admin admin = getAdminById(adminId);
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        return !connectedUserId.equals(admin.getId());
    }

    private Admin getAdminByUsername(String username) {
        log.trace("AdminService::getAdminByUsername fetching admin by username '{}';", username);
        Admin admin = adminRepository.findAdminByUsername(username).orElseThrow(
                () -> {
                    log.warn("AdminService::getAdminByUsername admin by username '{}' not found;", username);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject("user.by-email.not-found", username)
                    );
                }
        );
        log.info("AdminService::getAdminByUsername completed; adminUsername '{}';", username);
        return admin;
    }
}
