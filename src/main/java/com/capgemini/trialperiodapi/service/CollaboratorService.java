package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.CollaboratorCriteria;
import com.capgemini.trialperiodapi.dto.CollaboratorCsvRepresentation;
import com.capgemini.trialperiodapi.dto.request.CollaboratorRequestDTO;
import com.capgemini.trialperiodapi.dto.response.CollaboratorResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.CollaboratorMapper;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.CollaboratorRepository;
import com.capgemini.trialperiodapi.repository.TrialPeriodRepository;
import com.capgemini.trialperiodapi.util.DateUtil;
import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.Principal;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@Slf4j
public class CollaboratorService extends AbstractUploadService implements ICollaboratorService {
    AtomicInteger uploadedCollaboratorCount = new AtomicInteger();

    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("M/d/yyyy");
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("M/d/yyyy H:mm:ss");

    private final CollaboratorRepository collaboratorRepository;
    private final TrialPeriodRepository trialPeriodRepository;
    private final ITrialPeriodService trialPeriodService;
    private final CollaboratorMapper collaboratorMapper;
    private final IPeopleUnitService peopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final IProjectService projectService;
    private final IUserService userService;
    private final DateUtil dateUtil;

    public CollaboratorService(CollaboratorRepository collaboratorRepository,
                               TrialPeriodRepository trialPeriodRepository,
                               @Lazy ITrialPeriodService trialPeriodService,
                               CollaboratorMapper collaboratorMapper,
                               IPeopleUnitService peopleUnitService,
                               MessageSourceUtil messageSourceUtil,
                               IProjectService projectService,
                               IUserService userService,
                               FileUtil fileUtil, DateUtil dateUtil) {
        super(messageSourceUtil, fileUtil);
        this.collaboratorRepository = collaboratorRepository;
        this.trialPeriodRepository = trialPeriodRepository;
        this.trialPeriodService = trialPeriodService;
        this.collaboratorMapper = collaboratorMapper;
        this.peopleUnitService = peopleUnitService;
        this.messageSourceUtil = messageSourceUtil;
        this.projectService = projectService;
        this.userService = userService;
        this.dateUtil = dateUtil;
    }

    @Override
    public Page<CollaboratorResponseDTO> getAllCollaborators(
            CollaboratorCriteria criteria, Principal principal, Pageable pageable) {
        Page<Collaborator> collaboratorPage =
                collaboratorRepository.findAll(withFilters(criteria, principal), pageable);
        List<CollaboratorResponseDTO> collaboratorResponseDTOS = collaboratorPage.getContent()
                .stream()
                .map(collaboratorMapper::toCollaboratorResponseDTO)
                .toList();
        log.info("CollaboratorService::getAllCollaborators completed; returned collaborator(s) '{}';",
                collaboratorResponseDTOS.size());
        return new PageImpl<>(collaboratorResponseDTOS, pageable, collaboratorPage.getTotalElements());
    }

    public Specification<Collaborator> withFilters(CollaboratorCriteria criteria, Principal principal) {
        AppUser connectedUser = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(true));
            if (!isBlank(criteria.getKeyword())) {
                Join<Collaborator, Project> projectJoin = root.join("project", JoinType.LEFT);
                String likePattern = "%" + criteria.getKeyword().toLowerCase().trim() + "%";
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(root.get("ggid")), likePattern),
                        criteriaBuilder.like(criteriaBuilder.lower(root.get("email")), likePattern),
                        criteriaBuilder.and(
                                criteriaBuilder.isNotNull(projectJoin),
                                criteriaBuilder.like(criteriaBuilder.lower(projectJoin.get("name")), likePattern)
                        ),
                        criteriaBuilder.like(criteriaBuilder.lower(root.join("peopleUnit").get("name")), likePattern),
                        criteriaBuilder.like(criteriaBuilder.lower(root.get("firstname")), likePattern),
                        criteriaBuilder.like(criteriaBuilder.lower(root.get("lastname")), likePattern),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(criteriaBuilder.concat(
                                        criteriaBuilder.concat(root.get("firstname"), " "),
                                        root.get("lastname")
                                )),
                                likePattern
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(criteriaBuilder.concat(
                                        criteriaBuilder.concat(root.get("lastname"), " "),
                                        root.get("firstname")
                                )),
                                likePattern
                        )
                ));
            }
            if (connectedUser.isHrbp() || connectedUser.isHr()) {
                List<PeopleUnit> connectedUserPeopleUnits = peopleUnitService.getPeopleUnitsByConnectedUser(connectedUser);
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.and(root.get("peopleUnit").in(connectedUserPeopleUnits)));
            }
            if (!isBlank(criteria.getGlobalGrade())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("globalGrade"), criteria.getGlobalGrade()));
            }
            if (!isBlank(criteria.getLocalGrade())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("localGrade"), criteria.getLocalGrade()));
            }
            if (!isEmpty(criteria.getTrialPeriodStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.and(root.join("trialPeriod").get("status").in(criteria.getTrialPeriodStatus())));
            }
            if (!isBlank(criteria.getAssignmentStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assignmentStatus"), criteria.getAssignmentStatus()));
            }
            if (!isBlank(criteria.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), criteria.getStatus()));
            }
            log.debug("CollaboratorService::withFilters completed;");
            return predicate;
        };
    }

    @Override
    @Transactional
    public void saveCollaborator(CollaboratorRequestDTO request) {
        // Validate uniqueness of GGID and email
        validateCollaborator(request.getGgid(), request.getEmail());

        Collaborator collaborator = Collaborator.builder()
                .ggid(request.getGgid())
                .firstname(request.getFirstname())
                .lastname(request.getLastname())
                .entryDate(request.getEntryDate())
                .globalGrade(GlobalGrade.valueOf(request.getGlobalGrade()))
                .localGrade(LocalGrade.valueOf(request.getLocalGrade()))
                .status(CollaboratorStatus.valueOf(request.getStatus()))
                .peopleUnit(peopleUnitService.getPeopleUnitByName(request.getPeopleUnitName()))
                .tenureDate(request.getTenureDate())
                .interviewed(request.isInterviewed())
                .build();

        // Validate request
        if (!isBlank(request.getEmail())) {
            collaborator.setEmail(request.getEmail());
        }
        if (!isBlank(request.getAssignmentStatus())) {
            AssignmentStatus assignmentStatus = AssignmentStatus.valueOf(request.getAssignmentStatus());
            collaborator.setAssignmentStatus(assignmentStatus);
        }
        if (!isBlank(request.getProjectName())) {
            if (collaborator.getAssignmentStatus().equals(AssignmentStatus.OPPORTUNITY_0)) {
                log.warn("CollaboratorService::saveCollaborator can't assign project to Opp0 collaborator; collaborator GGID '{}';", request.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject("collaborator.is-opp-0", collaborator.fullName()));
            }
            Project project = projectService.getProjectByName(request.getProjectName());
            collaborator.setProject(project);
        }
        if (AssignmentStatus.valueOf(request.getAssignmentStatus()).equals(AssignmentStatus.SHADOW)
                || AssignmentStatus.valueOf(request.getAssignmentStatus()).equals(AssignmentStatus.FIRM_PROJECT)) {
            if (isBlank(request.getProjectName())) {
                log.warn("CollaboratorService::saveCollaborator project can't be blank for FIRM or SHADOW status; collaborator GGID '{}';", request.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject("validation.project-name-required", collaborator.fullName()));
            }
            Project project = projectService.getProjectByName(request.getProjectName());
            collaborator.setProject(project);
        }
        if (!GlobalGrade.valueOf(request.getGlobalGrade())
                .containsLocalGrade(LocalGrade.valueOf(request.getLocalGrade()))) {
            log.warn("CollaboratorService::saveCollaborator local grade and global grade are not coherent; " +
                    "collaborator GGID '{}';", request.getGgid());
            throw new IllegalStateException(messageSourceUtil.getMessage("local-grade.incoherent"));
        }

        // Save collaborator
        collaboratorRepository.saveAndFlush(collaborator);
        log.debug("CollaboratorService::saveCollaborator collaborator saved; collaborator GGID '{}';",
                collaborator.getGgid());

        trialPeriodService.handleTrialPeriod(
                collaborator,
                TrialPeriodStatus.valueOf(request.getTrialPeriodStatus()),
                request.isScorecardSent(),
                request.isCollaboratorEmailSent(),
                request.getComment()
        );

        // Turn collaborator trial period status on CONFIRMED, because being tenured means trial period confirmed
        if (request.getTenureDate() != null) {
            TrialPeriod trialPeriod = collaborator.getTrialPeriod();
            trialPeriod.setStatus(TrialPeriodStatus.CONFIRMED);
            trialPeriodRepository.save(trialPeriod);
        }
        log.info("CollaboratorService::saveCollaborator completed; collaborator GGID '{}';", collaborator.getGgid());
    }

    @Override
    public CollaboratorResponseDTO getCollaboratorDTOById(Long collaboratorId) {
        Collaborator collaborator = collaboratorRepository.findCollaboratorById(collaboratorId)
                .orElseThrow(() -> {
                    log.warn("CollaboratorService::getCollaboratorDTOById collaborator by ID not found; collaborator ID '{}';", collaboratorId);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject(
                                    "collaborator.by-id.not-found", collaboratorId
                            )
                    );
                });
        CollaboratorResponseDTO collaboratorResponse = collaboratorMapper.toCollaboratorResponseDTO(collaborator);
        log.info("CollaboratorService::getCollaboratorDTOById completed; collaborator ID '{}';", collaboratorId);
        return collaboratorResponse;
    }

    @Override
    public CollaboratorResponseDTO getCollaboratorDTOById(Long collaboratorId, Principal principal) {
        // Verify authorization
        if (!canHandleCollaborator(principal, collaboratorId)) {
            log.warn("CollaboratorService::getCollaboratorDTOById cannot handle collaborator; collaboratorId '{}', connectedUser '{}';",
                    collaboratorId, principal.getName());
            throw new UnauthorizedException();
        }

        CollaboratorResponseDTO collaborator = getCollaboratorDTOById(collaboratorId);
        log.info("CollaboratorService::getCollaboratorDTOById completed; collaboratorId '{}', connectedUser '{}';",
                collaborator.getGgid(), principal.getName());
        return collaborator;
    }

    private Collaborator getCollaboratorById(Long collaboratorId) {
        Collaborator collaborator = collaboratorRepository.findCollaboratorById(collaboratorId)
                .orElseThrow(() -> {
                    log.warn("CollaboratorService::getCollaboratorById collaborator by ID not found; collaborator ID '{}';", collaboratorId);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject("collaborator.by-id.not-found", collaboratorId));
                });
        log.info("CollaboratorService::getCollaboratorById completed; collaborator ID '{}';", collaboratorId);
        return collaborator;
    }

    @Override
    public Collaborator getCollaboratorByGgid(String collaboratorGgid) {
        Collaborator collaborator = collaboratorRepository.findCollaboratorByGgid(collaboratorGgid)
                .orElseThrow(() -> {
                    log.warn("CollaboratorService::getCollaboratorByGgid collaborator by ID not found; collaborator GGID '{}';", collaboratorGgid);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject(
                                    "collaborator.by-ggid.not-found", collaboratorGgid));
                });
        log.info("CollaboratorService::getCollaboratorByGgid completed; collaborator GGID '{}';", collaboratorGgid);
        return collaborator;
    }

    @Override
    public void updateCollaborator(Long collaboratorId, CollaboratorRequestDTO request) {
        Collaborator collaborator = getCollaboratorById(collaboratorId);

        // Validate uniqueness of GGID and email
        validateCollaboratorForUpdate(collaboratorId, request.getGgid(), request.getEmail());

        // Verify if trial period needs to be updated, it depends on if entry date is updated
        // Attention: This line must be placed before update collaborator
        boolean trialPeriodComputeNeeded = !collaborator.getEntryDate().equals(request.getEntryDate());

        collaborator.setGgid(request.getGgid());
        collaborator.setFirstname(request.getFirstname());
        collaborator.setLastname(request.getLastname());
        collaborator.setEntryDate(request.getEntryDate());
        collaborator.setTenureDate(request.getTenureDate());
        collaborator.setInterviewed(request.isInterviewed());
        collaborator.setEmail(request.getEmail());
        collaborator.setGlobalGrade(GlobalGrade.valueOf(request.getGlobalGrade()));
        collaborator.setLocalGrade(LocalGrade.valueOf(request.getLocalGrade()));
        collaborator.setStatus(CollaboratorStatus.valueOf(request.getStatus()));
        collaborator.setAssignmentStatus(AssignmentStatus.valueOf(request.getAssignmentStatus()));
        collaborator.setPeopleUnit(peopleUnitService.getPeopleUnitByName(request.getPeopleUnitName()));

        if (!isBlank(request.getProjectName())) {
            if (collaborator.getAssignmentStatus().equals(AssignmentStatus.OPPORTUNITY_0)) {
                log.warn("CollaboratorService::updateCollaborator can't assign project to Opp0 collaborator; collaborator GGID '{}';", request.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject("collaborator.is-opp-0", collaborator.fullName()));
            }
            Project project = projectService.getProjectByName(request.getProjectName());
            collaborator.setProject(project);
        } else {
            collaborator.setProject(null);
        }

        if (!GlobalGrade.valueOf(request.getGlobalGrade())
                .containsLocalGrade(LocalGrade.valueOf(request.getLocalGrade()))) {
            log.warn("CollaboratorService::updateCollaborator local grade and global grade are not coherent; " +
                    "collaborator ID '{}';", collaboratorId);
            throw new IllegalStateException(messageSourceUtil.getMessage("local-grade.incoherent"));
        }

        if (AssignmentStatus.valueOf(request.getAssignmentStatus()).equals(AssignmentStatus.SHADOW)
                || AssignmentStatus.valueOf(request.getAssignmentStatus()).equals(AssignmentStatus.FIRM_PROJECT)) {
            if (isBlank(request.getProjectName())) {
                log.warn("CollaboratorService::saveCollaborator project can't be blank for FIRM or SHADOW status; collaborator GGID '{}';", request.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject("validation.project-name-required", collaborator.fullName()));
            }
            Project project = projectService.getProjectByName(request.getProjectName());
            collaborator.setProject(project);
        }

        collaboratorRepository.saveAndFlush(collaborator);
        log.debug("CollaboratorService::updateCollaborator collaborator updated; collaborator GGID '{}';", request.getGgid());

        // Handle trial period
        if (trialPeriodComputeNeeded) {
            trialPeriodService.handleTrialPeriod(
                    collaborator,
                    TrialPeriodStatus.valueOf(request.getTrialPeriodStatus()),
                    request.isScorecardSent(),
                    request.isCollaboratorEmailSent(),
                    request.getComment()
            );
        } else {
            TrialPeriod trialPeriod = collaborator.getTrialPeriod();
            trialPeriod.setStatus(TrialPeriodStatus.valueOf(request.getTrialPeriodStatus()));
            trialPeriod.setScorecardSent(request.isScorecardSent());
            trialPeriod.setCollaboratorEmailSent(request.isCollaboratorEmailSent());
            trialPeriod.setComment(request.getComment());
            trialPeriodRepository.save(trialPeriod);
            collaborator.setTrialPeriod(trialPeriod);
            collaboratorRepository.save(collaborator);
            log.debug("CollaboratorService::updateCollaborator trial period updated; collaborator GGID '{}';", request.getGgid());
        }

        // Turn collaborator trial period status on CONFIRMED, because being tenured means trial period confirmed
        if (request.getTenureDate() != null) {
            TrialPeriod trialPeriod = collaborator.getTrialPeriod();
            trialPeriod.setStatus(TrialPeriodStatus.CONFIRMED);
            trialPeriodRepository.save(trialPeriod);
        }
        log.info("CollaboratorService::updateCollaborator completed; collaborator GGID '{}';", request.getGgid());
    }

    @Override
    public void updateCollaborator(Long collaboratorId, CollaboratorRequestDTO request, Principal principal) {
        // Verify authorization
        if (!canHandleCollaborator(principal, collaboratorId)) {
            log.warn("CollaboratorService::updateCollaborator cannot handle collaborator; collaboratorId '{}', connectedUser '{}';",
                    collaboratorId, principal.getName());
            throw new UnauthorizedException();
        }
        updateCollaborator(collaboratorId, request);
        log.info("CollaboratorService::updateCollaborator completed; collaborator GGID '{}', connectedUser '{}';",
                request.getGgid(), principal.getName());
    }

    private void validateCollaborator(String ggid, String email) {
        if (collaboratorRepository.findCollaboratorByGgid(ggid).isPresent()) {
            log.warn("CollaboratorService::validateCollaborator GGID already used '{}';", ggid);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "collaborator.ggid-already-exists", ggid));
        }
        if (!isBlank(email) && collaboratorRepository.findCollaboratorByEmail(email).isPresent()) {
            log.warn("CollaboratorService::validateCollaborator email already used '{}';", email);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "collaborator.email-already-exists", email));
        }
        log.debug("CollaboratorService::validateCollaborator completed; collaborator GGID '{}';", ggid);
    }

    private void validateCollaboratorForUpdate(Long id, String ggid, String email) {
        Optional<Collaborator> optionalCollaboratorByGgid = collaboratorRepository.findCollaboratorByGgid(ggid);
        // Verify if collaborator GGID is not already exists, excepting the processing collaborator
        if (optionalCollaboratorByGgid.isPresent() && !optionalCollaboratorByGgid.get().getId().equals(id)) {
            log.warn("CollaboratorService::validateCollaboratorForUpdate GGID already used; GGID '{}';", ggid);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "collaborator.ggid-already-exists", ggid));
        }
        if (!isBlank(email)) {
            Optional<Collaborator> optionalCollaboratorByEmail = collaboratorRepository.findCollaboratorByEmail(email);
            // Verify if collaborator email is not already exists, excepting the processing collaborator
            if (optionalCollaboratorByEmail.isPresent() && !optionalCollaboratorByEmail.get().getId().equals(id)) {
                log.warn("CollaboratorService::validateCollaboratorForUpdate email already used; Email '{}';", email);
                throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                        "user.email-already-exists", email));
            }
        }
        log.debug("CollaboratorService::validateCollaboratorForUpdate completed; collaborator GGID '{}';", ggid);
    }

    @Override
    @Transactional
    public void deleteCollaboratorByGgid(String collaboratorGgid) {
        Collaborator collaborator = getCollaboratorByGgid(collaboratorGgid);
        collaborator.setProject(null);
        collaborator.setPeopleUnit(null);
        collaboratorRepository.save(collaborator);
        trialPeriodRepository.deleteById(collaborator.getTrialPeriod().getId());
        log.debug("CollaboratorService::deleteCollaboratorByGgid delete collaborator trial period; collaborator GGID '{}';", collaborator.getGgid());
        collaboratorRepository.deleteById(collaborator.getId());
        log.debug("CollaboratorService::deleteCollaboratorByGgid completed; collaborator GGID '{}';", collaborator.getGgid());
    }

    @Override
    public void deleteCollaboratorByGgid(String collaboratorGgid, Principal principal) {
        Collaborator collaborator = getCollaboratorByGgid(collaboratorGgid);
        // Verify authorization
        if (!canHandleCollaborator(principal, collaborator.getId())) {
            log.warn("CollaboratorService::deleteCollaboratorByGgid cannot handle collaborator; collaboratorGgid '{}', connectedUser '{}';",
                    collaboratorGgid, principal.getName());
            throw new UnauthorizedException();
        }

        deleteCollaboratorByGgid(collaboratorGgid);
        log.info("CollaboratorService::deleteCollaboratorByGgid completed; collaborator GGID '{}';", collaborator.getGgid());
    }

    @Override
    public Integer uploadCollaborators(MultipartFile file) {
        return upload(file);
    }

    @Override
    protected Integer parseCsv(MultipartFile file) {
        try (Reader reader = new BufferedReader(new InputStreamReader(
                file.getInputStream(), StandardCharsets.UTF_8))) {
            ColumnPositionMappingStrategy<CollaboratorCsvRepresentation> strategy =
                    new ColumnPositionMappingStrategy<>();
            strategy.setType(CollaboratorCsvRepresentation.class);

            CsvToBean<CollaboratorCsvRepresentation> csvToBean =
                    new CsvToBeanBuilder<CollaboratorCsvRepresentation>(reader)
                            .withMappingStrategy(strategy)
                            .withIgnoreEmptyLine(true)
                            .withThrowExceptions(false)
                            .withIgnoreLeadingWhiteSpace(true)
                            .withSkipLines(1)
                            .build();

            csvToBean.parse().forEach(csvLine -> {
                        handleCollaborator(csvLine);
                        uploadedCollaboratorCount.getAndIncrement();
                    }
            );
            log.info("CollaboratorService::parseCsv completed; uploaded collaborators '{}';", uploadedCollaboratorCount.get());
            return uploadedCollaboratorCount.get();

        } catch (IOException e) {
            log.error("CollaboratorService::parseCsv failed to upload file;");
            throw new IllegalArgumentException(messageSourceUtil.getMessage("exception.file.upload-failed"));
        } finally {
            uploadedCollaboratorCount.getAndSet(0);
        }
    }

    private void handleCollaborator(CollaboratorCsvRepresentation csvLine) {
        CollaboratorRequestDTO request = CollaboratorRequestDTO.builder()
                .ggid(csvLine.getGgid())
                .firstname(csvLine.getFirstname())
                .lastname(csvLine.getLastname())
                .entryDate(dateUtil.parseDate(csvLine.getEntryDate(), dateFormatter))
                .tenureDate(dateUtil.parseDate(csvLine.getTenureDate(), dateFormatter))
                .status(CollaboratorStatus.fromLabel(csvLine.getStatus()).toString())
                .assignmentStatus(AssignmentStatus.fromLabel(csvLine.getAssignmentStatus()).toString())
                .localGrade(csvLine.getLocalGrade())
                .globalGrade(GlobalGrade.fromLocalGrade(LocalGrade.valueOf(csvLine.getLocalGrade())).toString())
                .trialPeriodStatus(TrialPeriodStatus.fromLabel(csvLine.getDecision()).toString())
                .projectName(csvLine.getProjectName())
                .peopleUnitName(csvLine.getPeopleUnitName())
                .interviewed(toBoolean(csvLine.getInterviewed()))
                .build();
        saveCollaborator(request);
        log.debug("CollaboratorService::handleCollaborator collaborator saved from csv; collaboratorGgid '{}'",
                csvLine.getGgid());

        // Handle trial period
        Collaborator collaborator = getCollaboratorByGgid(csvLine.getGgid());
        TrialPeriod trialPeriod = collaborator.getTrialPeriod();
        trialPeriod.setFirstTrialPeriodFeedbackRequestSentOn(dateUtil.parseDateTime(csvLine.getFirstTrialPeriodFeedbackRequestSentOn(), dateTimeFormatter));
        trialPeriod.setSecondTrialPeriodFeedbackRequestSentOn(dateUtil.parseDateTime(csvLine.getSecondTrialPeriodFeedbackRequestSentOn(), dateTimeFormatter));
        trialPeriod.setFirstTrialPeriodLastReminderDate(dateUtil.parseDateTime(csvLine.getFirstTrialPeriodLastReminderDate(), dateTimeFormatter));
        trialPeriod.setSecondTrialPeriodLastReminderDate(dateUtil.parseDateTime(csvLine.getSecondTrialPeriodLastReminderDate(), dateTimeFormatter));
        trialPeriod.setStatus(TrialPeriodStatus.fromLabel(csvLine.getDecision()));
        trialPeriod.setScorecardSent(toBoolean(csvLine.getScorecardSent()));
        trialPeriod.setComment(csvLine.getComment());
        trialPeriod.setCollaboratorEmailSent(toBoolean(csvLine.getCollaboratorEmailSent()));
        trialPeriodRepository.save(trialPeriod);
        log.debug("CollaboratorService::handleCollaborator trial period saved from csv; collaboratorGgid '{}'",
                csvLine.getGgid());
        log.debug("CollaboratorService::handleCollaborator completed; collaborator GGID '{}';", csvLine.getGgid());
    }

    private boolean toBoolean(String literal) {
        if (isBlank(literal)) {
            return false;
        } else if (literal.equalsIgnoreCase("NON")) {
            return false;
        } else if (literal.equalsIgnoreCase("OUI")) {
            return true;
        } else {
            log.error("CollaboratorService::toBoolean literal boolean entry is not valid; literal '{}'", literal);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject("exception.invalid-boolean", literal));
        }
    }

    private String fromBoolean(boolean scorecardSent) {
        if (scorecardSent) {
            return "OUI";
        } else {
            return "NON";
        }
    }

    @Override
    public boolean canHandleCollaborator(Principal principal, Long collaboratorId) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);
        // Except for the admin, a user can handle only the collaborators that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            Collaborator collaborator = getCollaboratorById(collaboratorId);
            Set<PeopleUnit> peopleUnits = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    peopleUnits.addAll(macroPeopleUnit.getPeopleUnits()));
            return peopleUnits.contains(collaborator.getPeopleUnit());
        } else if (connectedUser.isHr()) {
            Collaborator collaborator = getCollaboratorById(collaboratorId);
            Set<PeopleUnit> peopleUnits = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    peopleUnits.addAll(macroPeopleUnit.getPeopleUnits()));
            return peopleUnits.contains(collaborator.getPeopleUnit());
        }
        log.info("CollaboratorService::canHandleCollaborator completed; collaboratorId '{}'", collaboratorId);
        return false;
    }

    @Override
    public ByteArrayInputStream convertCollaboratorsToCSV() throws IOException {
        String[] headers = getHeaderColumns();

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(byteArrayOutputStream),
                     CSVFormat.DEFAULT.withHeader(headers))) {

            List<Collaborator> collaborators = collaboratorRepository.findAll();

            for (Collaborator collaborator : collaborators) {
                writeCollaboratorData(csvPrinter, collaborator);
            }
            csvPrinter.flush();

            ByteArrayInputStream csv = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            log.info("CollaboratorService::convertCollaboratorsToCSV completed;");
            return csv;
        }
    }

    private void writeCollaboratorData(CSVPrinter csvPrinter, Collaborator collaborator) throws IOException {
        TrialPeriod trialPeriod = collaborator.getTrialPeriod();
        String peopleUnitName = collaborator.getPeopleUnit().getName();
        String ggid = collaborator.getGgid();
        String status = collaborator.getStatus().getLabel();
        String lastName = collaborator.getLastname();
        String firstName = collaborator.getFirstname();
        String assignmentStatus = collaborator.getAssignmentStatus().getLabel();
        String localGrade = String.valueOf(collaborator.getLocalGrade());
        String trialPeriodStatus = trialPeriod.getStatus().getLabel();

        String projectName = (collaborator.getProject() != null)
                ? collaborator.getProject().getName() : "";
        String entryDate = (collaborator.getEntryDate() != null)
                ? collaborator.getEntryDate().format(dateFormatter) : "";
        String firstTrialPeriodEndDate = (trialPeriod.getFirstTrialPeriodEndDate() != null)
                ? trialPeriod.getFirstTrialPeriodEndDate().format(dateFormatter) : "";
        String firstTrialPeriodFeedbackRequestSentOn = (trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn() != null)
                ? trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn().format(dateTimeFormatter) : "";
        String firstTrialPeriodLastReminderDate = (trialPeriod.getFirstTrialPeriodLastReminderDate() != null)
                        ? trialPeriod.getFirstTrialPeriodLastReminderDate().format(dateTimeFormatter) : "";
        String secondTrialPeriodEndDate = (trialPeriod.getSecondTrialPeriodEndDate() != null)
                ? trialPeriod.getSecondTrialPeriodEndDate().format(dateFormatter) : "";
        String secondTrialPeriodFeedbackRequestSentOn = (trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn() != null)
                ? trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn().format(dateTimeFormatter) : "";
        String secondTrialPeriodLastReminderDate = (trialPeriod.getSecondTrialPeriodLastReminderDate() != null)
                ? trialPeriod.getSecondTrialPeriodLastReminderDate().format(dateTimeFormatter) : "";
        String tenureDate = (collaborator.getTenureDate() != null)
                ? collaborator.getTenureDate().format(dateFormatter) : "";

        // Decision maker isn't took in consideration in csv, it's automatically designated
        String decisionMaker = "";
        String scorecardSent = fromBoolean(trialPeriod.isScorecardSent());
        String interviewed = fromBoolean(collaborator.isInterviewed());
        String collaboratorEmailSent = fromBoolean(trialPeriod.isCollaboratorEmailSent());
        String comment = trialPeriod.getComment();

        csvPrinter.printRecord(peopleUnitName, ggid, status, lastName, firstName, localGrade, projectName, assignmentStatus, entryDate,
                firstTrialPeriodEndDate, firstTrialPeriodFeedbackRequestSentOn, firstTrialPeriodLastReminderDate, secondTrialPeriodEndDate, secondTrialPeriodFeedbackRequestSentOn,
                secondTrialPeriodLastReminderDate, trialPeriodStatus, tenureDate, decisionMaker,
                scorecardSent, interviewed, collaboratorEmailSent, comment);
    }

    private String[] getHeaderColumns() {
        return new String[]{
                "Production Unit Name", "Global Group ID", "Statut Contrat",
                "NOM", "Prénom", "Grade", "Project Name", "Statut Affectation",
                "Joining Date", "Fin 1ère PE", "Date de notification EPE 1",
                "Date de relance", "Fin 2ème PE", "Date de notification EPE 2",
                "Date de relance", "Décision", "Date de titularisation",
                "Décisionnaire", "EPE OP", "Entretien RH avec le collaborateur (Oui/NON)",
                "Remis au collaborateur", "Commentaire"};
    }
}