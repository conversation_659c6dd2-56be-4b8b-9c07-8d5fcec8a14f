package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.config.AppConfigProperties;
import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.repository.ProjectRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailAuthenticationException;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.security.Principal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.mail.javamail.MimeMessageHelper.MULTIPART_MODE_MIXED;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class EmailService implements IEmailService {
    @Value("${app.email-server-host}")
    private String emailServerHost;

    @Value("${app.email-server-protocol}")
    private String emailServerProtocol;

    @Value("${app.email-server-port}")
    private int emailServerPort;

    @Value("${app.frontend-login-url}")
    private String loginUrl;

    @Value("${app.admin-email}")
    private String testEmail;

    private final SpringTemplateEngine templateEngine;
    private final AppConfigProperties configProperties;
    private final MessageSourceUtil messageSourceUtil;
    private final ProjectRepository projectRepository;

    @Override
    public void sendActivationAccountEmail(
            String to,
            String username,
            EmailTemplateName emailTemplate,
            String url,
            String code
    ) {
        try {
            String templateName = emailTemplate.name();
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("username", username);
            properties.put("url", url);
            properties.put("code", code);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(configProperties.mailAddress());
            //helper.setTo(to);
            helper.setTo(testEmail);
            helper.setSubject(messageSourceUtil.getMessage("mail.subject.verify-email"));

            String template = templateEngine.process(templateName, context);
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.info("EmailService::sendActivationAccountEmail email sent to '{}';", to);

        } catch (MessagingException e) {
            log.error("EmailService::sendActivationAccountEmail failed to send email to user '{}';", to);
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    @Transactional
    public void sendAccountDeletedEmail(
            String recipientEmail,
            String recipientName,
            Principal principal,
            String connectedUserSessionPassword
    ) {
        String connectedUserEmail =
                ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getEmail();

        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailServerHost);
            mailSender.setPort(emailServerPort);
            mailSender.setProtocol(emailServerProtocol);
            mailSender.setUsername(connectedUserEmail);
            mailSender.setPassword(connectedUserSessionPassword);

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("username", recipientName);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(connectedUserEmail);
            //helper.setTo(recipientEmail);
            helper.setTo(testEmail);
            helper.setSubject(messageSourceUtil.getMessage("mail.subject.welcome"));

            String template = templateEngine.process(
                    String.valueOf(EmailTemplateName.DELETE_USER_ACCOUNT), context);
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.info("EmailService::sendAccountDeletedEmail email sent from '{}', to '{}';", connectedUserEmail, recipientEmail);

        } catch (MailAuthenticationException e) {
            log.warn("EmailService::sendAccountDeletedEmail wrong password");
            throw new BadCredentialsException(
                    messageSourceUtil.getMessage("password.wrong")
            );
        } catch (MailSendException | MessagingException e) {
            log.error("EmailService::sendAccountDeletedEmail failed to sent email");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        } catch (Exception e) {
            log.error("EmailService::sendAccountDeletedEmail unknown error");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    @Transactional
    @Override
    public void sendWelcomeEmail(
            String recipientEmail,
            String recipientName,
            EmailTemplateName emailTemplate,
            String username,
            String password,
            Principal principal,
            String connectedUserSessionPassword
    ) {
        String connectedUserEmail = ((AppUser)
                ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getEmail();

        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailServerHost);
            mailSender.setPort(emailServerPort);
            mailSender.setProtocol(emailServerProtocol);
            mailSender.setUsername(connectedUserEmail);
            mailSender.setPassword(connectedUserSessionPassword);

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("recipient", recipientName);
            properties.put("username", username);
            properties.put("password", password);
            properties.put("url", loginUrl);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(connectedUserEmail);
            //helper.setTo(recipientEmail);
            helper.setTo("<EMAIL>");
            helper.setSubject(messageSourceUtil.getMessage("mail.subject.welcome"));

            String template = templateEngine.process(emailTemplate.name(), context);
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.info("EmailService::sendWelcomeEmail email sent from '{}', to '{}';", connectedUserEmail, recipientEmail);

        } catch (MailAuthenticationException e) {
            log.warn("EmailService::sendWelcomeEmail wrong password", e);
            throw new BadCredentialsException(messageSourceUtil.getMessage("password.wrong")
            );
        } catch (MailSendException | MessagingException e) {
            log.error("EmailService::sendWelcomeEmail failed to sent email", e);
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        } catch (Exception e) {
            log.error("EmailService::sendWelcomeEmail unknown error", e);
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    @Override
    @Transactional
    public void sendFeedbackRequestEmail(String recipientEmail, String recipientUsername, Set<String> ccSet,
                                         List<Project> projects, String connectedUserEmail, String connectedUserSessionPassword) {
        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailServerHost);
            mailSender.setPort(emailServerPort);
            mailSender.setProtocol(emailServerProtocol);
            mailSender.setUsername(connectedUserEmail);
            mailSender.setPassword(connectedUserSessionPassword);

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("username", recipientUsername);
            properties.put("projects", projects);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(connectedUserEmail);
            helper.setTo("<EMAIL>");
            /*
            helper.setTo(email);
            ccSet.forEach(cc -> {
                try {
                    helper.addCc(cc);
                } catch (MessagingException e) {
                    e.printStackTrace();
                }
            });
            helper.addBcc(connectedUserEmail);
            */
            helper.addBcc(connectedUserEmail);
            helper.setSubject(messageSourceUtil.getMessage("mail.subject.feedback-request"));

            String template;
            if (isIndividualRequest(projects)) {
                template = templateEngine.process(
                        String.valueOf(EmailTemplateName.INDIVIDUAL_FEEDBACK_REQUEST), context);
            } else {
                template = templateEngine.process(
                        String.valueOf(EmailTemplateName.COLLECTIVE_FEEDBACK_REQUEST), context);
            }
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.info("EmailService::sendFeedbackRequestEmail email sent from '{}', to '{}', cc '{}';", connectedUserEmail, recipientEmail, ccSet);

        } catch (MailAuthenticationException e) {
            log.warn("EmailService::sendFeedbackRequestEmail wrong password");
            throw new BadCredentialsException(messageSourceUtil.getMessage("password.wrong"));
        } catch (MailSendException | MessagingException e) {
            log.error("EmailService::sendFeedbackRequestEmail failed to sent email");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        } catch (Exception e) {
            log.error("EmailService::sendFeedbackRequestEmail unknown error");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    @Override
    @Transactional
    public void sendReminderFeedbackRequestEmail(String recipientEmail, String recipientUsername, Set<String> ccSet,
                                                 List<Project> projects, String connectedUserEmail, String connectedUserSessionPassword) {
        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailServerHost);
            mailSender.setPort(emailServerPort);
            mailSender.setProtocol(emailServerProtocol);
            mailSender.setUsername(connectedUserEmail);
            mailSender.setPassword(connectedUserSessionPassword);

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("username", recipientUsername);
            properties.put("projects", projects);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(connectedUserEmail);
            helper.setTo("<EMAIL>");
            /*
            helper.setTo(email);
            ccSet.forEach(cc -> {
                try {
                    helper.addCc(cc);
                } catch (MessagingException e) {
                    e.printStackTrace();
                }
            });
            helper.addBcc(connectedUserEmail);
            */
            helper.addBcc(testEmail);
            helper.setSubject(messageSourceUtil.getMessage("mail.subject.feedback-request"));

            String template;
            template = templateEngine.process(
                    String.valueOf(EmailTemplateName.REMINDER_FEEDBACK_REQUEST), context);
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.info("EmailService::sendReminderFeedbackRequestEmail email sent from '{}', to '{}', cc '{}', bcc '{}';",
                    connectedUserEmail, recipientEmail, ccSet, connectedUserEmail);

        } catch (MailAuthenticationException e) {
            log.warn("EmailService::sendReminderFeedbackRequestEmail wrong password");
            throw new BadCredentialsException(messageSourceUtil.getMessage("password.wrong"));
        } catch (MailSendException | MessagingException e) {
            log.error("EmailService::sendReminderFeedbackRequestEmail failed to sent email");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        } catch (Exception e) {
            log.error("EmailService::sendReminderFeedbackRequestEmail unknown error");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    @Override
    public void sendForgotPasswordEmail(String toEmail, String toUsername, String resetPasswordUrl,
                                        String code, String subject, String connectedUserSessionPassword) {
        try {
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailServerHost);
            mailSender.setPort(emailServerPort);
            mailSender.setProtocol(emailServerProtocol);
            mailSender.setUsername(toEmail);
            mailSender.setPassword(connectedUserSessionPassword);

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    mimeMessage,
                    MULTIPART_MODE_MIXED,
                    UTF_8.name()
            );

            Map<String, Object> properties = new HashMap<>();
            properties.put("username", toUsername);
            properties.put("url", resetPasswordUrl);
            properties.put("code", code);

            Context context = new Context();
            context.setVariables(properties);
            helper.setFrom(toEmail);
            //helper.setTo(toEmail);
            helper.setTo("<EMAIL>");
            helper.setSubject(subject);

            String template = templateEngine.process(
                    String.valueOf(EmailTemplateName.FORGOT_PASSWORD), context);
            helper.setText(template, true);
            mailSender.send(mimeMessage);

            log.debug("EmailService::sendForgotPasswordEmail completed email sent to '{}';", toEmail);

        } catch (MailAuthenticationException e) {
            log.warn("EmailService::sendForgotPasswordEmail wrong password");
            throw new BadCredentialsException(messageSourceUtil.getMessage("password.wrong"));
        } catch (MailSendException | MessagingException e) {
            log.error("EmailService::sendForgotPasswordEmail failed to sent email");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        } catch (Exception e) {
            log.error("EmailService::sendForgotPasswordEmail unknown error");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.messaging"));
        }
    }

    private boolean isIndividualRequest(List<Project> projects) {
        AtomicInteger projectSize = new AtomicInteger(0);
        projects.forEach(project -> {
            if (projectRepository.findProjectByName(project.getName()).isPresent()) {
                projectSize.getAndIncrement();
            }
        });

        boolean isIndividual = projectSize.get() <= 1 && projects.get(0).getCollaborators().size() <= 1;
        log.debug("EmailService::isIndividualRequest is individual '{}';", isIndividual);
        return isIndividual;
    }
}