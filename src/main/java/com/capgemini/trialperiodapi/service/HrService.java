package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.repository.HrRepository;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.capgemini.trialperiodapi.util.PasswordGenerator;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class HrService implements IHrService {
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final IMacroPeopleUnitService macroPeopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final PasswordEncoder passwordEncoder;
    private final IEmailService emailService;
    private final HrRepository hrRepository;
    private final IUserService userService;
    private final UserMapper userMapper;

    @Override
    public Hr getHrByEmail(String email) {
        log.debug("HrService::getHrByEmail fetching hr by email '{}';", email);
        Hr hr = hrRepository.findHrByEmail(email)
                .orElseThrow(() -> {
                            log.warn("HrService::getHrByEmail hr by email '{}' not found;", email);
                            return new EntityNotFoundException(
                                    messageSourceUtil.getMessageWithObject("user.by-email.not-found", email)
                            );
                        }
                );
        log.info("HrService::getHrByEmail completed; hr found; email '{}';", email);
        return hr;
    }

    @Override
    public Hr getHrById(Long id) {
        log.debug("HrService::getHrById fetching hr by ID '{}';", id);
        return hrRepository.findById(id).orElseThrow(
                () -> {
                    log.warn("HrService::getHrById hr by ID '{}' not found;", id);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject("user.by-id.not-found", id)
                    );
                }
        );
    }

    @Override
    public Hr getHrByUsername(String username) {
        log.info("HrService::getHrByUsername fetching hr by username '{}';", username);
        return hrRepository.findHrByUsername(username).orElseThrow(
                () -> {
                    log.warn("HrService::getHrByUsername hr by username '{}' not found;", username);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "user.by-username.not-found", username));
                }
        );
    }

    @Override
    @Transactional
    public void saveHr(HrRequestDTO request, Principal principal, String connectedUserSessionPassword) {
        String username = request.getUsername();
        String firstname = request.getFirstname();
        String lastname = request.getLastname();
        String email = request.getEmail();

        // Validate uniqueness of username and email
        userService.validateUser(username, email);

        String password = PasswordGenerator.generatePassword();
        log.debug("HrService::saveHr password generated for hr; hrUsername '{}';", username);

        Hr hr = Hr.builder()
                .firstname(firstname)
                .lastname(lastname)
                .username(username)
                .email(email)
                .password(passwordEncoder.encode(password))
                .isEnabled(true)
                .build();
        hrRepository.save(hr);
        log.debug("HrService::saveHr admin created; hrUsername '{}';", username);

        if (!isEmpty(request.getMacroPeopleUnitNames())) {
            request.getMacroPeopleUnitNames().forEach(macroPeopleUnitName -> {
                MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
                macroPeopleUnit.getHrs().add(hr);
                macroPeopleUnitRepository.save(macroPeopleUnit);
            });
        }

        // Send credentials email
        emailService.sendWelcomeEmail(
                email,
                firstname,
                EmailTemplateName.WELCOME_USER,
                username,
                password,
                principal,
                connectedUserSessionPassword
        );
        log.debug("HrService::saveHr credentials email sent; connected user '{}', hrUsername '{}';",
                principal.getName(), username);
        log.info("HrService::saveHr completed; connected user '{}', hrUsername '{}';",
                principal.getName(), username);
    }

    @Override
    public List<UserResponseDTO> searchHrsByKeyword(String keyword) {
        List<Hr> hr = hrRepository.findHrsByKeyword(keyword);
        List<UserResponseDTO> hrsByKeyword = hr.stream().map(userMapper::toHrDTO).toList();
        log.debug("HrService::searchHrsByKeyword completed; keyword '{}'; results '{}';",
                keyword, hrsByKeyword.size());
        return hrsByKeyword;
    }

    @Override
    public List<UserResponseDTO> searchHrsByKeyword(String keyword, Principal principal) {
        List<UserResponseDTO> hrsByKeyword = searchHrsByKeyword(keyword)
                .stream().filter(hr -> canHandleHr(principal, hr.getId()))
                .toList();
        log.info("HrService::searchHrsByKeyword completed; " +
                "result size '{}', connectedUser '{}';", hrsByKeyword.size(), principal.getName());
        return hrsByKeyword;
    }

    @Override
    @Transactional
    public void deleteHrByUsername(String username, Principal principal, String connectedUserSessionPassword) {
        // Detach HRBP if not sole HRBP for Macro PU
        Hr hr = getHrByUsername(username);
        hr.getMacroPeopleUnits().forEach(macroPeopleUnit -> {
            if (macroPeopleUnit.getHrs().size() <= 1) {
                log.warn("HrService::deleteHrByUsername cannot delete macro people unit's sole hr; " +
                        "macro PU ID '{}', connectedUser '{}';", macroPeopleUnit.getId(), principal.getName());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "macro-people-unit.cannot-delete-the-only-hr-for-macro-pu", macroPeopleUnit.getName()));
            }
            macroPeopleUnit.getHrs().remove(hr);
            macroPeopleUnitRepository.save(macroPeopleUnit);
        });

        // Delete HR
        hrRepository.delete(hr);
        log.debug("HrService::deleteHrByUsername admin deleted successfully;" +
                "hrUsername '{}';", username);

        // Send deleted account email
        emailService.sendAccountDeletedEmail(
                hr.getEmail(),
                hr.getFirstname(),
                principal,
                connectedUserSessionPassword
        );
        log.debug("HrService::deleteHrByUsername email sent to admin; hrUsername '{}';", username);
        log.info("HrService::deleteHrByUsername completed; hrUsername '{}'; connectedUser '{}';",
                username, principal.getName());
    }

    @Override
    public UserResponseDTO getHrDtoById(Long hrId, Principal principal) {
        // Verify authorization
        if (!canHandleHr(principal, hrId)) {
            log.warn("HrService::getHrDTOById cannot handle hr; hrId '{}', connectedUser '{}';",
                    hrId, principal.getName());
            throw new UnauthorizedException();
        }

        Hr hr = getHrById(hrId);
        UserResponseDTO hrDTO = userMapper.toHrDTO(hr);
        log.info("HrService::getHrDTOById completed; connected user '{}', hrId '{}';", principal.getName(), hrId);
        return hrDTO;
    }

    private void updateHr(Long hrId, HrRequestDTO request) {
        Hr existHr = getHrById(hrId);

        // Validate uniqueness of username and email
        userService.validateUserForUpdate(hrId, request.getUsername(), request.getEmail());

        // Save HR
        existHr.setFirstname(request.getFirstname());
        existHr.setLastname(request.getLastname());
        existHr.setUsername(request.getUsername());
        existHr.setEmail(request.getEmail());
        hrRepository.save(existHr);

        // Handle Macro PU
        Set<MacroPeopleUnit> updatedMacroPeopleUnit = new HashSet<>();
        existHr.getMacroPeopleUnits().forEach(
                macroPeopleUnit -> {
                    updatedMacroPeopleUnit.add(macroPeopleUnit);
                    macroPeopleUnit.getHrs().remove((existHr));
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                });
        request.getMacroPeopleUnitNames().forEach(name -> {
            MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService
                    .getMacroPeopleUnitByName(name);
            macroPeopleUnit.getHrs().add(existHr);
            macroPeopleUnitRepository.save(macroPeopleUnit);
        });

        // Ensuring that every Macro People Unit has an assigned HR
        List<MacroPeopleUnit> macroPeopleUnitHavingNoHr =
                updatedMacroPeopleUnit.stream()
                        .filter(macroPeopleUnit -> isEmpty(macroPeopleUnit.getHrs())).toList();
        if (!macroPeopleUnitHavingNoHr.isEmpty()) {
            log.warn("HrService::updateHr macroPU cannot have no hr; macroPU '{}'", macroPeopleUnitHavingNoHr);
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject("macro-people-unit.cannot-delete-the-only-hrbp-for-macro-pu",
                    macroPeopleUnitHavingNoHr.get(0).getName()));
        }
        log.info("HrService::updateHr completed; hrId '{}';", hrId);
    }

    @Override
    public Page<UserResponseDTO> getAllHrs(String keyword, Pageable pageable, Principal principal) {
        Page<Hr> userPage = hrRepository.findAll(withFilters(keyword), pageable);
        List<UserResponseDTO> userDTOS = userPage.getContent()
                .stream()
                .filter(hr -> canHandleHr(principal, hr.getId()))
                .map(userMapper::toHrDTO)
                .toList();
        log.info("HrService::getAllHrs completed; connected user '{}', keyword '{}';",
                principal.getName(), keyword);
        return new PageImpl<>(userDTOS, pageable, userPage.getTotalElements());
    }

    private Specification<Hr> withFilters(String keyword) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(true));
            if (keyword != null && !keyword.isEmpty()) {
                String likePattern = "%" + keyword.toLowerCase().trim() + "%";
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.or(
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("firstname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("lastname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("username")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("email")), likePattern)
                        ));
            }
            log.debug("HrService::withFilters completed; keyword '{}';", keyword);
            return predicate;
        };
    }

    @Override
    public void updateHr(Long hrId, HrRequestDTO request, Principal principal) {
        // Verify authorization
        if (!canHandleHr(principal, hrId)) {
            log.warn("HrService::updateHr cannot handle hr; hrId '{}', connectedUser '{}';",
                    hrId, principal.getName());
            throw new UnauthorizedException();
        }

        // Update Hr
        updateHr(hrId, request);
    }

    private boolean canHandleHr(Principal principal, Long hrId) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);
        // Except for the admin, a user can handle only the hrs that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            Hr hr = getHrById(hrId);
            Set<Hr> hrs = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    hrs.addAll(macroPeopleUnit.getHrs()));
            return hrs.contains(hr);
        } else if (connectedUser.isHr()) {
            Hr hr = getHrById(hrId);
            Set<Hr> hrs = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    hrs.addAll(macroPeopleUnit.getHrs()));
            return hrs.contains(hr) && !connectedUserId.equals(hrId);
        }
        return false;
    }
}