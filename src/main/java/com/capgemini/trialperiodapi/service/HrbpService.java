package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrbpRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.repository.HrbpRepository;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.capgemini.trialperiodapi.util.PasswordGenerator;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class HrbpService implements IHrbpService {
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final IMacroPeopleUnitService macroPeopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final PasswordEncoder passwordEncoder;
    private final HrbpRepository hrbpRepository;
    private final IEmailService emailService;
    private final IUserService userService;
    private final UserMapper userMapper;

    @Override
    public Hrbp getHrbpByEmail(String email) {
        log.debug("HrbpService::getHrbpByEmail fetching hrbp by email '{}';", email);
        Hrbp hrbp = hrbpRepository.findHrbpByEmail(email).orElseThrow(() -> {
                    log.warn("HrbpService::getHrbpByEmail hrbp by email '{}' not found;", email);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "user.by-email.not-found", email));
                });
        log.info("HrbpService::getHrbpByEmail completed; hrbp found; email '{}';", email);
        return hrbp;
    }

    @Override
    public Hrbp getHrbpById(Long id) {
        log.debug("HrbpService::getHrbpById fetching hrbp by ID '{}';", id);
        Hrbp hrbp = hrbpRepository.findHrbpById(id).orElseThrow(
                () -> {
                    log.warn("HrbpService::getHrbpById hrbp by ID '{}' not found;", id);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject("user.by-id.not-found", id)
                    );
                }
        );
        log.info("HrbpService::getHrbpById completed; hrbp found; ID '{}';", id);
        return hrbp;
    }

    @Override
    public Hrbp getHrbpByUsername(String username) {
        log.info("HrbpService::getHrbpByUsername fetching hrbp by username '{}';", username);
        Hrbp hrbp = hrbpRepository.findHrbpByUsername(username)
                .orElseThrow(() -> {
                            log.warn("HrbpService::getHrbpByUsername hrbp by username '{}' not found;", username);
                            return new EntityNotFoundException(
                                    messageSourceUtil.getMessageWithObject("user.by-username.not-found", username)
                            );
                        }
                );
        log.info("HrbpService::getHrbpByUsername completed; hrbp found; username '{}';", username);
        return hrbp;
    }

    @Override
    @Transactional
    public void saveHrbp(HrbpRequestDTO request, Principal principal, String connectedUserSessionPassword) {
        String username = request.getUsername();
        String firstname = request.getFirstname();
        String lastname = request.getLastname();
        String email = request.getEmail();

        // Validate uniqueness of username and email
        userService.validateUser(username, email);

        String password = PasswordGenerator.generatePassword();
        log.debug("HrbpService::saveHrbp password generated for hrbp; hrbpUsername '{}';", username);

        // Save HRBP
        Hrbp hrbp = Hrbp.builder()
                .firstname(firstname)
                .lastname(lastname)
                .username(username)
                .email(email)
                .password(passwordEncoder.encode(password))
                .isEnabled(true)
                .build();
        hrbpRepository.save(hrbp);

        // Handle Macro PU
        if (!isEmpty(request.getMacroPeopleUnitNames())) {
            request.getMacroPeopleUnitNames().forEach(macroPeopleUnitName -> {
                MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
                macroPeopleUnit.getHrbps().add(hrbp);
                macroPeopleUnitRepository.save(macroPeopleUnit);
            });
        }

        // Send credentials email
        emailService.sendWelcomeEmail(
                email,
                firstname,
                EmailTemplateName.WELCOME_USER,
                username,
                password,
                principal,
                connectedUserSessionPassword
        );
        log.debug("HrbpService::saveHrbp credentials email sent; connected user '{}', hrbpUsername '{}';",
                principal.getName(), username);
        log.info("HrbpService::saveHrbp completed; connected user '{}', hrbpUsername '{}';",
                principal.getName(), username);
    }

    @Override
    public UserResponseDTO getHrbpDTOById(Long hrbpId, Principal principal) {
        // Verify authorization
        if (!canHandleHrbp(principal, hrbpId)) {
            log.warn("HrbpService::getHrbpDTOById cannot handle hr; hrbpId '{}', connectedUser '{}';",
                    hrbpId, principal.getName());
            throw new UnauthorizedException();
        }

        Hrbp hrbp = getHrbpById(hrbpId);
        UserResponseDTO hrbpDTO = userMapper.toHrbpDTO(hrbp);
        log.info("HrbpService::getHrbpDTOById completed; connected user '{}', hrbpId '{}';", principal.getName(), hrbpId);
        return hrbpDTO;
    }

    private List<UserResponseDTO> searchHrbpsByKeyword(String keyword) {
        List<Hrbp> hrbps = hrbpRepository.findHrbpsByKeyword(keyword);
        List<UserResponseDTO> hrbpsByKeyword = hrbps.stream().map(userMapper::toHrbpDTO).toList();
        log.debug("HrbpService::searchHrbpsByKeyword completed; keyword '{}'; results '{}';",
                keyword, hrbpsByKeyword.size());
        return hrbpsByKeyword;
    }

    @Override
    public List<UserResponseDTO> searchHrbpsByKeyword(String keyword, Principal principal) {
        List<UserResponseDTO> hrbpsByKeyword = searchHrbpsByKeyword(keyword)
                .stream().filter(hrbp -> canHandleHrbp(principal, hrbp.getId()))
                .toList();
        log.info("HrbpService::searchHrbpsByKeyword completed; " +
                "result size '{}', connectedUser '{}';", hrbpsByKeyword.size(), principal.getName());
        return hrbpsByKeyword;
    }

    @Override
    @Cacheable("hrbps")
    public Page<UserResponseDTO> getAllHrbps(String keyword, Pageable pageable, Principal principal) {
        Page<Hrbp> hrbpsPage = hrbpRepository.findAll(withFilters(keyword), pageable);
        List<UserResponseDTO> hrbps = hrbpsPage.getContent()
                .stream()
                .filter(hrbp -> canHandleHrbp(principal, hrbp.getId()))
                .map(userMapper::toHrbpDTO)
                .toList();
        log.info("HrbpService::getAllHrbps completed; connected user '{}', keyword '{}';",
                principal.getName(), keyword);
        return new PageImpl<>(hrbps, pageable, hrbpsPage.getTotalElements());
    }

    private Specification<Hrbp> withFilters(String keyword) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(true));
            if (keyword != null && !keyword.isEmpty()) {
                String likePattern = "%" + keyword.toLowerCase().trim() + "%";
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.or(
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("firstname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("lastname")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("username")), likePattern),
                                criteriaBuilder.like(criteriaBuilder.lower(root.get("email")), likePattern)
                        ));
            }
            log.debug("HrbpService::withFilters completed; keyword '{}';", keyword);
            return predicate;
        };
    }

    @Override
    public void updateHrbp(Long hrbpId, HrbpRequestDTO request) {
        Hrbp existHrbp = getHrbpById(hrbpId);

        // Validate uniqueness of username and email
        userService.validateUserForUpdate(hrbpId, request.getUsername(), request.getEmail());

        // Save HRBP
        existHrbp.setFirstname(request.getFirstname());
        existHrbp.setLastname(request.getLastname());
        existHrbp.setUsername(request.getUsername());
        existHrbp.setEmail(request.getEmail());
        hrbpRepository.save(existHrbp);

        // Handle Marco PU
        Set<MacroPeopleUnit> updatedMacroPeopleUnit = new HashSet<>();
        existHrbp.getMacroPeopleUnits().forEach(
                macroPeopleUnit -> {
                    updatedMacroPeopleUnit.add(macroPeopleUnit);
                    macroPeopleUnit.getHrbps().remove(existHrbp);
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                });
        request.getMacroPeopleUnitNames().forEach(
                name -> {
                    MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService
                            .getMacroPeopleUnitByName(name);
                    macroPeopleUnit.getHrbps().add((existHrbp));
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                });

        // Ensuring that every Macro People Unit has an assigned HRBP
        List<MacroPeopleUnit> macroPeopleUnitHavingNoHrbp = updatedMacroPeopleUnit.stream()
                .filter(macroPeopleUnit -> isEmpty(macroPeopleUnit.getHrbps()))
                .toList();
        if (!macroPeopleUnitHavingNoHrbp.isEmpty()) {
            log.warn("HrbpService::updateHrbp macroPU cannot have no hrbp; macroPU '{}'", macroPeopleUnitHavingNoHrbp);
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject("macro-people-unit.cannot-delete-the-only-hrbp-for-macro-pu",
                    macroPeopleUnitHavingNoHrbp.get(0).getName()));
        }
        log.info("HrbpService::updateHrbp completed; hrId '{}';", hrbpId);
    }

    @Override
    @Transactional
    public void deleteHrbpByUsername(String username, String connectedUserSessionPassword, Principal principal) {
        Hrbp hrbp = getHrbpByUsername(username);

        // Detach HRBP if not sole HRBP for Macro PU
        hrbp.getMacroPeopleUnits().forEach(macroPeopleUnit -> {
            if (macroPeopleUnit.getHrbps().size() <= 1) {
                log.warn("HrbpService::deleteHrbpByUsername cannot delete macro people unit's sole hrbp; " +
                        "macro PU ID '{}', connectedUser '{}';", macroPeopleUnit.getId(), principal.getName());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "macro-people-unit.cannot-delete-the-only-hrbp-for-macro-pu", macroPeopleUnit.getName()));
            }
            macroPeopleUnit.getHrbps().remove(hrbp);
            macroPeopleUnitRepository.save(macroPeopleUnit);
        });

        // Delete HRBP
        hrbpRepository.delete(hrbp);
        log.debug("HrbpService::deleteHrbpByUsername admin deleted successfully; hrbpUsername '{}';", username);

        // Send deleted account email
        emailService.sendAccountDeletedEmail(
                hrbp.getEmail(),
                hrbp.getFirstname(),
                principal,
                connectedUserSessionPassword
        );
        log.debug("HrbpService::deleteHrbpByUsername email sent to admin; hrbpUsername '{}';", username);
        log.info("HrbpService::deleteHrbpByUsername completed; hrbpUsername '{}'; connectedUser '{}';",
                username, principal.getName());
    }

    private boolean canHandleHrbp(Principal principal, Long userId) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);
        // Except for the admin, a user can handle only the hrbps that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            Hrbp hrbp = getHrbpById(userId);
            Set<AppUser> hrbps = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    hrbps.addAll(macroPeopleUnit.getHrbps()));
            return hrbps.contains(hrbp) && !connectedUserId.equals(userId);
        } else if (connectedUser.isHr()) {
            Hrbp hrbp = getHrbpById(userId);
            Set<AppUser> hrbps = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    hrbps.addAll(macroPeopleUnit.getHrbps()));
            return hrbps.contains(hrbp);
        }
        return false;
    }
}