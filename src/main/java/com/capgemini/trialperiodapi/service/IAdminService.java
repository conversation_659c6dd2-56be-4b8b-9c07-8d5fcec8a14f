package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.AdminRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.model.Admin;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.security.Principal;

public interface IAdminService {
    Admin getAdminById(Long id);

    UserResponseDTO getAdminDTOById(Long id, Principal principal);

    void saveAdmin(AdminRequestDTO request, Principal principal, String connectedUserSessionPassword);

    Page<UserResponseDTO> getAllAdmins(String keyword, Pageable pageable, Principal principal);

    void updateAdmin(Long adminId, AdminRequestDTO request);

    void deleteAdminByUsername(String username, String connectedUserSessionPassword, Principal principal);
}
