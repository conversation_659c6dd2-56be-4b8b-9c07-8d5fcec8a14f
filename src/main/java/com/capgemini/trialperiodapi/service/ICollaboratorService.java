package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.CollaboratorCriteria;
import com.capgemini.trialperiodapi.dto.request.CollaboratorRequestDTO;
import com.capgemini.trialperiodapi.dto.response.CollaboratorResponseDTO;
import com.capgemini.trialperiodapi.model.Collaborator;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.Principal;

public interface ICollaboratorService {
    Page<CollaboratorResponseDTO> getAllCollaborators(
            CollaboratorCriteria criteria, Principal principal, Pageable pageable);

    void saveCollaborator(CollaboratorRequestDTO collaborator);

    CollaboratorResponseDTO getCollaboratorDTOById(Long collaboratorId);

    CollaboratorResponseDTO getCollaboratorDTOById(Long collaboratorId, Principal principal);

    Collaborator getCollaboratorByGgid(String collaboratorGgid);

    void updateCollaborator(Long collaboratorId, CollaboratorRequestDTO request);

    void deleteCollaboratorByGgid(String collaboratorId);

    void deleteCollaboratorByGgid(String collaboratorGgid, Principal principal);

    Integer uploadCollaborators(MultipartFile file);

    void updateCollaborator(Long collaboratorId, CollaboratorRequestDTO request, Principal principal);

    boolean canHandleCollaborator(Principal principal, Long collaboratorId);

    ByteArrayInputStream convertCollaboratorsToCSV() throws IOException;
}
