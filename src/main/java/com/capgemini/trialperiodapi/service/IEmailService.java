package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import jakarta.transaction.Transactional;

import java.security.Principal;
import java.util.List;
import java.util.Set;

public interface IEmailService {

    void sendActivationAccountEmail(
            String to,
            String username,
            EmailTemplateName emailTemplate,
            String url,
            String code);

    void sendAccountDeletedEmail(
            String recipientEmail,
            String recipientName,
            Principal principal,
            String connectedUserSessionPassword);

    @Transactional
    void sendWelcomeEmail(
            String to,
            String recipient,
            EmailTemplateName emailTemplate,
            String username,
            String password,
            Principal principal,
            String connectedUserSessionPassword);

    @Transactional
    void sendFeedbackRequestEmail(String recipientEmail, String recipientUsername, Set<String> ccSet,
                                  List<Project> projects, String connectedUserEmail, String connectedUserSessionPassword);

    @Transactional
    void sendReminderFeedbackRequestEmail(String recipientEmail, String recipientUsername, Set<String> ccSet,
                                          List<Project> projects, String connectedUserEmail, String connectedUserSessionPassword);

    void sendForgotPasswordEmail(String email, String username, String resetPasswordUrl, String code,
                                 String message, String connectedUserSessionPassword);
}
