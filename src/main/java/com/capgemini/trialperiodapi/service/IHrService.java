package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.model.Hr;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.security.Principal;
import java.util.List;

public interface IHrService {
    Hr getHrByUsername(String username);

    Hr getHrById(Long id);

    Hr getHrByEmail(String email);

    void saveHr(HrRequestDTO request,
                Principal principal,
                String connectedUserSessionPassword);

    List<UserResponseDTO> searchHrsByKeyword(String keyword, Principal principal);

    List<UserResponseDTO> searchHrsByKeyword(String keyword);

    void deleteHrByUsername(String hrEmail, Principal principal, String connectedUserSessionPassword);

    UserResponseDTO getHrDtoById(Long hrId, Principal principal);

    Page<UserResponseDTO> getAllHrs(String keyword, Pageable pageable, Principal principal);

    void updateHr(Long hrId, HrRe<PERSON>D<PERSON> request, Principal principal);
}
