package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrbpRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.model.Hrbp;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.security.Principal;
import java.util.List;

public interface IHrbpService {
    Hrbp getHrbpByEmail(String email);

    Hrbp getHrbpById(Long id);

    Hrbp getHrbpByUsername(String username);

    UserResponseDTO getHrbpDTOById(Long id, Principal principal);

    @Transactional
    void saveHrbp(HrbpRequestDTO request,
                  Principal principal,
                  String connectedUserSessionPassword);

    List<UserResponseDTO> searchHrbpsByKeyword(String keyword, Principal principal);

    Page<UserResponseDTO> getAllHrbps(String keyword, Pageable pageable, Principal principal);

    void updateHrbp(Long hrbpId, HrbpRequestDTO request);

    void deleteHrbpByUsername(String username, String connectedUserSessionPassword, Principal principal);
}
