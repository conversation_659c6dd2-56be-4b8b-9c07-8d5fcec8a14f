package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.MacroPeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import jakarta.transaction.Transactional;

import java.security.Principal;
import java.util.List;

public interface IMacroPeopleUnitService {

    MacroPeopleUnit getMacroPeopleUnitByName(String macroPeopleUnitName);

    MacroPeopleUnit getMacroPeopleUnitById(Long macroPeopleUnitId);

    void saveMacroPeopleUnit(MacroPeopleUnitRequestDTO request);

    SpocsHrPumResponseDTO getSpocsHrPumByMacroPeopleUnitName(String macroPeopleUnitName);

    MacroPeopleUnit createMacroPeopleUnit(String macroPeopleUnitName);

    void updateMacroPeopleUnit(String macroPeopleUnitName, MacroPeopleUnitRequestDTO request);

    void deleteMacroPeopleUnitById(Long macroPeopleUnitId);

    void deleteMacroPeopleUnitByName(String macroPeopleUnitName);

    List<String> searchMacroPeopleUnitNamesByKeyword(String keyword, Principal principal);

    MacroPeopleUnit getMacroPeopleUnitByName(String macroPeopleUnitName, Principal principal);

    void updateMacroPeopleUnit(String macroPeopleUnitName, MacroPeopleUnitRequestDTO request, Principal principal);

    void deleteMacroPeopleUnitByName(String macroPeopleUnitName, Principal principal);
}
