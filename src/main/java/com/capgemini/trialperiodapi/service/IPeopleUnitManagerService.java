package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.PeopleUnitManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.security.Principal;
import java.util.List;

public interface IPeopleUnitManagerService {
    PeopleUnitManager getPeopleUnitManagerByEmail(String email);

    List<PeopleUnitManagerResponseDTO> searchPumsByKeyword(String keyword);

    void savePeopleUnitManager(PeopleUnitManagerRequestDTO request);

    PeopleUnitManagerResponseDTO getPumDTOById(Long pumId);

    void deletePumByEmail(String email);

    Page<PeopleUnitManagerResponseDTO> getPums(String keyword, Pageable pageable, Principal principal);

    void updatePum(Long pumId, PeopleUnitManagerRequestDTO request);

    List<PeopleUnitManagerResponseDTO> searchPumsByKeyword(String keyword, Principal principal);

    PeopleUnitManagerResponseDTO getPumDTOById(Long pumId, Principal principal);

    void deletePumByEmail(String pumEmail, Principal principal);

    void updatePum(Long pumId, PeopleUnitManagerRequestDTO request, Principal principal);
}
