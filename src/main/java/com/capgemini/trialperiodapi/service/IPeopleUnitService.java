package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.request.PeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.Principal;
import java.util.List;

public interface IPeopleUnitService {

    PeopleUnit getPeopleUnitByName(String projectName);

    List<String> searchPeopleUnitNamesByKeyword(String keyword, Principal principal);

    Integer uploadPeopleUnits(MultipartFile file, Principal principal,
                              String connectedUserSessionPassword);

    List<PeopleUnitResponseDTO> getPeopleUnitDTOsByConnectedUser(AppUser user);

    List<PeopleUnit> getPeopleUnitsByConnectedUser(AppUser user);

    List<SpocsHrPumResponseDTO> getSpocsHrPum(Principal principal);

    List<PeopleUnitResponseDTO> getAllPeopleUnits();

    List<PeopleUnitResponseDTO> getConnectedUserPeopleUnits(Principal principal);

    void savePeopleUnit(PeopleUnitRequestDTO request);

    void updatePeopleUnit(String peopleUnitName, PeopleUnitRequestDTO request);

    void deletePeopleUnitById(Long peopleUnitId);

    void deletePeopleUnitByName(String peopleUnitName);

    PeopleUnit getPeopleUnitById(Long peopleUnitId);

    PeopleUnitResponseDTO getPeopleUnitDTOById(Long peopleUnitId);

    PeopleUnitResponseDTO getPeopleUnitDTOByName(String peopleUnitName);

    void updatePeopleUnit(String peopleUnitName, PeopleUnitRequestDTO request, Principal principal);

    void deletePeopleUnitByName(String peopleUnitName, Principal principal);

    PeopleUnitResponseDTO getPeopleUnitDTOByName(String peopleUnitName, Principal principal);

    ByteArrayInputStream convertPeopleUnitsToCSV() throws IOException;
}
