package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ProjectManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import com.capgemini.trialperiodapi.model.ProjectManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface IProjectManagerService {
    ProjectManager getProjectManagerByEmail(String email);

    void saveProjectManager(ProjectManagerRequestDTO request);

    ProjectManagerResponseDTO getProjectManagerDTOById(Long projectManagerId);

    ProjectManager getProjectManagerById(Long projectManagerId);

    void deleteProjectManagerByEmail(String projectManagerEmail);
    void updateProjectManager(Long projectManagerId, ProjectManagerRequestDTO request);

    List<ProjectManagerResponseDTO> searchProjectManagersByKeyword(String keyword);

    Page<ProjectManagerResponseDTO> filterProjectManagers(String keyword, Pageable pageable);
}
