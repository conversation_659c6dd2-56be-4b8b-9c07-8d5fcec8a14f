package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ProjectRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectResponseDTO;
import com.capgemini.trialperiodapi.model.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;

public interface IProjectService {
    Project getProjectByName(String projectName);

    List<String> searchProjectNamesByKeyword(String keyword);

    Integer uploadProjects(MultipartFile file);

    Page<ProjectResponseDTO> getAllProjects(String keyword, Pageable pageable);

    void saveProject(ProjectRequestDTO request);

    void deleteProjectByProjectName(String projectId);

    void updateProject(String projectName, ProjectRequestDTO request);

    ProjectResponseDTO getProjectDTOByName(String projectName);

    ByteArrayInputStream convertProjectsToCSV() throws IOException;
}
