package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ResourceManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ResourceManagerResponseDTO;
import com.capgemini.trialperiodapi.model.ResourceManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.security.Principal;
import java.util.List;

public interface IResourceManagerService {

    void saveResourceManager(ResourceManagerRequestDTO request);

    ResourceManager getResourceManagerByEmail(String email);

    List<ResourceManagerResponseDTO> filterResourceManagers(String keyword);

    void deleteResourceManagerByEmail(String email);

    ResourceManagerResponseDTO getResourceManagerDtoById(Long resourceManagerId);

    void updateResourceManager(Long resourceManagerId, ResourceManagerRequestDTO request);

    Page<ResourceManagerResponseDTO> getResourceManager(String keyword, Pageable pageable, Principal principal);

    List<ResourceManagerResponseDTO> filterResourceManagers(String keyword, Principal principal);

    ResourceManagerResponseDTO getResourceManagerDtoById(Long resourceManagerId, Principal principal);

    void updateResourceManager(Long resourceManagerId, ResourceManagerRequestDTO request, Principal principal);

    void deleteResourceManagerByEmail(String resourceManagerEmail, Principal principal);
}