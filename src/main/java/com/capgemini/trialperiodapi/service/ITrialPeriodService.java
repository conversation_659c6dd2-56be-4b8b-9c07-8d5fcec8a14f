
package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.response.TrialPeriodResponseDTO;
import com.capgemini.trialperiodapi.model.Collaborator;
import com.capgemini.trialperiodapi.model.DecisionMaker;
import com.capgemini.trialperiodapi.model.TrialPeriod;
import com.capgemini.trialperiodapi.model.TrialPeriodStatus;
import jakarta.transaction.Transactional;

import java.security.Principal;
import java.util.List;
import java.util.Set;

public interface ITrialPeriodService {

    List<TrialPeriodResponseDTO> getImminentTrialPeriodDTOs(Principal principal);

    Integer getNotificationCount(Principal principal);

    Set<DecisionMaker> getRecipientsByTrialPeriods(List<TrialPeriod> trialPeriods);

    @Transactional
    void notifyDecisionMakerByTrialPeriodIds(
            List<Long> trialPeriodIds, String connectedUserSessionPassword, Principal principal);

    @Transactional
    void notifyDecisionMakerByNotificationId(
            Long trialPeriodId, String connectedUserSessionPassword, Principal principal);

    void markFeedbackRequestsAsSent(List<Long> trialPeriodIds);

    void hideFeedbackRequests(List<Long> trialPeriodIds);

    TrialPeriodResponseDTO getTrialPeriodByCollaboratorId(Long collaboratorId);

    TrialPeriodResponseDTO getTrialPeriodByCollaboratorId(Long collaboratorId, Principal principal);

    void handleTrialPeriod(Collaborator collaborator, TrialPeriodStatus status, boolean scorecardSent, boolean collaboratorEmailSent, String comment);
}
