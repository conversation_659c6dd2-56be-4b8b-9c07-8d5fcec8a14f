package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.dto.request.UserRequestDTO;

import java.security.Principal;

public interface IUserService {

    AppUser getUserById(Long id);

    void saveUserVerificationToken(AppUser user, String token);

    void revokeAllUserTokens(AppUser user);

    AppUser getUserByUsername(String username);

    AppUser getUserByEmail(String email);

    void deleteUserAccount(Long id);

    void validateUserForUpdate(Long id, String username, String email);

    void updateConnectedUser(UserRequestDTO request, Principal principal);

    UserResponseDTO getConnectedUser(Principal principal);

    void validateUser(String username, String email);
}
