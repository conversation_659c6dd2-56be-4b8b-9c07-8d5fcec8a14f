package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.MacroPeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.*;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.*;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.*;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@Slf4j
public class MacroPeopleUnitService implements IMacroPeopleUnitService {
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final ResourceManagerRepository resourceManagerRepository;
    private final PeopleUnitManagerMapper peopleUnitManagerMapper;
    private final ResourceManagerMapper resourceManagerMapper;
    private final MacroPeopleUnitMapper macroPeopleUnitMapper;
    private final PeopleUnitRepository peopleUnitRepository;
    private final PeopleUnitManagerRepository pumRepository;
    private final IPeopleUnitService peopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final PeopleUnitMapper peopleUnitMapper;
    private final HrbpRepository hrbpRepository;
    private final HrRepository hrRepository;
    private final UserService userService;
    private final UserMapper userMapper;

    public MacroPeopleUnitService(MacroPeopleUnitRepository macroPeopleUnitRepository,
                                  ResourceManagerRepository resourceManagerRepository,
                                  @Lazy MacroPeopleUnitMapper macroPeopleUnitMapper,
                                  PeopleUnitManagerMapper peopleUnitManagerMapper,
                                  ResourceManagerMapper resourceManagerMapper,
                                  @Lazy IPeopleUnitService peopleUnitService,
                                  PeopleUnitRepository peopleUnitRepository,
                                  PeopleUnitManagerRepository pumRepository,
                                  MessageSourceUtil messageSourceUtil,
                                  PeopleUnitMapper peopleUnitMapper,
                                  HrbpRepository hrbpRepository,
                                  @Lazy UserService userService,
                                  HrRepository hrRepository,
                                  UserMapper userMapper) {
        this.macroPeopleUnitRepository = macroPeopleUnitRepository;
        this.resourceManagerRepository = resourceManagerRepository;
        this.peopleUnitManagerMapper = peopleUnitManagerMapper;
        this.resourceManagerMapper = resourceManagerMapper;
        this.macroPeopleUnitMapper = macroPeopleUnitMapper;
        this.peopleUnitRepository = peopleUnitRepository;
        this.peopleUnitService = peopleUnitService;
        this.messageSourceUtil = messageSourceUtil;
        this.peopleUnitMapper = peopleUnitMapper;
        this.hrbpRepository = hrbpRepository;
        this.pumRepository = pumRepository;
        this.hrRepository = hrRepository;
        this.userService = userService;
        this.userMapper = userMapper;
    }

    @Override
    public MacroPeopleUnit getMacroPeopleUnitByName(String macroPeopleUnitName) {
        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName)
                .orElseThrow(() -> {
                    log.warn("MacroPeopleUnitService::getMacroPeopleUnitByName macroPU by name '{}' not found;",
                            macroPeopleUnitName);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject(
                                    "macro-people-unit.by-name.not-found", macroPeopleUnitName
                            )
                    );
                });
        log.debug("MacroPeopleUnitService::getMacroPeopleUnitByName completed; " +
                "macro PU name '{}'", macroPeopleUnitName);
        return macroPeopleUnit;
    }

    @Override
    public MacroPeopleUnit getMacroPeopleUnitByName(String macroPeopleUnitName, Principal principal) {
        // Verify authorization
        if (!canHandleMacroPeopleUnit(principal, macroPeopleUnitName)) {
            log.warn("MacroPeopleUnitService::getMacroPeopleUnitByName cannot handle macroPU; " +
                    "macroPU Name '{}', connectedUser '{}';", macroPeopleUnitName, principal.getName());
            throw new UnauthorizedException();
        }

        MacroPeopleUnit macroPeopleUnitByName = getMacroPeopleUnitByName(macroPeopleUnitName);
        log.info("MacroPeopleUnitService::getMacroPeopleUnitByName completed; " +
                "connected user '{}', macroPU Name '{}';", principal.getName(), macroPeopleUnitByName);
        return macroPeopleUnitByName;
    }

    @Override
    public void saveMacroPeopleUnit(MacroPeopleUnitRequestDTO request) {
        String macroPeopleUnitName = request.getName();
        if (macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName).isPresent()) {
            log.warn("MacroPeopleUnitService::saveMacroPeopleUnit macroPU name already exists; name '{}';",
                    macroPeopleUnitName);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessageWithObject(
                            "macro-people-unit.by-name.already-exists", macroPeopleUnitName
                    )
            );
        }

        // Verify if a people unit is already assigned to an other Macro PU, so cannot be assigned to more than one
        String firstAssignedPeopleUnitConflict = findFirstAssignedPeopleUnitConflict(
                macroPeopleUnitName, request.getPeopleUnitNames());
        if (!firstAssignedPeopleUnitConflict.isEmpty()) {
            String conflictingMacroPeopleUnit = getMacroPeopleUnitNameByPeopleUnitName(firstAssignedPeopleUnitConflict);
            log.warn("MacroPeopleUnitService::saveMacroPeopleUnit people unit '{}' already assigned to macroPU '{}';",
                    firstAssignedPeopleUnitConflict, conflictingMacroPeopleUnit);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObjects(
                    "people-unit.already-assigned-to-a-macro-pu",
                    Arrays.asList(firstAssignedPeopleUnitConflict, conflictingMacroPeopleUnit))
            );
        }

        MacroPeopleUnit newMacroPeopleUnit = macroPeopleUnitMapper.toMacroPeopleUnitEntity(request);
        macroPeopleUnitRepository.save(newMacroPeopleUnit);
        log.info("MacroPeopleUnitService::saveMacroPeopleUnit macroPU completed; saved macroPU '{}';",
                macroPeopleUnitName);
    }

    @Override
    public SpocsHrPumResponseDTO getSpocsHrPumByMacroPeopleUnitName(String macroPeopleUnitName) {
        MacroPeopleUnit macroPeopleUnit = getMacroPeopleUnitByName(macroPeopleUnitName);
        List<PeopleUnit> peopleUnits = peopleUnitRepository.findAllPeopleUnitByMacroPeopleUnitName(macroPeopleUnitName);
        List<Hr> hrs = hrRepository.findAllHrsByMacroPeopleUnitName(macroPeopleUnitName);
        List<Hrbp> hrbps = hrbpRepository.findAllHrbpsByMacroPeopleUnitName(macroPeopleUnitName);
        List<PeopleUnitManager> pums = pumRepository.findAllPumsByMacroPeopleUnitName(macroPeopleUnitName);
        List<ResourceManager> resourceManagers = resourceManagerRepository.findResourceManagersByMacroPeopleUnitName(
                macroPeopleUnitName);

        SpocsHrPumResponseDTO spocs = SpocsHrPumResponseDTO.builder()
                .macroPeopleUnitName(macroPeopleUnit.getName())
                .peopleUnits(peopleUnits.stream()
                        .map(peopleUnitMapper::toPeopleUnitDTO)
                        .toList())
                .hrs(hrs.stream()
                        .map(userMapper::toHrDTO)
                        .toList())
                .hrbps(hrbps.stream()
                        .map(userMapper::toHrbpDTO)
                        .toList())
                .pums(pums.stream()
                        .map(peopleUnitManagerMapper::toPeopleUnitManagerDTO)
                        .toList())
                .resourceManagers(resourceManagers.stream()
                        .map(resourceManagerMapper::toResourceManagerDTO)
                        .toList())
                .build();
        log.info("MacroPeopleUnitService::getSpocsHrPumByMacroPeopleUnitName completed; macroPU name '{}';",
                macroPeopleUnitName);
        return spocs;
    }

    @Override
    public void updateMacroPeopleUnit(String macroPeopleUnitName, MacroPeopleUnitRequestDTO request) {
        if (macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName).isEmpty()) {
            log.warn("MacroPeopleUnitService::updateMacroPeopleUnit macroPU by name '{}' not found;",
                    macroPeopleUnitName);
            throw new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                    "macro-people-unit.by-name.not-found", macroPeopleUnitName)
            );
        }

        // Verify if a people unit is already assigned to an other Macro PU, so cannot be assigned to more than one
        if (!findFirstAssignedPeopleUnitConflict(macroPeopleUnitName, request.getPeopleUnitNames()).isEmpty()) {
            String firstAssignedPeopleUnitConflict = findFirstAssignedPeopleUnitConflict(
                    macroPeopleUnitName, request.getPeopleUnitNames());
            String conflictingMacroPeopleUnit = getMacroPeopleUnitNameByPeopleUnitName(firstAssignedPeopleUnitConflict);

            log.warn("MacroPeopleUnitService::updateMacroPeopleUnit people unit '{}' already assigned to macroPU '{}';",
                    firstAssignedPeopleUnitConflict, conflictingMacroPeopleUnit);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObjects(
                    "people-unit.already-assigned-to-a-macro-pu",
                    Arrays.asList(firstAssignedPeopleUnitConflict, conflictingMacroPeopleUnit)
            ));
        }

        Optional<MacroPeopleUnit> macroPeopleUnitExists =
                macroPeopleUnitRepository.findMacroPeopleUnitByName(request.getName());
        // Verify if Macro PU name is not already exists, excepting the processing Macro PU
        if (macroPeopleUnitExists.isPresent() && !macroPeopleUnitExists.get().getName().equals(macroPeopleUnitName)) {
            log.warn("MacroPeopleUnitService::updateMacroPeopleUnit macroPU by name '{}' already exists;",
                    macroPeopleUnitName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "macro-people-unit.by-name.already-exists", request.getName())
            );
        }

        MacroPeopleUnit newMacroPeopleUnit = macroPeopleUnitMapper.toMacroPeopleUnitEntity(request);
        newMacroPeopleUnit.setId(getMacroPeopleUnitByName(macroPeopleUnitName).getId());
        macroPeopleUnitRepository.save(newMacroPeopleUnit);
        log.debug("MacroPeopleUnitService::updateMacroPeopleUnit completed; " +
                "macroPU saved; macro PU name '{}';", macroPeopleUnitName);
    }

    @Override
    public void updateMacroPeopleUnit(String macroPeopleUnitName,
                                      MacroPeopleUnitRequestDTO request,
                                      Principal principal) {
        // Verify authorization
        if (!canHandleMacroPeopleUnit(principal, macroPeopleUnitName)) {
            log.warn("MacroPeopleUnitService::updateMacroPeopleUnit cannot handle macroPU; " +
                    "macroPU Name '{}', connectedUser '{}';", macroPeopleUnitName, principal.getName());
            throw new UnauthorizedException();
        }

        updateMacroPeopleUnit(macroPeopleUnitName, request);
        log.info("MacroPeopleUnitService::updateMacroPeopleUnit completed; macroPU updated; " +
                "macro PU name '{}';", macroPeopleUnitName);
    }

    @Override
    public void deleteMacroPeopleUnitById(Long macroPeopleUnitId) {
        MacroPeopleUnit macroPeopleUnit = getMacroPeopleUnitById(macroPeopleUnitId);
        macroPeopleUnit.getPeopleUnits()
                .stream()
                .map(PeopleUnit::getId)
                .forEach(this::deletePeopleUnitById);
        macroPeopleUnit.setHrs(new HashSet<>());
        macroPeopleUnit.setResourceManagers(new HashSet<>());
        macroPeopleUnit.setHrbps(new HashSet<>());
        macroPeopleUnit.setPums(new HashSet<>());
        macroPeopleUnitRepository.save(macroPeopleUnit);
        macroPeopleUnitRepository.deleteById(macroPeopleUnit.getId());
        log.debug("MacroPeopleUnitService::deleteMacroPeopleUnitById completed; " +
                "deleted macro PU name '{}';", macroPeopleUnit.getName());
    }

    @Override
    public void deleteMacroPeopleUnitByName(String macroPeopleUnitName) {
        Long macroPeopleUnitId = getMacroPeopleUnitByName(macroPeopleUnitName).getId();
        deleteMacroPeopleUnitById(macroPeopleUnitId);
        log.debug("MacroPeopleUnitService::deleteMacroPeopleUnitByName completed; " +
                "deleted macro PU name '{}';", macroPeopleUnitName);
    }

    @Override
    public void deleteMacroPeopleUnitByName(String macroPeopleUnitName, Principal principal) {
        // Verify authorization
        if (!canHandleMacroPeopleUnit(principal, macroPeopleUnitName)) {
            log.warn("MacroPeopleUnitService::deleteMacroPeopleUnitByName cannot handle macroPU; " +
                    "macroPU Name '{}', connectedUser '{}';", macroPeopleUnitName, principal.getName());
            throw new UnauthorizedException();
        }

        deleteMacroPeopleUnitByName(macroPeopleUnitName);
        log.info("MacroPeopleUnitService::deleteMacroPeopleUnitByName completed; " +
                "deleted macro PU name '{}';", macroPeopleUnitName);
    }

    @Override
    public List<String> searchMacroPeopleUnitNamesByKeyword(String keyword, Principal principal) {
        List<String> macroPeopleUnitNameResults = macroPeopleUnitRepository.findMacroPeopleUnitNamesByKeyword(keyword)
                .stream()
                .filter(macroPeopleUnitName -> canHandleMacroPeopleUnit(principal, macroPeopleUnitName))
                .toList();
        log.info("MacroPeopleUnitService::searchMacroPeopleUnitNamesByKeyword completed; " +
                "result size '{}';", macroPeopleUnitNameResults.size());
        return macroPeopleUnitNameResults;
    }

    @Override
    public MacroPeopleUnit getMacroPeopleUnitById(Long macroPeopleUnitId) {
        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitRepository.findById(macroPeopleUnitId)
                .orElseThrow(() -> {
                    log.warn("MacroPeopleUnitService::getMacroPeopleUnitById macro PU by ID '{}' not found;",
                            macroPeopleUnitId);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "macro-people-unit.by-id.not-found", macroPeopleUnitId));
                });
        log.info("MacroPeopleUnitService::getMacroPeopleUnitById completed; " +
                "macro PU name '{}';", macroPeopleUnit.getName());
        return macroPeopleUnit;
    }

    @Override
    public MacroPeopleUnit createMacroPeopleUnit(String macroPeopleUnitName) {
        if (macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName).isPresent()) {
            log.warn("MacroPeopleUnitService::createMacroPeopleUnit macro PU by name '{}' already exists;",
                    macroPeopleUnitName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "macro-people-unit.by-name.already-exists", macroPeopleUnitName));
        }
        MacroPeopleUnit newMacroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName)
                .build();
        return macroPeopleUnitRepository.save(newMacroPeopleUnit);
    }

    // Verify if a people unit is already assigned to an other Macro PU, so cannot be assigned to more than one
    private String findFirstAssignedPeopleUnitConflict(String macroPeopleUnitName, Set<String> peopleUnitNames) {
        List<String> alreadyAssignedPeopleUnits = new ArrayList<>();
        peopleUnitNames.forEach(peopleUnitName -> {
            PeopleUnit peopleUnit = peopleUnitService.getPeopleUnitByName(peopleUnitName);
            MacroPeopleUnit macroPeopleUnit = peopleUnit.getMacroPeopleUnit();
            // Verify if Macro PU name is not already exists, excepting the processing Macro PU
            if (macroPeopleUnit != null && !macroPeopleUnit.getName().equals(macroPeopleUnitName)) {
                log.trace("MacroPeopleUnitService::findFirstAssignedPeopleUnitConflict " +
                        "people unit '{}' is conflicting;", peopleUnit.getName());
                alreadyAssignedPeopleUnits.add(peopleUnit.getName());
            }
        });
        String firstAssignedPeopleUnitConflict = !alreadyAssignedPeopleUnits.isEmpty() ?
                alreadyAssignedPeopleUnits.get(0) : "";
        log.debug("MacroPeopleUnitService::findFirstAssignedPeopleUnitConflict first conflicting people unit '{}';",
                firstAssignedPeopleUnitConflict);
        return firstAssignedPeopleUnitConflict;
    }

    private String getMacroPeopleUnitNameByPeopleUnitName(String peopleUnitName) {
        String macroPeopleUnitName = peopleUnitService.getPeopleUnitByName(peopleUnitName)
                .getMacroPeopleUnit().getName();
        log.debug("MacroPeopleUnitService::getMacroPeopleUnitNameByPeopleUnitName completed; " +
                "PU name '{}', macro PU name '{}';", peopleUnitName, macroPeopleUnitName);
        return macroPeopleUnitName;
    }

    @Transactional
    private void deletePeopleUnitById(Long peopleUnitId) {
        PeopleUnit peopleUnit = peopleUnitService.getPeopleUnitById(peopleUnitId);
        if (!isEmpty(peopleUnit.getCollaborators())) {
            peopleUnit.getCollaborators().forEach(collaborator -> {
                log.warn("MacroPeopleUnitService::deletePeopleUnitById people unit belongs to a collaborator; " +
                        "collaborator GGID '{}';", collaborator.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObjects(
                        "people-unit.cannot-delete-pu-belongs-to-collaborator",
                        Arrays.asList(peopleUnit.getName(), collaborator.fullName())));
            });
        }
        peopleUnit.setMacroPeopleUnit(null);
        peopleUnitRepository.save(peopleUnit);
        peopleUnitRepository.deletePeopleUnitById(peopleUnit.getId());
        log.debug("MacroPeopleUnitService::deletePeopleUnitById completed; PU ID '{}';", peopleUnitId);
    }

    private boolean canHandleMacroPeopleUnit(Principal principal, String macroPeopleUnitName) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);

        // Except for the admin, a user can handle only the Macro PUs to which they belongs
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            MacroPeopleUnit macroPeopleUnit = getMacroPeopleUnitByName(macroPeopleUnitName);
            return ((Hrbp) connectedUser).getMacroPeopleUnits().contains(macroPeopleUnit);
        } else if (connectedUser.isHr()) {
            MacroPeopleUnit macroPeopleUnit = getMacroPeopleUnitByName(macroPeopleUnitName);
            return ((Hr) connectedUser).getMacroPeopleUnits().contains(macroPeopleUnit);
        } else {
            return false;
        }
    }
}