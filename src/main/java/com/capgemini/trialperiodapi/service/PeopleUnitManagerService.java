package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.PeopleUnitManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.PeopleUnitManagerMapper;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.repository.PeopleUnitManagerRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.*;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class PeopleUnitManagerService implements IPeopleUnitManagerService {
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final PeopleUnitManagerMapper peopleUnitManagerMapper;
    private final MacroPeopleUnitService macroPeopleUnitService;
    private final PeopleUnitManagerRepository pumRepository;
    private final MessageSourceUtil messageSourceUtil;
    private final IUserService userService;

    @Override
    public PeopleUnitManager getPeopleUnitManagerByEmail(String email) {
        PeopleUnitManager peopleUnitManager = pumRepository.findPumByEmail(email)
                .orElseThrow(() -> {
                    log.warn("PeopleUnitManagerService::getPeopleUnitManagerByEmail pum by email '{}' not found;", email);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject("pum.by-email.not-found", email)
                    );
                });
        log.debug("PeopleUnitManagerService::getPeopleUnitManagerByEmail completed; pum email '{}';", email);
        return peopleUnitManager;
    }

    @Override
    public List<PeopleUnitManagerResponseDTO> searchPumsByKeyword(String keyword) {
        List<PeopleUnitManager> peopleUnitManagers = pumRepository.findPeopleUnitManagersByKeyword(keyword);
        List<PeopleUnitManagerResponseDTO> peopleUnitManagerDTOS = peopleUnitManagers.stream().map(peopleUnitManagerMapper::toPeopleUnitManagerDTO).toList();
        log.debug("PeopleUnitManagerService::searchPumsByKeyword completed; " +
                "keyword '{}', result size '{}';", keyword, peopleUnitManagerDTOS.size());
        return peopleUnitManagerDTOS;
    }

    @Override
    public List<PeopleUnitManagerResponseDTO> searchPumsByKeyword(String keyword, Principal principal) {
        List<PeopleUnitManagerResponseDTO> peopleUnitManagerDTOS = searchPumsByKeyword(keyword)
                .stream()
                .filter(peopleUnitManagerResponseDTO ->
                        canHandlePum(principal, peopleUnitManagerResponseDTO.getId()))
                .toList();
        log.info("PeopleUnitManagerService::searchPumsByKeyword completed; keyword '{}', result size '{}', " +
                "connected user: '{}';", keyword, peopleUnitManagerDTOS.size(), principal.getName());
        return peopleUnitManagerDTOS;
    }

    @Override
    public void savePeopleUnitManager(PeopleUnitManagerRequestDTO request) {
        String pumEmail = request.getEmail();

        // Validate PUM uniqueness
        Optional<PeopleUnitManager> peopleUnitManagerExists = pumRepository.findPumByEmail(pumEmail);
        if (peopleUnitManagerExists.isPresent()) {
            log.warn("PeopleUnitManagerService::savePeopleUnitManager PUM already exists; email '{}';", pumEmail);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessageWithObject("pum.by-email.already-exists", pumEmail)
            );
        }

        // Save PUM
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setFirstname(request.getFirstname());
        peopleUnitManager.setLastname(request.getLastname());
        peopleUnitManager.setEmail(pumEmail);
        pumRepository.save(peopleUnitManager);

        // Assign Macro PU
        if (!isEmpty(request.getMacroPeopleUnitNames())) {
            request.getMacroPeopleUnitNames().forEach(macroPeopleUnitName -> {
                MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
                macroPeopleUnit.getPums().add(peopleUnitManager);
                macroPeopleUnitRepository.save(macroPeopleUnit);
                log.trace("PeopleUnitManagerService::savePeopleUnitManager pum '{}' assigned to macro PU '{}'",
                        pumEmail, macroPeopleUnit.getName());
            });
        }
        log.info("PeopleUnitManagerService::savePeopleUnitManager completed; PUM by email '{}' saved;", pumEmail);
    }

    @Override
    public void deletePumByEmail(String email) {
        PeopleUnitManager pum = getPeopleUnitManagerByEmail(email);

        // Detach PUM if not sole PUM for Macro PU
        pum.getMacroPeopleUnits().forEach(macroPeopleUnit -> {
            if (macroPeopleUnit.getPums().size() <= 1) {
                log.warn("PeopleUnitManagerService::deletePumByEmail cannot delete the only PUM assigned to a Macro PU; email '{}';", email);
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "macro-people-unit.cannot-delete-the-only-pum-for-macro-pu",
                        macroPeopleUnit.getName()));
            }
            macroPeopleUnit.getPums().remove(pum);
            macroPeopleUnitRepository.save(macroPeopleUnit);
        });
        pumRepository.deleteById(pum.getId());
        log.debug("PeopleUnitManagerService::deletePumByEmail completed; pum email '{}';", email);
    }

    @Override
    public void deletePumByEmail(String email, Principal principal) {
        PeopleUnitManager peopleUnitManager = getPeopleUnitManagerByEmail(email);
        // Verify authorization
        if (!canHandlePum(principal, peopleUnitManager.getId())) {
            log.warn("PeopleUnitManagerService::deletePumByEmail cannot handle PUM; " +
                    "PUM email '{}', connectedUser '{}';", email, principal.getName());
            throw new UnauthorizedException();
        }

        deletePumByEmail(email);
        log.info("PeopleUnitManagerService::deletePumByEmail completed; PUM by email '{}' deleted;", email);
    }

    @Override
    public Page<PeopleUnitManagerResponseDTO> getPums(String keyword, Pageable pageable, Principal principal) {
        AppUser connectedUser = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();
        Page<PeopleUnitManager> pumPage = new PageImpl<>(new ArrayList<>(), pageable, 0);
        if (connectedUser.isAdmin()) {
            pumPage = pumRepository.findPumsByKeyword(keyword, pageable);
        } else if (connectedUser.isHrbp()) {
            pumPage = pumRepository.getPumsByConnectedHrbpId(keyword, connectedUser.getId(), pageable);
        } else if (connectedUser.isHr()) {
            pumPage = pumRepository.getPumsByConnectedHrId(keyword, connectedUser.getId(), pageable);
        }

        List<PeopleUnitManagerResponseDTO> pums = pumPage.getContent()
                .stream()
                .map(peopleUnitManagerMapper::toPeopleUnitManagerDTO)
                .toList();
        log.info("PeopleUnitManagerService::getPums completed; PUMs size '{}', connected user '{}'",
                pums.size(), principal.getName());
        return new PageImpl<>(pums, pageable, pumPage.getTotalElements());
    }

    @Override
    @Transactional
    public void updatePum(Long pumId, PeopleUnitManagerRequestDTO request) {
        PeopleUnitManager peopleUnitManager = getPumById(pumId);
        Optional<PeopleUnitManager> peopleUnitManagerExists = pumRepository.findPumByEmail(request.getEmail());

        // Verify if PUM email is not already exists, excepting the processing PUM
        if (peopleUnitManagerExists.isPresent() && !peopleUnitManagerExists.get().getId().equals(pumId)) {
            log.warn("PeopleUnitManagerService::updatePum email '{}' already exists; ", request.getEmail());
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "pum.by-email.already-exists", request.getEmail()));
        }

        // Save PUM
        peopleUnitManager.setFirstname(request.getFirstname());
        peopleUnitManager.setLastname(request.getLastname());
        peopleUnitManager.setEmail(request.getEmail());
        pumRepository.save(peopleUnitManager);

        // Handle Macro PU
        Set<MacroPeopleUnit> updatedMacroPeopleUnit = new HashSet<>();
        peopleUnitManager.getMacroPeopleUnits().forEach(
                macroPeopleUnit -> {
                    updatedMacroPeopleUnit.add(macroPeopleUnit);
                    macroPeopleUnit.getPums().remove(peopleUnitManager);
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                    log.trace("PeopleUnitManagerService::updatePum pum '{}' assigned to macro PU '{}'",
                            peopleUnitManager.getLastname(), macroPeopleUnit.getName());
                });
        request.getMacroPeopleUnitNames().forEach(
                name -> {
                    MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(name);
                    macroPeopleUnit.getPums().add(peopleUnitManager);
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                });

        // Ensuring that every Macro People Unit has an assigned PUM
        List<MacroPeopleUnit> macroPeopleUnitsHavingNoPum = updatedMacroPeopleUnit.stream()
                .filter(macroPeopleUnit -> isEmpty(macroPeopleUnit.getPums())).toList();
        if (!macroPeopleUnitsHavingNoPum.isEmpty()) {
            log.warn("PeopleUnitManagerService::updatePum cannot delete the only pum for macro PU '{}', pumId '{}';",
                    macroPeopleUnitsHavingNoPum.get(0), pumId);
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject("macro-people-unit.cannot-delete-the-only-pum-for-macro-pu",
                    macroPeopleUnitsHavingNoPum.get(0).getName()));
        }
    }

    @Override
    @Transactional
    public void updatePum(Long pumId, PeopleUnitManagerRequestDTO request, Principal principal) {
        // Verify authorization
        if (!canHandlePum(principal, pumId)) {
            log.warn("PeopleUnitManagerService::updatePum cannot handle PUM; " +
                    "PUM ID '{}', connectedUser '{}';", pumId, principal.getName());
            throw new UnauthorizedException();
        }

        updatePum(pumId, request);
        log.info("PeopleUnitManagerService::updatePum completed; PUM ID '{}';", pumId);
    }

    private PeopleUnitManager getPumById(Long pumId) {
        PeopleUnitManager peopleUnitManager = pumRepository.findById(pumId)
                .orElseThrow(() -> {
                            log.warn("PeopleUnitManagerService::getPumById pum by ID '{}' not found;", pumId);
                            return new EntityNotFoundException(
                                    messageSourceUtil.getMessageWithObject("pum.by-id.not-found", pumId)
                            );
                        }
                );
        log.debug("PeopleUnitManagerService::getPumById completed; PUM ID '{}';", pumId);
        return peopleUnitManager;
    }

    @Override
    public PeopleUnitManagerResponseDTO getPumDTOById(Long pumId) {
        PeopleUnitManager pum = getPumById(pumId);
        PeopleUnitManagerResponseDTO peopleUnitManagerDTO = peopleUnitManagerMapper.toPeopleUnitManagerDTO(pum);
        log.debug("PeopleUnitManagerService::getPumDTOById completed; PUM ID '{}';", pumId);
        return peopleUnitManagerDTO;
    }

    @Override
    public PeopleUnitManagerResponseDTO getPumDTOById(Long pumId, Principal principal) {
        // Verify authorization
        if (!canHandlePum(principal, pumId)) {
            log.warn("PeopleUnitManagerService::getPumDTOById cannot handle PUM; " +
                    "PUM ID '{}', connectedUser '{}';", pumId, principal.getName());
            throw new UnauthorizedException();
        }

        PeopleUnitManagerResponseDTO pumDTOById = getPumDTOById(pumId);
        log.info("PeopleUnitManagerService::getPumDTOById completed; PUM ID '{}';", pumId);
        return pumDTOById;
    }

    public boolean canHandlePum(Principal principal, Long pumId) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);
        // Except for the admin, a user can handle only the PUMs that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            PeopleUnitManager peopleUnitManager = getPumById(pumId);
            Set<PeopleUnitManager> pums = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    pums.addAll(macroPeopleUnit.getPums()));
            return pums.contains(peopleUnitManager);
        } else if (connectedUser.isHr()) {
            PeopleUnitManager peopleUnitManager = getPumById(pumId);
            Set<PeopleUnitManager> pums = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    pums.addAll(macroPeopleUnit.getPums()));
            return pums.contains(peopleUnitManager);
        }
        return false;
    }
}