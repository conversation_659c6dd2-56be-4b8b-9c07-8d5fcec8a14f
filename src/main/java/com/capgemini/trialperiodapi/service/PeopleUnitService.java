package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.MacroPeopleUnitCsvRepresentation;
import com.capgemini.trialperiodapi.dto.request.*;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.PeopleUnitMapper;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.*;
import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.capgemini.trialperiodapi.util.StringUtil;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.Principal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@Transactional
@Slf4j
public class PeopleUnitService implements IPeopleUnitService {
    public static final String LINE_SEPARATOR = "line.separator";

    AtomicInteger uploadedPeopleUnitCount = new AtomicInteger();

    private final PeopleUnitManagerRepository peopleUnitManagerRepository;
    private final ResourceManagerRepository resourceManagerRepository;
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final IResourceManagerService resourceManagerService;
    private final IMacroPeopleUnitService macroPeopleUnitService;
    private final PeopleUnitRepository peopleUnitRepository;
    private final IPeopleUnitManagerService pumService;
    private final MessageSourceUtil messageSourceUtil;
    private final PeopleUnitMapper peopleUnitMapper;
    private final HrbpRepository hrbpRepository;
    private final HrRepository hrRepository;
    private final IUserService userService;
    private final IHrbpService hrbpService;
    private final StringUtil stringUtil;
    private final IHrService hrService;
    private final FileUtil fileUtil;

    /*
    These processing variables are to handle csv merged lines,
    because OpenCSV doesn't consider logical csv lines,
    it reads the first physical line value, and let other physical ones blank,
    so these variables store the first line value of each logical line,
    until having an other not null value, meaning attend the next logical line.
    Look at getOrCreateHRBPs method and comment above !
    */
    private String processingMacroPeopleUnit = "";
    private String processingPums = "";
    private String processingResourceManager = "";
    private String processingHrbps = "";
    private String processingHrs = "";

    public PeopleUnitService(PeopleUnitManagerRepository peopleUnitManagerRepository,
                             ResourceManagerRepository resourceManagerRepository,
                             MacroPeopleUnitRepository macroPeopleUnitRepository,
                             IMacroPeopleUnitService macroPeopleUnitService,
                             IResourceManagerService resourceManagerService,
                             PeopleUnitRepository peopleUnitRepository,
                             IPeopleUnitManagerService pumService,
                             MessageSourceUtil messageSourceUtil,
                             PeopleUnitMapper peopleUnitMapper,
                             HrbpRepository hrbpRepository,
                             HrRepository hrRepository,
                             IUserService userService,
                             IHrbpService hrbpService,
                             StringUtil stringUtil,
                             IHrService hrService,
                             FileUtil fileUtil) {
        this.peopleUnitManagerRepository = peopleUnitManagerRepository;
        this.resourceManagerRepository = resourceManagerRepository;
        this.macroPeopleUnitRepository = macroPeopleUnitRepository;
        this.resourceManagerService = resourceManagerService;
        this.macroPeopleUnitService = macroPeopleUnitService;
        this.peopleUnitRepository = peopleUnitRepository;
        this.messageSourceUtil = messageSourceUtil;
        this.peopleUnitMapper = peopleUnitMapper;
        this.hrbpRepository = hrbpRepository;
        this.hrRepository = hrRepository;
        this.userService = userService;
        this.hrbpService = hrbpService;
        this.stringUtil = stringUtil;
        this.pumService = pumService;
        this.hrService = hrService;
        this.fileUtil = fileUtil;
    }

    @Override
    public PeopleUnit getPeopleUnitByName(String peopleUnitName) {
        PeopleUnit peopleUnit = peopleUnitRepository.findPeopleUnitByName(peopleUnitName)
                .orElseThrow(() -> {
                    log.warn("PeopleUnitService::getPeopleUnitByName PU by name '{}' not found;", peopleUnitName);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "people-unit.by-name.not-found", peopleUnitName)
                    );
                });
        log.debug("PeopleUnitService::getPeopleUnitByName completed; PU name '{}'", peopleUnitName);
        return peopleUnit;
    }

    @Override
    public List<String> searchPeopleUnitNamesByKeyword(String keyword, Principal principal) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);

        List<String> peopleUnitNamesByKeyword = peopleUnitRepository.findPeopleUnitNamesByKeyword(keyword);
        if (connectedUser.isHrbp() || connectedUser.isHr()) {
            Set<String> perimeter = getPeopleUnitDTOsByConnectedUser(connectedUser).stream()
                    .map(PeopleUnitResponseDTO::getName)
                    .collect(Collectors.toSet());
            List<String> filteredPeopleUnitNames = peopleUnitNamesByKeyword.stream()
                    .filter(perimeter::contains)
                    .toList();
            log.info("PeopleUnitService::searchPeopleUnitNamesByKeyword completed; result size '{}', " +
                    "connected user role '{}'", filteredPeopleUnitNames.size(), connectedUser.getRole());
            return filteredPeopleUnitNames;

        } else if (connectedUser.isAdmin()) {
            log.info("PeopleUnitService::searchPeopleUnitNamesByKeyword completed; result size '{}', " +
                    "connected user role '{}'", peopleUnitNamesByKeyword.size(), connectedUser.getRole());
            return peopleUnitNamesByKeyword;

        } else {
            log.warn("PeopleUnitService::searchPeopleUnitNamesByKeyword unauthorized;");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.unauthorized-action"));
        }
    }

    @Override
    public Integer uploadPeopleUnits(MultipartFile file, Principal principal,
                                     String connectedUserSessionPassword) {
        return parseCsv(file, principal, connectedUserSessionPassword);
    }

    private Integer parseCsv(MultipartFile file, Principal principal, String connectedUserSessionPassword) {
        if (file.isEmpty()) {
            log.warn("AbstractUploadService::upload empty file;");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.file.not-found"));
        }

        // validate csv format
        fileUtil.checkFileExtension(file);

        // validate UTF_8 encoding
        fileUtil.checkFileEncoding(file);

        // Processing csv
        try (Reader reader = new BufferedReader(new InputStreamReader(
                file.getInputStream(), StandardCharsets.UTF_8))) {
            ColumnPositionMappingStrategy<MacroPeopleUnitCsvRepresentation> strategy =
                    new ColumnPositionMappingStrategy<>();
            strategy.setType(MacroPeopleUnitCsvRepresentation.class);
            CsvToBean<MacroPeopleUnitCsvRepresentation> csvToBean =
                    new CsvToBeanBuilder<MacroPeopleUnitCsvRepresentation>(reader)
                            .withMappingStrategy(strategy)
                            .withIgnoreEmptyLine(true)
                            .withThrowExceptions(false)
                            .withIgnoreLeadingWhiteSpace(true)
                            .withSkipLines(1)
                            .build();

            csvToBean.parse().forEach(csvLine -> {
                        String macroPeopleUnitName = getMacroPeopleUnitFromCsvLine(csvLine.getMacroPeopleUnitName());
                        MacroPeopleUnit.builder()
                                .name(getOrCreateMacroPeopleUnit(macroPeopleUnitName))
                                .peopleUnits(handleAndGetPeopleUnits(
                                        macroPeopleUnitName,
                                        csvLine.getPeopleUnitNames()))
                                .hrs(getOrCreateHRs(macroPeopleUnitName, csvLine.getHrs(),
                                        principal, connectedUserSessionPassword))
                                .hrbps(getOrCreateHRBPs(macroPeopleUnitName, csvLine.getHrbps(),
                                        principal, connectedUserSessionPassword))
                                .pums(getOrCreatePUMs(macroPeopleUnitName, csvLine.getPractice()))
                                .resourceManagers(getOrCreateResourceManagers(
                                        macroPeopleUnitName,
                                        csvLine.getPractice()))
                                .build();
                    }
            );
            log.info("PeopleUnitService::parseCsv completed; uploaded people units '{}';", uploadedPeopleUnitCount.get());
            return uploadedPeopleUnitCount.get();

        } catch (IOException e) {
            log.error("PeopleUnitService::parseCsv failed to upload file;");
            throw new IllegalArgumentException(messageSourceUtil.getMessage("exception.file.upload-failed"));

        } finally {
            uploadedPeopleUnitCount.getAndSet(0);
        }
    }

    private String getMacroPeopleUnitFromCsvLine(String macroPeopleUnitName) {
        if (!isBlank(macroPeopleUnitName)) {
            processingMacroPeopleUnit = macroPeopleUnitName;
        }
        log.debug("PeopleUnitService::getMacroPeopleUnitFromCsvLine completed; macro PU name '{}';", macroPeopleUnitName);
        return processingMacroPeopleUnit;
    }

    private Set<ResourceManager> getOrCreateResourceManagers(String macroPeopleUnitName, String practice) {
        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
        String resourceManager = "";
        if (!isBlank(practice)) {
            /* Only last line of practice column is reserve for Resource Manager,
            from first line to length - 1 are for PUMs (+ we almost never find a PU with more than one PUM) */
            int resourceManagerIndex = getLinesFromCsvColumn(practice).length - 1;
            processingResourceManager = getLinesFromCsvColumn(practice)[resourceManagerIndex];
            resourceManager = processingResourceManager;
        } else {
            resourceManager = processingResourceManager;
        }

        if (getLinesFromCsvColumn(practice).length <= 1) {
            return new HashSet<>();
        }

        String firstname = stringUtil.extractFirstnameFromCsvColumn(resourceManager);
        String lastname = stringUtil.extractLastnameFromCsvColumn(resourceManager);
        String email = stringUtil.extractEmailFromCsvColumn(resourceManager);

        Optional<ResourceManager> optionalResourceManager = resourceManagerRepository.findResourceManagerByEmail(email);
        if (optionalResourceManager.isEmpty()) {
            ResourceManagerRequestDTO request = ResourceManagerRequestDTO.builder()
                    .firstname(firstname)
                    .lastname(lastname)
                    .email(email)
                    .build();
            resourceManagerService.saveResourceManager(request);
            ResourceManager manager = resourceManagerService.getResourceManagerByEmail(email);
            macroPeopleUnit.getResourceManagers().add(manager);
        } else {
            macroPeopleUnit.getResourceManagers().add(optionalResourceManager.get());
        }
        macroPeopleUnitRepository.save(macroPeopleUnit);
        log.debug("PeopleUnitService::getOrCreateResourceManagers completed; macro PU name '{}';", macroPeopleUnitName);
        return macroPeopleUnit.getResourceManagers();
    }

    private Set<PeopleUnit> handleAndGetPeopleUnits(String macroPeopleUnitName, String peopleUnitName) {
        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
        Optional<PeopleUnit> optionalPU = peopleUnitRepository.findPeopleUnitByName(peopleUnitName);
        if (optionalPU.isEmpty()) {
            PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder()
                    .name(peopleUnitName)
                    .macroPeopleUnitName(macroPeopleUnitName)
                    .build();
            savePeopleUnit(request);
            PeopleUnit peopleUnit = getPeopleUnitByName(peopleUnitName);
            macroPeopleUnit.getPeopleUnits().add(peopleUnit);
            uploadedPeopleUnitCount.getAndIncrement();
        } else {
            macroPeopleUnit.getPeopleUnits().add(optionalPU.get());
        }
        macroPeopleUnitRepository.save(macroPeopleUnit);
        log.debug("PeopleUnitService::handleAndGetPeopleUnits completed; macro PU name '{}';", macroPeopleUnitName);
        return macroPeopleUnit.getPeopleUnits();
    }

    private String[] getLinesFromCsvColumn(String hrs) {
        String[] lines = hrs.split("\\r?\\n");
        if (lines.length <= 0) {
            log.warn("PeopleUnitService::getLinesFromCsvColumn practice is empty;");
            throw new IllegalArgumentException(messageSourceUtil.getMessage("practice.empty"));
        }
        log.trace("PeopleUnitService::getLinesFromCsvColumn completed;");
        return lines;
    }

    private Set<Hrbp> getOrCreateHRBPs(String macroPeopleUnitName, String hrbps,
                                       Principal principal, String connectedUserSessionPassword) {
        /*
        - Prerequisites:
            If hrbps is not blank, that means that csv iterator is in the first physical line of the logical one.
            If hrbps is blank, that means that csv iterator is no longer in the first physical line of the logical one.
        - Logic:
            If hrbps is not blank (= in first line), we store the value in processing variable,
            if it is blank, we get from the processing variable the value of hrbps,
            which is stored when iterator was in the first physical line of the logical one.
         */
        if (!isBlank(hrbps)) {
            processingHrbps = hrbps;
        } else {
            hrbps = processingHrbps;
        }

        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
        for (String line : getLinesFromCsvColumn(hrbps)) {
            String firstname = stringUtil.extractFirstnameFromCsvColumn(line);
            String lastname = stringUtil.extractLastnameFromCsvColumn(line);
            String username = stringUtil.generateUsernameFromFirstnameAndLastname(firstname, lastname);
            String email = stringUtil.extractEmailFromCsvColumn(line);

            Optional<Hrbp> optionalHrbp = hrbpRepository.findHrbpByEmailOrUsername(email, username);
            if (optionalHrbp.isEmpty()) {
                HrbpRequestDTO request = HrbpRequestDTO.builder()
                        .firstname(firstname)
                        .lastname(lastname)
                        .username(username)
                        .email(email)
                        .build();
                hrbpService.saveHrbp(request, principal, connectedUserSessionPassword);
                /* saveHrbp method sends email from connected user email to the new HRBP,
                 considering the 5 emails per minute limit for outlook email server from the same email,
                 we should leave an interval of 12 seconds (60 sec / 5 emails) before sending the next. */
                try {
                    Thread.sleep(12000);
                } catch (InterruptedException e) {
                    log.warn("PeopleUnitService::getOrCreateHRBPs interrupted thread;");
                    Thread.currentThread().interrupt();
                }
                macroPeopleUnit.getHrbps().add(hrbpService.getHrbpByEmail(email));
            } else {
                macroPeopleUnit.getHrbps().add(optionalHrbp.get());
            }
            macroPeopleUnitRepository.save(macroPeopleUnit);
        }
        log.debug("PeopleUnitService::getOrCreateHRBPs completed; macro PU name '{}';", macroPeopleUnitName);
        return macroPeopleUnit.getHrbps();
    }

    private Set<Hr> getOrCreateHRs(String macroPeopleUnitName, String hrs,
                                   Principal principal, String connectedUserSessionPassword) {
        if (!isBlank(hrs)) {
            processingHrs = hrs;
        } else {
            hrs = processingHrs;
        }

        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);

        for (String line : getLinesFromCsvColumn(hrs)) {
            String firstname = stringUtil.extractFirstnameFromCsvColumn(line);
            String lastname = stringUtil.extractLastnameFromCsvColumn(line);
            String username = stringUtil.generateUsernameFromFirstnameAndLastname(firstname, lastname);
            String email = stringUtil.extractEmailFromCsvColumn(line);

            Optional<Hr> optionalHr = hrRepository.findHrByEmailOrUsername(email, username);

            if (optionalHr.isEmpty()) {
                HrRequestDTO request = HrRequestDTO.builder()
                        .firstname(firstname)
                        .lastname(lastname)
                        .username(username)
                        .email(email)
                        .build();
                hrService.saveHr(request, principal, connectedUserSessionPassword);

                /* saveHr method sends email from connected user email to the new HR,
                 considering the 5 emails per minute limit for outlook email server from the same email,
                 we should leave an interval of 12 seconds (60 sec / 5 emails) before sending the next. */
                try {
                    Thread.sleep(12000);
                } catch (InterruptedException e) {
                    log.warn("PeopleUnitService::getOrCreateHRs interrupted thread;");
                    Thread.currentThread().interrupt();
                }
                macroPeopleUnit.getHrs().add(hrService.getHrByEmail(email));
            } else {
                macroPeopleUnit.getHrs().add(optionalHr.get());
            }
            macroPeopleUnitRepository.save(macroPeopleUnit);
        }
        log.debug("PeopleUnitService::getOrCreateHRs completed; macro PU name '{}';", macroPeopleUnitName);
        return macroPeopleUnit.getHrs();
    }

    private Set<PeopleUnitManager> getOrCreatePUMs(String macroPeopleUnitName, String practice) {
        String pum = "";
        if (!isBlank(practice)) {
            processingPums = getLinesFromCsvColumn(practice)[0];
            pum = processingPums;
        } else {
            pum = processingPums;
        }

        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);

        String firstname = stringUtil.extractFirstnameFromCsvColumn(pum);
        String lastname = stringUtil.extractLastnameFromCsvColumn(pum);
        String email = stringUtil.extractEmailFromCsvColumn(pum);

        Optional<PeopleUnitManager> optionalPum = peopleUnitManagerRepository.findPumByEmail(email);
        if (optionalPum.isEmpty()) {
            PeopleUnitManagerRequestDTO request = PeopleUnitManagerRequestDTO.builder()
                    .firstname(firstname)
                    .lastname(lastname)
                    .email(email)
                    .build();
            pumService.savePeopleUnitManager(request);
            PeopleUnitManager manager = pumService.getPeopleUnitManagerByEmail(request.getEmail());
            macroPeopleUnit.getPums().add(manager);
        } else {
            macroPeopleUnit.getPums().add(optionalPum.get());
        }
        macroPeopleUnitRepository.save(macroPeopleUnit);
        log.debug("PeopleUnitService::getOrCreatePUMs completed; macro PU name '{}';", macroPeopleUnitName);
        return macroPeopleUnit.getPums();
    }

    private String getOrCreateMacroPeopleUnit(String macroPeopleUnitName) {
        if (macroPeopleUnitName.contains("/") || macroPeopleUnitName.contains("\\")) {
            throw new IllegalStateException(
                    messageSourceUtil.getMessage("validation.name-no-slash")
            );
        }

        if (!isBlank(macroPeopleUnitName)) {
            processingMacroPeopleUnit = macroPeopleUnitName;
        } else {
            macroPeopleUnitName = processingMacroPeopleUnit;
        }

        String returnMacroPeopleUnitName;
        if (macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName).isPresent()) {
            returnMacroPeopleUnitName = macroPeopleUnitName;
        } else {
            returnMacroPeopleUnitName = macroPeopleUnitService.createMacroPeopleUnit(macroPeopleUnitName).getName();
        }
        log.debug("PeopleUnitService::getOrCreateMacroPeopleUnit completed; macro PU name '{}';", macroPeopleUnitName);
        return returnMacroPeopleUnitName;
    }

    @Override
    public List<PeopleUnitResponseDTO> getAllPeopleUnits() {
        List<PeopleUnitResponseDTO> peopleUnitResponseDTOS = peopleUnitRepository.findAll()
                .stream()
                .map(peopleUnitMapper::toPeopleUnitDTO)
                .toList();
        log.debug("PeopleUnitService::getAllPeopleUnits completed; people units size '{}';",
                peopleUnitResponseDTOS.size());
        return peopleUnitResponseDTOS;
    }

    @Override
    public List<PeopleUnitResponseDTO> getPeopleUnitDTOsByConnectedUser(AppUser user) {
        List<PeopleUnitResponseDTO> peopleUnits = new ArrayList<>();
        Set<MacroPeopleUnit> macroPeopleUnits = new HashSet<>();

        if (user.isHrbp()) {
            macroPeopleUnits = hrbpService.getHrbpById(user.getId()).getMacroPeopleUnits();
        } else if (user.isHr()) {
            macroPeopleUnits = hrService.getHrById(user.getId()).getMacroPeopleUnits();
        }

        macroPeopleUnits.forEach(macroPeopleUnit -> peopleUnits.addAll(macroPeopleUnit.getPeopleUnits()
                .stream()
                .map(peopleUnitMapper::toPeopleUnitDTO)
                .toList()));

        log.debug("PeopleUnitService::getPeopleUnitDTOsByConnectedUser completed; people units size '{}';",
                peopleUnits.size());
        return peopleUnits;
    }

    @Override
    public List<PeopleUnit> getPeopleUnitsByConnectedUser(AppUser user) {
        List<PeopleUnit> peopleUnits = new ArrayList<>();
        Set<MacroPeopleUnit> macroPeopleUnits = new HashSet<>();

        if (user.isHrbp()) {
            macroPeopleUnits = hrbpService.getHrbpById(user.getId())
                    .getMacroPeopleUnits();
        } else if (user.isHr()) {
            macroPeopleUnits = hrService.getHrById(user.getId())
                    .getMacroPeopleUnits();
        }

        macroPeopleUnits.forEach(macroPeopleUnit -> peopleUnits.addAll(macroPeopleUnit.getPeopleUnits()));
        log.debug("PeopleUnitService::getPeopleUnitsByConnectedUser completed; people units size '{}';",
                peopleUnits.size());
        return peopleUnits;
    }

    @Override
    public List<SpocsHrPumResponseDTO> getSpocsHrPum(Principal principal) {
        AppUser connectedUser = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();

        List<SpocsHrPumResponseDTO> spocsHrPumDTOS = new ArrayList<>();
        Set<MacroPeopleUnit> macroPeopleUnits = new HashSet<>();

        if (connectedUser.isAdmin()) {
            macroPeopleUnits = new HashSet<>(macroPeopleUnitRepository.findAll());
        } else if (connectedUser.isHrbp()) {
            connectedUser = hrbpService.getHrbpById(connectedUser.getId());
            macroPeopleUnits = ((Hrbp) connectedUser).getMacroPeopleUnits();
        } else if (connectedUser.isHr()) {
            connectedUser = hrService.getHrById(connectedUser.getId());
            macroPeopleUnits = ((Hr) connectedUser).getMacroPeopleUnits();
        }

        for (MacroPeopleUnit macroPeopleUnit : macroPeopleUnits) {
            SpocsHrPumResponseDTO response =
                    macroPeopleUnitService.getSpocsHrPumByMacroPeopleUnitName(macroPeopleUnit.getName());
            spocsHrPumDTOS.add(response);
        }
        spocsHrPumDTOS.sort(Comparator.comparing(SpocsHrPumResponseDTO::getMacroPeopleUnitName));

        log.info("PeopleUnitService::getSpocsHrPum completed; spocs size '{}'; connected user '{}'",
                spocsHrPumDTOS.size(), principal.getName());
        return spocsHrPumDTOS;
    }

    @Override
    public List<PeopleUnitResponseDTO> getConnectedUserPeopleUnits(Principal principal) {
        AppUser connectedUser = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();

        if (connectedUser.isAdmin()) {
            List<PeopleUnitResponseDTO> peopleUnits = getAllPeopleUnits();
            log.info("PeopleUnitService::getConnectedUserPeopleUnits completed; result size '{}', " +
                    "connected user role '{}'", peopleUnits.size(), connectedUser.getRole());
            return peopleUnits;
        } else if (connectedUser.isHrbp() || connectedUser.isHr()) {
            List<PeopleUnitResponseDTO> peopleUnits = getPeopleUnitDTOsByConnectedUser(connectedUser);
            log.info("PeopleUnitService::getConnectedUserPeopleUnits completed; result size '{}', " +
                    "connected user role '{}'", peopleUnits.size(), connectedUser.getRole());
            return peopleUnits;
        }

        log.warn("PeopleUnitService::getConnectedUserPeopleUnits unauthorized;");
        throw new IllegalStateException(messageSourceUtil.getMessage("exception.unauthorized-action"));
    }

    @Override
    @Transactional
    public void savePeopleUnit(PeopleUnitRequestDTO request) {
        String peopleUnitName = request.getName();
        // Slashes are not allowed in the People Unit name because it is passed in the URL
        if (peopleUnitName.contains("/") || peopleUnitName.contains("\\")) {
            log.warn("PeopleUnitService::createPeopleUnit unauthorized name '{}';", peopleUnitName);
            throw new IllegalStateException(messageSourceUtil.getMessage("validation.name-no-slash"));
        }

        if (peopleUnitRepository.findPeopleUnitByName(peopleUnitName).isPresent()) {
            log.warn("PeopleUnitService::savePeopleUnit people unit by name '{}' already exists;", peopleUnitName);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessageWithObject("people-unit.by-name.already-exists", peopleUnitName)
            );
        }

        PeopleUnit newPeopleUnit = PeopleUnit.builder()
                .name(request.getName())
                .build();
        PeopleUnit peopleUnit = peopleUnitRepository.save(newPeopleUnit);
        log.debug("PeopleUnitService::savePeopleUnit people unit saved; PU name '{}';", peopleUnitName);

        assignPeopleUnitToMacroPeopleUnit(peopleUnit, request.getMacroPeopleUnitName());
        log.info("PeopleUnitService::savePeopleUnit completed; PU name '{}';", peopleUnitName);
    }

    private void assignPeopleUnitToMacroPeopleUnit(PeopleUnit peopleUnit, String macroPeopleUnitName) {
        MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
        if (!isEmpty(macroPeopleUnit.getPeopleUnits())) {
            macroPeopleUnit.getPeopleUnits().add(peopleUnit);
        } else {
            macroPeopleUnit.setPeopleUnits(new HashSet<>(Set.of(peopleUnit)));
        }
        macroPeopleUnitRepository.save(macroPeopleUnit);
        log.debug("PeopleUnitService::assignPeopleUnitToMacroPeopleUnit completed; " +
                "PU '{}' assigned to Macro PU '{}';", peopleUnit.getName(), macroPeopleUnitName);
    }

    @Override
    public void updatePeopleUnit(String peopleUnitName, PeopleUnitRequestDTO request) {
        Optional<PeopleUnit> peopleUnit = peopleUnitRepository.findPeopleUnitByName(peopleUnitName);
        if (peopleUnit.isEmpty()) {
            log.warn("PeopleUnitService::updatePeopleUnit people unit by name '{}' not found;", peopleUnitName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "people-unit.by-id.not-found", peopleUnitName));
        }

        // Verify if PU name is not already exists, excepting the processing PU
        Optional<PeopleUnit> peopleUnitExists = peopleUnitRepository.findPeopleUnitByName(request.getName());
        if (peopleUnitExists.isPresent() && !peopleUnitExists.get().getName().equals(peopleUnitName)) {
            log.warn("PeopleUnitService::updatePeopleUnit people unit by name '{}' already exist;", peopleUnitName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "people-unit.by-name.already-exists", request.getName())
            );
        }

        // Update PU
        PeopleUnit updatePeopleUnit = peopleUnit.get();
        updatePeopleUnit.setName(request.getName());
        updatePeopleUnit.setMacroPeopleUnit(macroPeopleUnitService.getMacroPeopleUnitByName(request.getMacroPeopleUnitName()));
        peopleUnitRepository.save(updatePeopleUnit);
        log.warn("PeopleUnitService::updatePeopleUnit completed; people unit '{}' updated;", peopleUnitName);
    }

    @Override
    public void updatePeopleUnit(String peopleUnitName, PeopleUnitRequestDTO request, Principal principal) {
        // Verify authorization
        if (!canHandlePeopleUnit(principal, peopleUnitName)) {
            throw new UnauthorizedException();
        }

        updatePeopleUnit(peopleUnitName, request);
    }

    @Override
    @Transactional
    public void deletePeopleUnitById(Long peopleUnitId) {
        PeopleUnit peopleUnit = getPeopleUnitById(peopleUnitId);
        if (!isEmpty(peopleUnit.getCollaborators())) {
            peopleUnit.getCollaborators().forEach(collaborator -> {
                log.warn("PeopleUnitService::deletePeopleUnitById people unit belongs to a collaborator; collaborator GGID '{}';", collaborator.getGgid());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObjects("people-unit.cannot-delete-pu-belongs-to-collaborator",
                        Arrays.asList(peopleUnit.getName(), collaborator.fullName())));
            });
        }
        if (peopleUnit.getMacroPeopleUnit().getPeopleUnits().size() <= 1) {
            log.warn("PeopleUnitService::deletePeopleUnitById cannot delete macro people unit's sole people unit; macro PU name '{}';", peopleUnit.getMacroPeopleUnit().getName());
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                    "macro-people-unit.cannot-delete-the-only-people-unit-for-macro-pu", peopleUnit.getMacroPeopleUnit().getName()));
        }

        peopleUnit.setMacroPeopleUnit(null);
        peopleUnitRepository.save(peopleUnit);
        peopleUnitRepository.deletePeopleUnitById(peopleUnit.getId());
        log.debug("PeopleUnitService::deletePeopleUnitById completed; PU ID '{}';", peopleUnitId);
    }

    @Override
    public void deletePeopleUnitByName(String peopleUnitName) {
        PeopleUnit peopleUnit = getPeopleUnitByName(peopleUnitName);
        deletePeopleUnitById(peopleUnit.getId());
        log.debug("PeopleUnitService::deletePeopleUnitByName completed; PU name '{}';", peopleUnitName);
    }

    @Override
    public void deletePeopleUnitByName(String peopleUnitName, Principal principal) {
        // Verify authorization
        if (!canHandlePeopleUnit(principal, peopleUnitName)) {
            log.warn("PeopleUnitService::deletePeopleUnitByName cannot handle PU; " +
                    "PU Name '{}', connectedUser '{}';", peopleUnitName, principal.getName());
            throw new UnauthorizedException();
        }

        deletePeopleUnitByName(peopleUnitName);
        log.info("PeopleUnitService::deletePeopleUnitByName completed; PU name '{}';", peopleUnitName);
    }

    @Override
    public PeopleUnit getPeopleUnitById(Long peopleUnitId) {
        PeopleUnit peopleUnit = peopleUnitRepository.findPeopleUnitById(peopleUnitId)
                .orElseThrow(() -> {
                    log.warn("PeopleUnitService::getPeopleUnitById PU by ID '{}' not found;", peopleUnitId);
                    return new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "people-unit.by-id.not-found", peopleUnitId));
                });
        log.debug("PeopleUnitService::getPeopleUnitById completed; PU ID '{}';", peopleUnitId);
        return peopleUnit;
    }

    @Override
    public PeopleUnitResponseDTO getPeopleUnitDTOById(Long peopleUnitId) {
        PeopleUnit peopleUnit = peopleUnitRepository.findPeopleUnitById(peopleUnitId)
                .orElseThrow(() -> {
                    log.warn("PeopleUnitService::getPeopleUnitDTOById PU by ID '{}' not found;", peopleUnitId);
                    return new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "people-unit.by-id.not-found", peopleUnitId));
                });
        PeopleUnitResponseDTO peopleUnitDTO = peopleUnitMapper.toPeopleUnitDTO(peopleUnit);
        log.warn("PeopleUnitService::getPeopleUnitDTOById PU by ID '{}' not found;", peopleUnitId);
        return peopleUnitDTO;
    }

    @Override
    public PeopleUnitResponseDTO getPeopleUnitDTOByName(String peopleUnitName) {
        PeopleUnit peopleUnit = peopleUnitRepository.findPeopleUnitByName(peopleUnitName)
                .orElseThrow(() -> {
                    log.warn("PeopleUnitService::getPeopleUnitDTOByName PU by name '{}' not found;", peopleUnitName);
                    return new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "people-unit.by-name.not-found", peopleUnitName));
                });
        PeopleUnitResponseDTO peopleUnitDTO = peopleUnitMapper.toPeopleUnitDTO(peopleUnit);
        log.warn("PeopleUnitService::getPeopleUnitDTOByName PU by name '{}' not found;", peopleUnitName);
        return peopleUnitDTO;
    }

    @Override
    public PeopleUnitResponseDTO getPeopleUnitDTOByName(String peopleUnitName, Principal principal) {
        // Verify authorization
        if (!canHandlePeopleUnit(principal, peopleUnitName)) {
            log.warn("PeopleUnitService::getPeopleUnitDTOByName cannot handle PU; " +
                    "PU Name '{}', connectedUser '{}';", peopleUnitName, principal.getName());
            throw new UnauthorizedException();
        }

        PeopleUnitResponseDTO peopleUnitDTO = getPeopleUnitDTOByName(peopleUnitName);
        log.info("PeopleUnitService::getPeopleUnitDTOByName completed; PU name '{}';", peopleUnitName);
        return peopleUnitDTO;
    }

    private boolean canHandlePeopleUnit(Principal principal, String peopleUnitName) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);

        // Except for the admin, a user can handle only the PUs that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            Set<String> peopleUnitNames = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits()
                    .forEach(macroPeopleUnit -> peopleUnitNames.addAll(
                            macroPeopleUnit.getPeopleUnits()
                                    .stream()
                                    .map(PeopleUnit::getName)
                                    .collect(Collectors.toSet())));
            return peopleUnitNames.contains(peopleUnitName);
        } else if (connectedUser.isHr()) {
            Set<String> peopleUnitNames = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits()
                    .forEach(macroPeopleUnit -> peopleUnitNames.addAll(
                            macroPeopleUnit.getPeopleUnits()
                                    .stream()
                                    .map(PeopleUnit::getName)
                                    .collect(Collectors.toSet())));
            return peopleUnitNames.contains(peopleUnitName);
        }
        return false;
    }

    @Override
    public ByteArrayInputStream convertPeopleUnitsToCSV() throws IOException {
        String[] headers = {"Macro PU", "PU Name", "Chargé de mission RH", "HRBP", "Practice"};

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(byteArrayOutputStream),
                     CSVFormat.DEFAULT.withHeader(headers))) {
            List<MacroPeopleUnit> macroPeopleUnits = macroPeopleUnitRepository.findAll();

            for (MacroPeopleUnit macroPeopleUnit : macroPeopleUnits) {
                writePeopleUnitData(csvPrinter, macroPeopleUnit);
            }
            csvPrinter.flush();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            log.info("PeopleUnitService::convertPeopleUnitsToCSV completed;");
            return byteArrayInputStream;
        }
    }


    private void writePeopleUnitData(CSVPrinter csvPrinter, MacroPeopleUnit macroPeopleUnit) throws IOException {
        String macroPeopleUnitName = macroPeopleUnit.getName();
        int peopleUnitsListSize = (macroPeopleUnit.getPeopleUnits() != null) ? macroPeopleUnit.getPeopleUnits().size() : 0;
        int hrsListSize = (macroPeopleUnit.getHrs() != null) ? macroPeopleUnit.getHrs().size() : 0;
        int hrbpsListSize = (macroPeopleUnit.getHrbps() != null) ? macroPeopleUnit.getHrbps().size() : 0;
        int pumsListSize = (macroPeopleUnit.getPums() != null) ? macroPeopleUnit.getPums().size() : 0;
        int resourceManagersListSize = (macroPeopleUnit.getResourceManagers() != null) ? macroPeopleUnit.getResourceManagers().size() : 0;

        List<Integer> sizes = Arrays.asList(peopleUnitsListSize, hrbpsListSize, hrsListSize, pumsListSize, resourceManagersListSize);
        int max = Collections.max(sizes);
        for (int i = 0; i < max; i++) {
            String peopleUnit = writePeopleUnit(macroPeopleUnit, peopleUnitsListSize, i);
            String infosHr = writeHr(macroPeopleUnit, hrsListSize, i);
            String infosHrbp = writeHrbp(macroPeopleUnit, hrbpsListSize, i);
            String infosPum = writePum(macroPeopleUnit, pumsListSize, i);
            String infosResourceManager = writeResourceManager(macroPeopleUnit, resourceManagersListSize, i);
            String infosPractice = writePractice(infosPum, infosResourceManager);
            
            if (i == 0) {
                csvPrinter.printRecord(macroPeopleUnitName, peopleUnit, infosHr, infosHrbp, infosPractice);
            } else {
                csvPrinter.printRecord("", peopleUnit, infosHr, infosHrbp, infosPractice);
            }
        }
    }

    private String writeHrbp(MacroPeopleUnit macroPeopleUnit, int hrbpsListSize, int i) {
        String infosHrbp = "";
        if (i < hrbpsListSize) {
            StringBuilder infosHrbpBuilder = new StringBuilder();
            AppUser hrbp = macroPeopleUnit.getHrbps().stream().toList().get(i);
            infosHrbpBuilder.append(hrbp.getLastname());
            infosHrbpBuilder.append(", ");
            infosHrbpBuilder.append(hrbp.getFirstname());
            infosHrbpBuilder.append(" <");
            infosHrbpBuilder.append(hrbp.getEmail());
            infosHrbpBuilder.append(">");
            if (i < hrbpsListSize - 1) {
                infosHrbpBuilder.append(System.getProperty(LINE_SEPARATOR));
            }
            infosHrbp = infosHrbpBuilder.toString();
        }
        return infosHrbp;
    }

    private String writePum(MacroPeopleUnit macroPeopleUnit, int pumsListSize, int i) {
        String infosPum = "";
        if (i < pumsListSize) {
            StringBuilder infosPumBuilder = new StringBuilder();
            PeopleUnitManager pum = macroPeopleUnit.getPums().stream().toList().get(i);
            infosPumBuilder.append(pum.getFirstname());
            infosPumBuilder.append(", ");
            infosPumBuilder.append(pum.getLastname());
            infosPumBuilder.append(" <");
            infosPumBuilder.append(pum.getEmail());
            infosPumBuilder.append(">");
            if (i < pumsListSize - 1) {
                infosPumBuilder.append(System.getProperty(LINE_SEPARATOR));
            }
            infosPum = infosPumBuilder.toString();
        }
        return infosPum;
    }

    private String writePractice(String infosPum, String infosResourceManager) {
        String infosPractice;
        if (!infosPum.isEmpty() && !infosResourceManager.isEmpty()) {
            infosPractice = infosPum.concat("\n").concat(infosResourceManager);
        } else {
            infosPractice = infosPum.concat(infosResourceManager);
        }
        return infosPractice;
    }

    private String writeResourceManager(MacroPeopleUnit macroPeopleUnit, int resourceManagersListSize, int i) {
        String infosResourceManager = "";
        if (i < resourceManagersListSize) {
            StringBuilder infosResourceManagerBuilder = new StringBuilder();
            ResourceManager resourceManager = macroPeopleUnit.getResourceManagers().stream().toList().get(i);
            infosResourceManagerBuilder.append(resourceManager.getLastname());
            infosResourceManagerBuilder.append(", ");
            infosResourceManagerBuilder.append(resourceManager.getFirstname());
            infosResourceManagerBuilder.append(" <");
            infosResourceManagerBuilder.append(resourceManager.getEmail());
            infosResourceManagerBuilder.append(">");
            if (i < resourceManagersListSize - 1) {
                infosResourceManagerBuilder.append(System.getProperty(LINE_SEPARATOR));
            }
            infosResourceManager = infosResourceManagerBuilder.toString();
        }
        return infosResourceManager;
    }

    private String writeHr(MacroPeopleUnit macroPeopleUnit, int hrsListSize, int i) {
        String infosHr = "";
        if (i < hrsListSize) {
            StringBuilder infosHrBuilder = new StringBuilder();
            Hr hr = macroPeopleUnit.getHrs().stream().toList().get(i);
            infosHrBuilder.append(hr.getLastname());
            infosHrBuilder.append(", ");
            infosHrBuilder.append(hr.getFirstname());
            infosHrBuilder.append(" <");
            infosHrBuilder.append(hr.getEmail());
            infosHrBuilder.append(">");
            if (i < hrsListSize - 1) {
                infosHrBuilder.append(System.getProperty(LINE_SEPARATOR));
            }
            infosHr = infosHrBuilder.toString();
        }
        return infosHr;
    }

    private String writePeopleUnit(MacroPeopleUnit macroPeopleUnit, int peopleUnitsListSize, int i) {
        String peopleUnit = "";
        if (i < peopleUnitsListSize) {
            peopleUnit = macroPeopleUnit.getPeopleUnits().stream().toList().get(i).getName();
        }
        return peopleUnit;
    }
}