package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ProjectManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import com.capgemini.trialperiodapi.mapper.ProjectManagerMapper;
import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.ProjectManager;
import com.capgemini.trialperiodapi.repository.ProjectManagerRepository;
import com.capgemini.trialperiodapi.repository.ProjectRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectManagerService implements IProjectManagerService {
    private final ProjectManagerRepository projectManagerRepository;
    private final ProjectManagerMapper projectManagerMapper;
    private final ProjectRepository projectRepository;
    private final MessageSourceUtil messageSourceUtil;
    private final IProjectService projectService;

    @Override
    public ProjectManager getProjectManagerByEmail(String email) {
        ProjectManager projectManager = projectManagerRepository.findProjectManagerByEmail(email)
                .orElseThrow(() -> {
                    log.warn("ProjectManagerService::getProjectManagerByEmail project manager by email '{}' not found;", email);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "project-manager.by-email.not-found", email)
                    );
                });
        log.debug("ProjectManagerService::getProjectManagerByEmail completed; project manager email '{}'", email);
        return projectManager;
    }

    @Override
    @Transactional
    public void saveProjectManager(ProjectManagerRequestDTO request) {
        String email = request.getEmail();
        if (projectManagerRepository.findProjectManagerByEmail(email).isPresent()) {
            log.warn("ProjectManagerService::saveProjectManager project manager by email '{}' already exists;", email);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "project-manager.by-email.exists", email)
            );
        }

        // Save project manager
        ProjectManager projectManager = new ProjectManager();
        projectManager.setFirstname(request.getFirstname());
        projectManager.setLastname(request.getLastname());
        projectManager.setEmail(email);
        projectManagerRepository.save(projectManager);
        log.debug("ProjectManagerService::saveProjectManager project manager saved; project manager email '{}'", email);

        // Assign project manager to request projects
        if (isNotEmpty(request.getProjectNames())) {
            request.getProjectNames()
                    .stream()
                    .map(projectService::getProjectByName)
                    .forEach(project -> {
                        log.trace("ProjectManagerService::saveProjectManager project manager '{}' assigned to project '{}'",
                                email, project.getName());
                        project.getProjectManagers().add(projectManager);
                        projectRepository.save(project);
                    });
        }
        log.info("ProjectManagerService::saveProjectManager completed; project manager email '{}'", email);
    }

    @Override
    public ProjectManagerResponseDTO getProjectManagerDTOById(Long projectManagerId) {
        ProjectManager projectManager = getProjectManagerById(projectManagerId);
        ProjectManagerResponseDTO projectManagerDTO = projectManagerMapper.toProjectManagerResponseDTO(projectManager);
        log.debug("ProjectManagerService::getProjectManagerDTOById completed; project ID '{}';", projectManagerId);
        return projectManagerDTO;
    }

    @Override
    public ProjectManager getProjectManagerById(Long projectManagerId) {
        ProjectManager projectManager = projectManagerRepository.findById(projectManagerId)
                .orElseThrow(() -> {
                    log.warn("ProjectManagerService::getProjectManagerById project manager by ID '{}' not found;", projectManagerId);
                    return new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "project-manager.by-id.not-found", projectManagerId)
                    );
                });
        log.debug("ProjectManagerService::getProjectManagerById completed; project manager ID '{}';", projectManagerId);
        return projectManager;
    }

    @Override
    @Transactional
    public void deleteProjectManagerByEmail(String projectManagerEmail) {
        // Detach Project Manager if not sole Project Manager for Project
        ProjectManager projectManager = getProjectManagerByEmail(projectManagerEmail);
        projectManager.getProjects().forEach(project -> {
            if (project.getProjectManagers().size() <= 1) {
                log.warn("ProjectManagerService::deleteProjectManagerByEmail cannot delete the only project manager '{}' for project '{}';",
                        projectManager.getLastname(), project.getName());
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "project-manager.cannot-delete-the-only-project-manager-for-project", project.getName())
                );
            }
            project.getProjectManagers().remove(projectManager);
            projectRepository.save(project);
        });

        // Delete project manager
        projectManagerRepository.deleteProjectManagerByEmail(projectManager.getEmail());
        log.debug("ProjectManagerService::deleteProjectManagerByEmail completed; project manager email '{}';",
                projectManager.getEmail());
    }

    @Override
    @Transactional
    public void updateProjectManager(Long projectManagerId, ProjectManagerRequestDTO request) {
        if (projectManagerRepository.findById(projectManagerId).isEmpty()) {
            log.warn("ProjectManagerService::updateProjectManager project manager by ID '{}' not found;", projectManagerId);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessageWithObject("project-manager.by-id.not-found", projectManagerId)
            );
        }

        // Verify if project manager email is not already exists, excepting the processing project manager
        String email = request.getEmail();
        Optional<ProjectManager> managerExists = projectManagerRepository.findProjectManagerByEmail(email);
        if (managerExists.isPresent() && !managerExists.get().getId().equals(projectManagerId)) {
            log.warn("ProjectManagerService::updateProjectManager project manager by email '{}' already exists;", email);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessageWithObject("project-manager.by-email.exists", email)
            );
        }

        // Update project manager
        ProjectManager projectManager = getProjectManagerById(projectManagerId);
        projectManager.setFirstname(request.getFirstname());
        projectManager.setLastname(request.getLastname());
        projectManager.setEmail(request.getEmail());
        projectManagerRepository.save(projectManager);

        // Update projects assignment
        Set<Project> updatedProjects = new HashSet<>();
        Set<Project> projects = projectManager.getProjects();
        projects.forEach(project -> {
            updatedProjects.add(project);
            project.getProjectManagers().remove(projectManager);
            projectRepository.save(project);
        });
        request.getProjectNames().stream()
                .map(projectService::getProjectByName)
                .forEach(project -> {
                    project.getProjectManagers().add(projectManager);
                    projectRepository.save(project);
                });

        // Ensure that the project still has at least one project manager
        updatedProjects.forEach(project -> {
            if (isEmpty(project.getProjectManagers())) {
                log.warn("ProjectManagerService::updateProjectManager cannot delete the only project manager '{}' for project '{}';",
                        projectManager.getLastname(), project.getId());
                throw new IllegalStateException(
                        messageSourceUtil.getMessageWithObject(
                                "project-manager.cannot-delete-the-only-project-manager-for-project", project.getName()
                        )
                );
            }
        });
        log.info("ProjectManagerService::updateProjectManager completed; project manager ID '{}';", projectManagerId);
    }

    @Override
    public List<ProjectManagerResponseDTO> searchProjectManagersByKeyword(String keyword) {
        List<ProjectManager> projectManagers = projectManagerRepository.findProjectManagersByKeyword(keyword);
        List<ProjectManagerResponseDTO> projectManagerDTOS = projectManagers.stream().map(projectManagerMapper::toProjectManagerResponseDTO).toList();
        log.info("ProjectManagerService::searchProjectManagersByKeyword keyword '{}' projectManagers size '{}';",
                keyword, projectManagerDTOS.size());
        return projectManagerDTOS;
    }

    @Override
    public Page<ProjectManagerResponseDTO> filterProjectManagers(String keyword, Pageable pageable) {
        Page<ProjectManager> projectManagerPage = projectManagerRepository.findAllProjectManagersByKeyword(keyword, pageable);
        List<ProjectManagerResponseDTO> projectManagers = projectManagerPage.getContent()
                .stream()
                .map(projectManagerMapper::toProjectManagerResponseDTO)
                .toList();
        log.info("ProjectManagerService::filterProjectManagers keyword '{}' projectManagers size '{}';",
                keyword, projectManagers.size());
        return new PageImpl<>(projectManagers, pageable, projectManagerPage.getTotalElements());
    }
}