package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.ProjectCsvRepresentation;
import com.capgemini.trialperiodapi.dto.request.ProjectManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.request.ProjectRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectResponseDTO;
import com.capgemini.trialperiodapi.mapper.ProjectMapper;
import com.capgemini.trialperiodapi.model.DecisionMaker;
import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.ProjectManager;
import com.capgemini.trialperiodapi.repository.ProjectManagerRepository;
import com.capgemini.trialperiodapi.repository.ProjectRepository;
import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.capgemini.trialperiodapi.util.StringUtil;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@Transactional
@Slf4j
public class ProjectService extends AbstractUploadService implements IProjectService {
    AtomicInteger uploadedProjectCount = new AtomicInteger();

    private final ProjectManagerRepository projectManagerRepository;
    private final IProjectManagerService projectManagerService;
    private final MessageSourceUtil messageSourceUtil;
    private final ProjectRepository projectRepository;
    private final ProjectMapper projectMapper;
    private final StringUtil stringUtil;

    public ProjectService(ProjectManagerRepository projectManagerRepository,
                          @Lazy IProjectManagerService projectManagerService,
                          MessageSourceUtil messageSourceUtil,
                          ProjectRepository projectRepository,
                          ProjectMapper projectMapper,
                          StringUtil stringUtil,
                          FileUtil fileUtil) {
        super(messageSourceUtil, fileUtil);
        this.projectManagerRepository = projectManagerRepository;
        this.projectManagerService = projectManagerService;
        this.messageSourceUtil = messageSourceUtil;
        this.projectRepository = projectRepository;
        this.projectMapper = projectMapper;
        this.stringUtil = stringUtil;
    }

    @Override
    public Project getProjectByName(String projectName) {
        Project project = projectRepository.findProjectByName(projectName).orElseThrow(() -> {
            log.warn("ProjectService::getProjectByName project name '{}' not found;", projectName);
            return new EntityNotFoundException(
                    messageSourceUtil.getMessageWithObject("project.by-name.not-found", projectName)
            );
        });
        log.debug("ProjectService::getProjectByName completed; project name '{}';", projectName);
        return project;
    }

    @Override
    public List<String> searchProjectNamesByKeyword(String keyword) {
        List<String> projectNamesByKeyword = projectRepository.findProjectNamesByKeyword(keyword);
        log.info("ProjectService::searchProjectNamesByKeyword completed; keyword '{}'; " +
                "project results size '{}';", keyword, projectNamesByKeyword.size());
        return projectNamesByKeyword;
    }

    @Override
    @Transactional
    public void saveProject(ProjectRequestDTO request) {
        String projectName = request.getName();
        if (isBlank(projectName)) {
            log.warn("ProjectService::createProject project name is blank;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("project.name.empty")
            );
        }
        if (projectName.contains("/") || projectName.contains("\\")) {
            log.warn("ProjectService::saveProject invalid name '{}' containing slash;", projectName);
            throw new IllegalStateException(messageSourceUtil.getMessage("validation.name-no-slash"));
        }
        if (projectRepository.findProjectByName(projectName).isPresent()) {
            log.warn("ProjectService::saveProject project by name '{}' already exists;", projectName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "project.by-name.exists", projectName));
        }

        Project project = Project.builder().name(projectName).build();

        if (request.getProjectManagerEmails() != null) {
            project.setProjectManagers(request.getProjectManagerEmails()
                    .stream()
                    .map(projectManagerService::getProjectManagerByEmail)
                    .collect(Collectors.toSet()));
        }
        projectRepository.save(project);
        log.info("ProjectService::saveProject completed;  project name '{}';", projectName);
    }

    @Override
    public Integer uploadProjects(MultipartFile file) {
        return upload(file);
    }

    @Override
    @Transactional
    protected Integer parseCsv(MultipartFile file) {
        try (Reader reader = new BufferedReader(new InputStreamReader(
                file.getInputStream(), StandardCharsets.UTF_8))) {

            HeaderColumnNameMappingStrategy<ProjectCsvRepresentation> strategy =
                    new HeaderColumnNameMappingStrategy<>();
            strategy.setType(ProjectCsvRepresentation.class);

            CsvToBean<ProjectCsvRepresentation> csvToBean =
                    new CsvToBeanBuilder<ProjectCsvRepresentation>(reader)
                            .withMappingStrategy(strategy)
                            .withIgnoreEmptyLine(true)
                            .withThrowExceptions(false)
                            .withIgnoreLeadingWhiteSpace(true)
                            .build();

            csvToBean.parse().forEach(csvLine -> Project.builder()
                    .name(getOrCreateProject(csvLine.getProjectName()))
                    .projectManagers(getOrCreateProjectManagers(csvLine.getProjectManagers(),
                            csvLine.getProjectName()))
                    .build()
            );
            log.info("ProjectService::parseCsv completed; uploaded projects '{}';", uploadedProjectCount.get());
            return uploadedProjectCount.get();

        } catch (IOException e) {
            log.error("ProjectService::parseCsv failed to upload file;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.file.upload-failed")
            );
        } finally {
            uploadedProjectCount.getAndSet(0);
        }
    }

    private String getOrCreateProject(String projectName) {
        if (projectRepository.findProjectByName(projectName).isPresent()) {
            log.debug("ProjectService::getOrCreateProject completed; project '{}' returned;", projectName);
            return projectName;
        } else {
            ProjectRequestDTO project = ProjectRequestDTO.builder()
                    .name(projectName)
                    .build();
            saveProject(project);
            uploadedProjectCount.getAndIncrement();
            log.debug("ProjectService::getOrCreateProject project '{}' created;", projectName);
            return getProjectByName(projectName).getName();
        }
    }

    private Set<ProjectManager> getOrCreateProjectManagers(String projectManagers, String projectName) {
        Set<ProjectManager> managers = new HashSet<>();

        Project project = getProjectByName(projectName);
        project.setProjectManagers(new HashSet<>());

        String[] lines = getLinesFromCsvColumn(projectManagers);
        for (String line : lines) {
            String firstname = stringUtil.extractFirstnameFromCsvColumn(line);
            String lastname = stringUtil.extractLastnameFromCsvColumn(line);
            String email = stringUtil.extractEmailFromCsvColumn(line);

            Optional<ProjectManager> optionalProjectManager = projectManagerRepository.findProjectManagerByEmail(email);
            if (optionalProjectManager.isEmpty()) {
                ProjectManagerRequestDTO projectManagerRequest = ProjectManagerRequestDTO.builder()
                        .firstname(firstname)
                        .lastname(lastname)
                        .email(email)
                        .build();
                projectManagerService.saveProjectManager(projectManagerRequest);
                ProjectManager newProjectManager = projectManagerService.getProjectManagerByEmail(email);
                project.getProjectManagers().add(newProjectManager);
            } else {
                project.getProjectManagers().add(optionalProjectManager.get());
                managers.add(associateAndGetProjectManager(email, projectName));
            }

            ProjectRequestDTO request = ProjectRequestDTO.builder()
                    .name(project.getName())
                    .projectManagerEmails(project.getProjectManagers()
                            .stream()
                            .map(DecisionMaker::getEmail)
                            .collect(Collectors.toSet()))
                    .build();
            updateProject(project.getName(), request);
        }
        log.debug("ProjectService::getOrCreateProjectManagers projectManagers size '{}';", managers.size());
        return managers;
    }

    private ProjectManager associateAndGetProjectManager(String email, String projectName) {
        ProjectManager projectManager = projectManagerService.getProjectManagerByEmail(email);
        projectManager.setId(projectManager.getId());
        projectManager.getProjects().add(getProjectByName(projectName));
        ProjectManager associatedProjectManager = projectManagerRepository.save(projectManager);
        log.debug("ProjectService::associateAndGetProjectManager project '{}' associated to project manager '{}';",
                projectName, projectManager.getLastname());
        return associatedProjectManager;
    }

    @Override
    public ProjectResponseDTO getProjectDTOByName(String projectName) {
        Project project = projectRepository.findProjectByName(projectName)
                .orElseThrow(() -> {
                    log.warn("ProjectService::getProjectDTOByName project '{}' not found;", projectName);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "project.by-name.not-found", projectName)
                    );
                });
        ProjectResponseDTO projectDTO = projectMapper.toProjectResponseDTO(project);
        log.debug("ProjectService::getProjectDTOByName completed; project name '{}';", projectName);
        return projectDTO;
    }

    @Override
    public ByteArrayInputStream convertProjectsToCSV() throws IOException {
        String[] headers = {"Nom Projet", "EM"};

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(byteArrayOutputStream), CSVFormat.DEFAULT.withHeader(headers))) {

            List<Project> projects = projectRepository.findAll();
            for (Project project : projects) {
                if (project.getProjectManagers() != null && !project.getProjectManagers().isEmpty()) {
                    StringBuilder infosProjectManagers = new StringBuilder();
                    for (ProjectManager projectManager : project.getProjectManagers()) {
                        infosProjectManagers.append(projectManager.getLastname());
                        infosProjectManagers.append(", ");
                        infosProjectManagers.append(projectManager.getFirstname());
                        infosProjectManagers.append(" <");
                        infosProjectManagers.append(projectManager.getEmail());
                        infosProjectManagers.append(">;");
                    }
                    log.warn("Output '{}';", infosProjectManagers);
                    csvPrinter.printRecord(project.getName(), infosProjectManagers.toString());

                } else {
                    csvPrinter.printRecord(project.getName(), "");

                }
            }
            csvPrinter.flush();
            ByteArrayInputStream csv = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

            log.info("ProjectService::convertProjectsToCSV completed;");
            return csv;
        }
    }

    @Override
    public Page<ProjectResponseDTO> getAllProjects(String keyword, Pageable pageable) {
        Page<Project> projectPage = projectRepository.findAll(withFilters(keyword), pageable);
        List<ProjectResponseDTO> projectResponseDTOS = projectPage.getContent()
                .stream()
                .map(projectMapper::toProjectResponseDTO)
                .toList();
        log.info("ProjectService::getAllProjects completed; projects size '{}'", projectResponseDTOS.size());
        return new PageImpl<>(projectResponseDTOS, pageable, projectPage.getTotalElements());
    }

    public static Specification<Project> withFilters(String keyword) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.isTrue(criteriaBuilder.literal(true));
            if (!isBlank(keyword)) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(root.get("name")), "%" + keyword.toLowerCase().trim() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(root.join("projectManagers").get("firstname")), "%" + keyword.toLowerCase().trim() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(root.join("projectManagers").get("lastname")), "%" + keyword.toLowerCase().trim() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(root.join("projectManagers").get("email")), "%" + keyword.toLowerCase().trim() + "%")
                ));
            }
            query.distinct(true);
            log.debug("ProjectService::withFilters completed; keyword '{}'", keyword);
            return predicate;
        };
    }

    @Override
    @Transactional
    public void deleteProjectByProjectName(String projectName) {
        Project project = getProjectByName(projectName);
        project.getCollaborators().forEach(collaborator -> {
            log.warn("ProjectService::deleteProjectByProjectName cannot delete project '{}' assigned to collaborator; " +
                    "collaborator GGID '{}';", projectName, collaborator.getGgid());
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                    "project.cannot-delete-project-assigned-to-collaborator", collaborator.fullName()));
        });

        project.setProjectManagers(new HashSet<>());
        projectRepository.save(project);

        projectRepository.deleteById(project.getId());
        log.info("ProjectService::deleteProjectByProjectName completed; project name '{}';", projectName);
    }

    @Override
    @Transactional
    public void updateProject(String projectName, ProjectRequestDTO request) {
        String newProjectName = request.getName();

        Optional<Project> existProject = projectRepository.findProjectByName(newProjectName);
        // Verify if project manager name is not already exists, excepting the processing project manager
        if (existProject.isPresent() && !existProject.get().getName().equals(projectName)) {
            log.warn("ProjectService::updateProject project by name '{}' already exists;", projectName);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "project.by-name.exists", newProjectName));
        }

        Project project = getProjectByName(projectName);
        project.setName(request.getName());

        if (request.getProjectManagerEmails() != null) {
            project.setProjectManagers(request.getProjectManagerEmails()
                    .stream()
                    .map(projectManagerService::getProjectManagerByEmail)
                    .collect(Collectors.toSet()));
        }
        projectRepository.save(project);
        log.info("ProjectService::updateProject completed; project name '{}';", projectName);
    }

    private String[] getLinesFromCsvColumn(String hrs) {
        String[] split = hrs.split("\\s+;\\s+");
        log.trace("ProjectService::getLinesFromCsvColumn splitting hrs '{}';", hrs);
        return split;
    }
}