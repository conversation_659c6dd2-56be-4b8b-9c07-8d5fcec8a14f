package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ResourceManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ResourceManagerResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.ResourceManagerMapper;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.ResourceManager;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.repository.ResourceManagerRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.*;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceManagerService implements IResourceManagerService {
    private final ResourceManagerRepository resourceManagerRepository;
    private final MacroPeopleUnitRepository macroPeopleUnitRepository;
    private final MacroPeopleUnitService macroPeopleUnitService;
    private final ResourceManagerMapper resourceManagerMapper;
    private final MessageSourceUtil messageSourceUtil;
    private final IUserService userService;

    @Override
    public void saveResourceManager(ResourceManagerRequestDTO request) {
        String email = request.getEmail();
        if (resourceManagerRepository.findResourceManagerByEmail(email).isPresent()) {
            log.warn("ResourceManagerService::saveResourceManager resource manager by email '{}' already exists;", email);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "resource-manager.by-email.exists", email));
        }
        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setFirstname(request.getFirstname());
        resourceManager.setLastname(request.getLastname());
        resourceManager.setEmail(email);
        resourceManagerRepository.save(resourceManager);
        log.debug("ResourceManagerService::saveResourceManager resource manager by email '{}' saved;", email);

        if (!isEmpty(request.getMacroPeopleUnitNames())) {
            request.getMacroPeopleUnitNames().forEach(macroPeopleUnitName -> {
                log.trace("ResourceManagerService::saveResourceManager resource manager by email '{}' " +
                        "assigned to macro PU '{}';", email, macroPeopleUnitName);
                MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);
                macroPeopleUnit.getResourceManagers().add(resourceManager);
                macroPeopleUnitRepository.save(macroPeopleUnit);
            });
        }
        log.debug("ResourceManagerService::saveResourceManager completed; resource manager email '{}';", email);
    }

    @Override
    public ResourceManager getResourceManagerByEmail(String email) {
        ResourceManager resourceManager = resourceManagerRepository.findResourceManagerByEmail(email)
                .orElseThrow(() -> {
                    log.warn("ResourceManagerService::getResourceManagerByEmail resource manager by email '{}' not found;", email);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject(
                                    "resource-manager.by-email.not-found", email)
                    );
                });
        log.debug("ResourceManagerService::getResourceManagerByEmail completed; resource manager email '{}'", email);
        return resourceManager;
    }

    @Override
    public List<ResourceManagerResponseDTO> filterResourceManagers(String keyword) {
        List<ResourceManager> resourceManagers = resourceManagerRepository.findResourceManagersByKeyword(keyword);
        List<ResourceManagerResponseDTO> resourceManagerDTOS = resourceManagers.stream().map(resourceManagerMapper::toResourceManagerDTO).toList();
        log.info("ResourceManagerService::getResourceManagerByEmail keyword '{}' resourceManagers size '{}';",
                keyword, resourceManagerDTOS.size());
        return resourceManagerDTOS;
    }

    @Override
    public List<ResourceManagerResponseDTO> filterResourceManagers(
            String keyword, Principal principal) {
        List<ResourceManagerResponseDTO> resourceManagers = filterResourceManagers(keyword)
                .stream()
                .filter(resourceManager -> canHandleResourceManager(principal, resourceManager.getId()))
                .toList();
        log.info("ResourceManagerService::filterResourceManagers keyword '{}' resourceManagers size '{}';",
                keyword, resourceManagers.size());
        return resourceManagers;
    }

    @Override
    @Transactional
    public void deleteResourceManagerByEmail(String email) {
        ResourceManager resourceManager = getResourceManagerByEmail(email);
        resourceManager.getMacroPeopleUnits().forEach(macroPeopleUnit -> {
            macroPeopleUnit.getResourceManagers().remove(resourceManager);
            macroPeopleUnitRepository.save(macroPeopleUnit);
        });
        resourceManagerRepository.deleteById(resourceManager.getId());
        log.debug("ResourceManagerService::deleteResourceManagerByEmail completed; resource manager email '{}'", email);

    }

    @Override
    public void deleteResourceManagerByEmail(String email, Principal principal) {
        ResourceManager resourceManager = getResourceManagerByEmail(email);
        // Verify authorization
        if (!canHandleResourceManager(principal, resourceManager.getId())) {
            log.warn("ResourceManagerService::deleteResourceManagerByEmail cannot handle resource manager; " +
                    "resource manager email '{}', connectedUser '{}';", email, principal.getName());
            throw new UnauthorizedException();
        }
        deleteResourceManagerByEmail(email);
        log.info("ResourceManagerService::deleteResourceManagerByEmail completed; " +
                "resource manager by email '{}' deleted;", email);
    }

    private ResourceManager getResourceManagerById(Long id) {
        ResourceManager resourceManager = resourceManagerRepository.findById(id)
                .orElseThrow(() -> {
                            log.warn("ResourceManagerService::getResourceManagerById resource manager by ID '{}' not found;", id);
                            return new EntityNotFoundException(
                                    messageSourceUtil.getMessageWithObject("resource-manager.by-id.not-found", id)
                            );
                        }
                );
        log.debug("ResourceManagerService::getResourceManagerById completed; resource manager ID '{}';", id);
        return resourceManager;
    }

    @Override
    public ResourceManagerResponseDTO getResourceManagerDtoById(Long resourceManagerId) {
        ResourceManager resourceManager = getResourceManagerById(resourceManagerId);
        ResourceManagerResponseDTO resourceManagerDTO = resourceManagerMapper.toResourceManagerDTO(resourceManager);
        log.debug("ResourceManagerService::getResourceManagerDtoById completed; resource manager ID '{}';", resourceManagerId);
        return resourceManagerDTO;
    }

    @Override
    public ResourceManagerResponseDTO getResourceManagerDtoById(Long resourceManagerId, Principal principal) {
        // Verify authorization
        if (!canHandleResourceManager(principal, resourceManagerId)) {
            log.warn("ResourceManagerService::getResourceManagerById cannot handle resource manager; " +
                    "resource manager ID '{}', connectedUser '{}';", resourceManagerId, principal.getName());
            throw new UnauthorizedException();
        }
        ResourceManagerResponseDTO resourceManagerDtoById = getResourceManagerDtoById(resourceManagerId);
        log.info("ResourceManagerService::getResourceManagerById completed; resource manager ID '{}';", resourceManagerId);
        return resourceManagerDtoById;
    }

    @Override
    @Transactional
    public void updateResourceManager(Long resourceManagerId, ResourceManagerRequestDTO request) {
        ResourceManager resourceManager = getResourceManagerById(resourceManagerId);
        Optional<ResourceManager> resourceManagerExists =
                resourceManagerRepository.findResourceManagerByEmail(request.getEmail());

        // Verify if resource manager email is not already exists, excepting the processing resource manager
        if (resourceManagerExists.isPresent() && !resourceManagerExists.get().getId().equals(resourceManagerId)) {
            log.warn("ResourceManagerService::updateResourceManager email '{}' already exists; ", request.getEmail());
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "resource-manager.by-email.exists", request.getEmail()));
        }

        resourceManager.setFirstname(request.getFirstname());
        resourceManager.setLastname(request.getLastname());
        resourceManager.setEmail(request.getEmail());
        resourceManagerRepository.save(resourceManager);

        resourceManager.getMacroPeopleUnits().forEach(
                macroPeopleUnit -> {
                    macroPeopleUnit.getResourceManagers().remove(resourceManager);
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                });

        request.getMacroPeopleUnitNames().forEach(
                name -> {
                    MacroPeopleUnit macroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(name);
                    macroPeopleUnit.getResourceManagers().add(resourceManager);
                    macroPeopleUnitRepository.save(macroPeopleUnit);
                    log.trace("ResourceManagerService::updateResourceManager resource manager '{}' assigned to macro PU '{}'",
                            resourceManager.getLastname(), macroPeopleUnit.getName());
                });
        log.info("ResourceManagerService::updateResourceManager completed; resource manager ID '{}';", resourceManagerId);
    }

    @Override
    public void updateResourceManager(Long resourceManagerId,
                                      ResourceManagerRequestDTO request,
                                      Principal principal) {
        // Verify authorization
        if (!canHandleResourceManager(principal, resourceManagerId)) {
            log.warn("ResourceManagerService::updateResourceManager cannot handle resource manager; " +
                    "resource manager ID '{}', connectedUser '{}';", resourceManagerId, principal.getName());
            throw new UnauthorizedException();
        }
        updateResourceManager(resourceManagerId, request);
        log.info("ResourceManagerService::updateResourceManager completed; resource manager ID '{}';", resourceManagerId);
    }

    @Override
    public Page<ResourceManagerResponseDTO> getResourceManager(String keyword, Pageable pageable, Principal principal) {
        AppUser connectedUser = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();

        Page<ResourceManager> resourceManagerPage = new PageImpl<>(new ArrayList<>(), pageable, 0);
        if (connectedUser.isAdmin()) {
            resourceManagerPage = resourceManagerRepository.findResourceManagersByKeyword(keyword, pageable);
        } else if (connectedUser.isHrbp()) {
            resourceManagerPage = resourceManagerRepository.getResourceManagersByConnectedHrbpId(keyword, connectedUser.getId(), pageable);
        } else if (connectedUser.isHr()) {
            resourceManagerPage = resourceManagerRepository.getResourceManagersByConnectedHrId(keyword, connectedUser.getId(), pageable);
        }

        List<ResourceManagerResponseDTO> resourceManagers = resourceManagerPage.getContent()
                .stream()
                .map(resourceManagerMapper::toResourceManagerDTO)
                .toList();

        log.info("ResourceManagerService::getResourceManager completed; resource managers size '{}', connected user '{}'",
                resourceManagers.size(), principal.getName());
        return new PageImpl<>(resourceManagers, pageable, resourceManagerPage.getTotalElements());
    }

    public boolean canHandleResourceManager(Principal principal, Long resourceManagerId) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal)
                .getPrincipal()).getId();
        AppUser connectedUser = userService.getUserById(connectedUserId);
        // Except for the admin, a user can handle only the resource managers that belong to their Macro PU
        if (connectedUser.isAdmin()) {
            return true;
        } else if (connectedUser.isHrbp()) {
            ResourceManager resourceManager = getResourceManagerById(resourceManagerId);
            Set<ResourceManager> resourceManagers = new HashSet<>();
            ((Hrbp) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    resourceManagers.addAll(macroPeopleUnit.getResourceManagers()));
            return resourceManagers.contains(resourceManager);
        } else if (connectedUser.isHr()) {
            ResourceManager resourceManager = getResourceManagerById(resourceManagerId);
            Set<ResourceManager> resourceManagers = new HashSet<>();
            ((Hr) connectedUser).getMacroPeopleUnits().forEach(macroPeopleUnit ->
                    resourceManagers.addAll(macroPeopleUnit.getResourceManagers()));
            return resourceManagers.contains(resourceManager);
        }
        return false;
    }
}