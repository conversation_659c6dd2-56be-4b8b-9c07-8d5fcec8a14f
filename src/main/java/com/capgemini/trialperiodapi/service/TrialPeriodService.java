package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.response.TrialPeriodResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.TrialPeriodMapper;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.CollaboratorRepository;
import com.capgemini.trialperiodapi.repository.TrialPeriodRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrialPeriodService implements ITrialPeriodService {
    private static final int TRIAL_PERIOD_DAYS_BEFORE_NOTIFY = 21;
    public static final String INTERCONTRAT = "Intercontrat";

    private final CollaboratorRepository collaboratorRepository;
    private final TrialPeriodRepository trialPeriodRepository;
    private final ICollaboratorService collaboratorService;
    private final IPeopleUnitService peopleUnitService;
    private final MessageSourceUtil messageSourceUtil;
    private final TrialPeriodMapper trialPeriodMapper;
    private final IEmailService emailService;

    @Override
    public List<TrialPeriodResponseDTO> getImminentTrialPeriodDTOs(Principal principal) {
        List<TrialPeriodResponseDTO> trialPeriods = trialPeriodRepository.findAll(withFilters(principal))
                .stream().map(trialPeriodMapper::toTrialPeriodResponseDTO)
                .toList();
        log.info("TrialPeriodService::getImminentTrialPeriodDTOs imminent trial periods size '{}';", trialPeriods.size());
        return trialPeriods;
    }

    @Override
    public Integer getNotificationCount(Principal principal) {
        int count = trialPeriodRepository.findAll(withFilters(principal)).size();
        log.trace("TrialPeriodService::getImminentTrialPeriodDTOs count '{}';", count);
        return count;
    }

    public Specification<TrialPeriod> withFilters(Principal principal) {
        AppUser connectedUser =
                (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();

        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.or(
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("status"), TrialPeriodStatus.IN_PROGRESS),
                            criteriaBuilder.lessThan(root.get("firstTrialPeriodNotificationDate"), LocalDate.now()),
                            criteriaBuilder.or(
                                    criteriaBuilder.isNull(root.get("firstTrialPeriodFeedbackRequestSentOn")),
                                    criteriaBuilder.lessThan(root.get("firstTrialPeriodFeedbackRequestSentOn"), root.get("lastComputedOn"))
                            ),
                            criteriaBuilder.or(
                                    criteriaBuilder.isNull(root.get("firstTrialPeriodLastReminderDate")),
                                    criteriaBuilder.lessThan(root.get("firstTrialPeriodLastReminderDate"), root.get("lastComputedOn"))
                            )
                    ),
                    criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("status"), TrialPeriodStatus.RENEWED),
                            criteriaBuilder.lessThan(root.get("secondTrialPeriodNotificationDate"), LocalDate.now()),
                            criteriaBuilder.or(
                                    criteriaBuilder.isNull(root.get("secondTrialPeriodFeedbackRequestSentOn")),
                                    criteriaBuilder.lessThan(root.get("secondTrialPeriodFeedbackRequestSentOn"), root.get("lastComputedOn"))
                            ),
                            criteriaBuilder.or(
                                    criteriaBuilder.isNull(root.get("secondTrialPeriodLastReminderDate")),
                                    criteriaBuilder.lessThan(root.get("secondTrialPeriodLastReminderDate"), root.get("lastComputedOn"))
                            )
                    )
            );

            if (connectedUser.isHrbp() || connectedUser.isHr()) {
                List<PeopleUnit> connectedUserPeopleUnits = peopleUnitService.getPeopleUnitsByConnectedUser(connectedUser);
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.and(root.get("collaborator").get("peopleUnit").in(connectedUserPeopleUnits)));
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("hidden")));
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("scorecardSent")));
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNull(root.get("collaborator").get("tenureDate")));

            Path<Object> sortAttribute = root.get("firstTrialPeriodNotificationDate");
            query.orderBy(criteriaBuilder.asc(sortAttribute));

            log.debug("TrialPeriodService::withFilters completed;");
            return predicate;
        };
    }

    public Set<DecisionMaker> getRecipientsByTrialPeriods(List<TrialPeriod> trialPeriods) {
        Set<DecisionMaker> recipients = new HashSet<>();
        trialPeriods.forEach(
                trialPeriod -> {
                    Collaborator collaborator = trialPeriod.getCollaborator();
                    MacroPeopleUnit macroPeopleUnit = collaborator
                            .getPeopleUnit().getMacroPeopleUnit();
                    Project project = collaborator.getProject();

                    if (collaborator.isInFirmProject()) {
                        recipients.addAll(project.getProjectManagers());
                    }
                    if (collaborator.isInShadowOrOpp0()) {
                        recipients.addAll(macroPeopleUnit.getPums());
                        recipients.addAll(macroPeopleUnit.getResourceManagers());
                    }
                }
        );
        if (recipients.isEmpty()) {
            log.error("TrialPeriodService::getRecipientsByTrialPeriods no decision maker, recipients is empty;");
            throw new IllegalStateException(messageSourceUtil.getMessage("project.has-no-decision-maker"));
        }
        log.debug("TrialPeriodService::getRecipientsByTrialPeriods completed, recipients size '{}';", recipients.size());
        return recipients;
    }

    @Override
    @Transactional
    public void notifyDecisionMakerByTrialPeriodIds(List<Long> trialPeriodIds, String connectedUserSessionPassword, Principal principal) {
        AppUser connectedUser = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal());

        List<TrialPeriod> trialPeriods = trialPeriodIds.stream()
                .map(this::getTrialPeriodById)
                .toList();

        trialPeriods.forEach(trialPeriod -> {
            Collaborator collaborator = trialPeriod.getCollaborator();
            if (collaborator.getTenureDate() != null) {
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "trial-period.collaborator-already-tenured", collaborator.fullName()));
            }
            if (trialPeriod.isScorecardSent()) {
                throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                        "trial-period.scorecard-already-sent", collaborator.fullName()));
            }
        });

        Set<DecisionMaker> recipients = getRecipientsByTrialPeriods(trialPeriods);
        Set<Project> projects = getProjectsByTrialPeriods(trialPeriods);

        /*
        This object help us to display project name for non assigned collaborators
        in the email template, it doesn't exist in the database (just in memory)
        */
        Project defaultProject = Project.builder()
                .name(INTERCONTRAT)
                .collaborators(trialPeriods.stream()
                        .map(TrialPeriod::getCollaborator)
                        .filter(collaborator -> collaborator.getProject() == null)
                        .collect(Collectors.toSet()))
                .build();
        projects.add(defaultProject);

        recipients.forEach(recipient -> {
                    List<Project> allProjects = projects.stream()
                            .filter(project -> isProjectBelongsToRecipient(recipient, project))
                            .toList();

                    Set<String> ccSet = getCc(recipient, allProjects);
                    emailService.sendFeedbackRequestEmail(
                            recipient.getEmail(),
                            recipient.getFirstname(),
                            ccSet,
                            allProjects,
                            connectedUser.getEmail(),
                            connectedUserSessionPassword
                    );
                    log.debug("TrialPeriodService::notifyDecisionMakerByTrialPeriodIds notification sent from '{}' to '{}';",
                            connectedUser.getLastname(), recipient.getLastname());

                    /* sendFeedbackRequestEmail method sends email from connected user email to decision maker,
                       considering the 5 emails per minute limit for outlook email server from the same email,
                       we should leave an interval of 12 seconds (60 sec / 5 emails) before sending the next. */
                    if (recipients.size() > 5) {
                        try {
                            Thread.sleep(12000);
                        } catch (InterruptedException e) {
                            log.warn("TrialPeriodService::notifyDecisionMakerByTrialPeriodIds interrupted thread !");
                            Thread.currentThread().interrupt();
                        }
                    }
                }
        );
        markFeedbackRequestsAsSent(trialPeriodIds);
        log.info("TrialPeriodService::notifyDecisionMakerByTrialPeriodIds completed; connected user '{}';", principal.getName());
    }

    @Override
    @Transactional
    public void notifyDecisionMakerByNotificationId(Long trialPeriodId, String connectedUserSessionPassword, Principal principal) {
        AppUser connectedUser = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal());

        TrialPeriod trialPeriod = getTrialPeriodById(trialPeriodId);
        Collaborator collaborator = trialPeriod.getCollaborator();

        if (collaborator.getTenureDate() != null) {
            log.warn("TrialPeriodService::notifyDecisionMakerByNotificationId collaborator already tenured;");
            throw new IllegalStateException(
                    messageSourceUtil.getMessageWithObject(
                            "trial-period.collaborator-already-tenured", collaborator.fullName()
                    ));
        }
        if (trialPeriod.isScorecardSent()) {
            log.warn("TrialPeriodService::notifyDecisionMakerByNotificationId scorecard already sent;");
            throw new IllegalStateException(messageSourceUtil.getMessageWithObject(
                    "trial-period.scorecard-already-sent", collaborator.fullName()));
        }

        Set<Project> projects = getProjectsByTrialPeriods(List.of(trialPeriod));
        Set<DecisionMaker> recipients = getRecipientsByTrialPeriods(List.of(trialPeriod));

        /*
        This object help us to display project for non assigned collaborators
        in the email template, it doesn't exist in the database (just in memory)
        */
        Project defaultProject = Project.builder()
                .name(INTERCONTRAT)
                .collaborators(Set.of(trialPeriod.getCollaborator()))
                .build();

        recipients.forEach(recipient -> {
                    List<Project> projectList = projects.stream()
                            .filter(project -> isProjectBelongsToRecipient(recipient, project))
                            .toList();

                    List<Project> allProjects = new ArrayList<>(projectList);
                    allProjects.add(defaultProject);

                    Set<String> ccSet = getCc(recipient, allProjects);

                    if (isReminder(trialPeriod)) {
                        emailService.sendReminderFeedbackRequestEmail(
                                recipient.getEmail(),
                                recipient.getFirstname(),
                                ccSet,
                                allProjects,
                                connectedUser.getEmail(),
                                connectedUserSessionPassword);
                        log.debug("TrialPeriodService::notifyDecisionMakerByNotificationId feedback reminder sent from '{}' to '{}';",
                                connectedUser.getLastname(), recipient.getLastname());
                    } else {
                        emailService.sendFeedbackRequestEmail(
                                recipient.getEmail(),
                                recipient.getFirstname(),
                                ccSet,
                                allProjects,
                                connectedUser.getEmail(),
                                connectedUserSessionPassword);
                        log.debug("TrialPeriodService::notifyDecisionMakerByNotificationId feedback request sent from '{}' to '{}';",
                                connectedUser.getLastname(), recipient.getLastname());
                    }
                }
        );
        markFeedbackRequestsAsSent(List.of(trialPeriodId));
        log.info("TrialPeriodService::notifyDecisionMakerByNotificationId completed; " +
                "connected user '{}'", connectedUser.getLastname());
    }

    private boolean isReminder(TrialPeriod trialPeriod) {
        boolean isReminder = (trialPeriod.isFirstTrialPeriod()
                && trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn() != null)
                || (!trialPeriod.isFirstTrialPeriod()
                && trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn() != null);
        log.trace("TrialPeriodService::isReminder completed; trial period ID '{}' is reminder ? '{}';",
                trialPeriod.getId(), isReminder);
        return isReminder;
    }

    private Set<String> getCc(DecisionMaker recipient, List<Project> projectList) {
        Set<String> ccSet = new HashSet<>();
        if (recipient instanceof ProjectManager) {
            Set<Collaborator> collaborators = new HashSet<>();
            projectList.forEach(project -> collaborators.addAll(project.getCollaborators()));
            collaborators.forEach(collaborator -> {
                ccSet.addAll(collaborator.getPeopleUnit().getMacroPeopleUnit().getResourceManagers().stream().map(DecisionMaker::getEmail).collect(Collectors.toSet()));
                ccSet.addAll(collaborator.getPeopleUnit().getMacroPeopleUnit().getPums().stream().map(DecisionMaker::getEmail).collect(Collectors.toSet()));
            });
        }
        log.trace("TrialPeriodService::getCc CCs size '{}'", ccSet.size());
        return ccSet;
    }

    @Override
    public void markFeedbackRequestsAsSent(List<Long> trailPeriodIds) {
        trailPeriodIds.forEach(
                periodId -> {
                    TrialPeriod trialPeriod = getTrialPeriodById(periodId);
                    if (trialPeriod.isFirstTrialPeriod()
                            && trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn() == null) {
                        trialPeriod.setFirstTrialPeriodFeedbackRequestSentOn(LocalDateTime.now());
                    }
                    if (trialPeriod.isFirstTrialPeriod()
                            && trialPeriod.getFirstTrialPeriodFeedbackRequestSentOn() != null) {
                        trialPeriod.setFirstTrialPeriodLastReminderDate(LocalDateTime.now());
                    }
                    if (!trialPeriod.isFirstTrialPeriod()
                            && trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn() == null) {
                        trialPeriod.setSecondTrialPeriodFeedbackRequestSentOn(LocalDateTime.now());
                    }
                    if (!trialPeriod.isFirstTrialPeriod()
                            && trialPeriod.getSecondTrialPeriodFeedbackRequestSentOn() != null) {
                        trialPeriod.setSecondTrialPeriodLastReminderDate(LocalDateTime.now());
                    }
                    trialPeriodRepository.save(trialPeriod);
                    log.trace("TrialPeriodService::markFeedbackRequestsAsSent trial period ID '{}' marked as sent;", trialPeriod.getId());
                }
        );
        log.debug("TrialPeriodService::markFeedbackRequestsAsSent completed;");
    }

    @Override
    public void hideFeedbackRequests(List<Long> trialPeriodIds) {
        trialPeriodIds.forEach(
                periodId -> {
                    TrialPeriod trialPeriod = getTrialPeriodById(periodId);
                    trialPeriod.setHidden(true);
                    trialPeriodRepository.save(trialPeriod);
                    log.trace("TrialPeriodService::hideFeedbackRequests trial period ID '{}' marked as hidden;", trialPeriod.getId());
                }
        );
        log.debug("TrialPeriodService::hideFeedbackRequests completed;");
    }

    @Override
    public TrialPeriodResponseDTO getTrialPeriodByCollaboratorId(Long collaboratorId) {
        TrialPeriod trialPeriod = trialPeriodRepository.findTrialPeriodByCollaboratorId(collaboratorId)
                .orElseThrow(() -> {
                    log.warn("TrialPeriodService::getTrialPeriodByCollaboratorId trial period " +
                            "by collaborator ID '{}' not found;", collaboratorId);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "trial-period.by-collaborator-id.not-found", collaboratorId));
                });

        TrialPeriodResponseDTO trialPeriodDTO = trialPeriodMapper.toTrialPeriodResponseDTO(trialPeriod);
        log.debug("TrialPeriodService::getTrialPeriodByCollaboratorId completed; trial period ID '{}'", trialPeriod.getId());
        return trialPeriodDTO;
    }

    @Override
    public TrialPeriodResponseDTO getTrialPeriodByCollaboratorId(Long collaboratorId, Principal principal) {
        if (!collaboratorService.canHandleCollaborator(principal, collaboratorId)) {
            log.warn("TrialPeriodService::getTrialPeriodByCollaboratorId cannot handle collaborator; " +
                    "collaborator ID '{}', connectedUser '{}';", collaboratorId, principal.getName());
            throw new UnauthorizedException();
        }
        TrialPeriodResponseDTO trialPeriod = getTrialPeriodByCollaboratorId(collaboratorId);
        log.info("TrialPeriodService::getTrialPeriodByCollaboratorId completed; trial period ID '{}';", trialPeriod.getId());
        return trialPeriod;
    }

    @Override
    public void handleTrialPeriod(Collaborator collaborator,
                                  TrialPeriodStatus status, boolean scorecardSent,
                                  boolean collaboratorEmailSent, String comment) {

        LocalDate entryDate = collaborator.getEntryDate();
        int trialPeriodDuration = collaborator.trialPeriodDuration();

        TrialPeriod trialPeriod = TrialPeriod.builder()
                .status(status)
                .firstTrialPeriodEndDate(entryDate.plusDays(trialPeriodDuration))
                .secondTrialPeriodEndDate(entryDate.plusDays(2L * trialPeriodDuration))
                .firstTrialPeriodNotificationDate(entryDate
                        .plusDays(trialPeriodDuration)
                        .minusDays(TRIAL_PERIOD_DAYS_BEFORE_NOTIFY))
                .secondTrialPeriodNotificationDate(entryDate
                        .plusDays(2L * trialPeriodDuration)
                        .minusDays(TRIAL_PERIOD_DAYS_BEFORE_NOTIFY))
                .scorecardSent(scorecardSent)
                .collaboratorEmailSent(collaboratorEmailSent)
                .comment(comment)
                .collaborator(collaborator)
                .lastComputedOn(LocalDateTime.now())
                .build();

        if (collaborator.getTrialPeriod() != null) {
            TrialPeriod oldTrialPeriod = collaborator.getTrialPeriod();
            trialPeriod.setId(oldTrialPeriod.getId());
            trialPeriod.setFirstTrialPeriodLastReminderDate(oldTrialPeriod.getFirstTrialPeriodLastReminderDate());
            trialPeriod.setSecondTrialPeriodLastReminderDate(oldTrialPeriod.getSecondTrialPeriodLastReminderDate());
            trialPeriod.setFirstTrialPeriodFeedbackRequestSentOn(oldTrialPeriod.getFirstTrialPeriodFeedbackRequestSentOn());
            trialPeriod.setSecondTrialPeriodFeedbackRequestSentOn(oldTrialPeriod.getSecondTrialPeriodFeedbackRequestSentOn());
        }

        trialPeriodRepository.saveAndFlush(trialPeriod);

        collaborator.setTrialPeriod(trialPeriod);
        collaboratorRepository.save(collaborator);

        log.debug("TrialPeriodService::handleTrialPeriod completed; trial period ID '{}';", trialPeriod.getId());
    }

    public TrialPeriod getTrialPeriodById(Long trialPeriodId) {
        TrialPeriod trialPeriod = trialPeriodRepository.findById(trialPeriodId)
                .orElseThrow(() -> {
                            log.warn("TrialPeriodService::getTrialPeriodById trial period by ID '{}' not found;", trialPeriodId);
                            return new EntityNotFoundException(
                                    messageSourceUtil.getMessageWithObject("trial-period.by-id.not-found", trialPeriodId)
                            );
                        }
                );
        log.debug("TrialPeriodService::getTrialPeriodById completed; trial period ID '{}';", trialPeriodId);
        return trialPeriod;
    }

    public boolean isProjectBelongsToRecipient(DecisionMaker recipient, Project project) {
        boolean isProjectBelongsToRecipient = false;
        for (Collaborator collaborator : project.getCollaborators()) {
            if (collaborator.isInFirmProject()) {
                isProjectBelongsToRecipient = recipient instanceof ProjectManager
                        && collaborator.getProject() == project
                        && project.getProjectManagers().contains(recipient);
            }
            if (collaborator.isInShadowOrOpp0()) {
                isProjectBelongsToRecipient =
                        recipient instanceof PeopleUnitManager pum && pum.getMacroPeopleUnits()
                                .contains(collaborator.getPeopleUnit().getMacroPeopleUnit())
                                || recipient instanceof ResourceManager rm && rm.getMacroPeopleUnits()
                                .contains(collaborator.getPeopleUnit().getMacroPeopleUnit());
            }
            if (isProjectBelongsToRecipient) {
                log.trace("TrialPeriodService::isProjectBelongsToRecipient completed; " +
                        "project '{}' belongs to recipient '{}';", project.getName(), recipient);
                return true;
            }
        }
        log.trace("TrialPeriodService::isProjectBelongsToRecipient completed; " +
                "project '{}' doesn't belong to recipient '{}';", project.getName(), recipient);
        return false;
    }

    public Set<Project> getProjectsByTrialPeriods(List<TrialPeriod> trialPeriods) {
        Set<Project> projects = new HashSet<>();
        trialPeriods.stream()
                .filter(trialPeriod -> trialPeriod.getCollaborator().getProject() != null)
                .forEach(trialPeriod -> {
                    Project collaboratorProject = trialPeriod.getCollaborator().getProject();
                    if (!projects.contains(collaboratorProject)) {
                        collaboratorProject.setCollaborators(new HashSet<>());
                    }
                    projects.add(collaboratorProject);
                    projects.forEach(project -> {
                        if (collaboratorProject == project) {
                            project.getCollaborators().add(trialPeriod.getCollaborator());
                        }
                    });
                });
        log.debug("TrialPeriodService::getProjectsByTrialPeriods completed; projects size '{}';", projects.size());
        return projects;
    }
}