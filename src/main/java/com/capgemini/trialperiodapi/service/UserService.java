package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.UserRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.service.auth.IVerificationTokenService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
@Slf4j
public class UserService implements IUserService {
    private final VerificationTokenRepository verificationTokenRepository;
    private final IVerificationTokenService tokenService;
    private final MessageSourceUtil messageSourceUtil;
    private final UserRepository userRepository;
    private final UserMapper userMapper;

    public UserService(IVerificationTokenService tokenService,
                       MessageSourceUtil messageSourceUtil,
                       VerificationTokenRepository verificationTokenRepository,
                       UserRepository userRepository,
                       UserMapper userMapper) {
        this.tokenService = tokenService;
        this.messageSourceUtil = messageSourceUtil;
        this.verificationTokenRepository = verificationTokenRepository;
        this.userRepository = userRepository;
        this.userMapper = userMapper;
    }

    @Override
    public AppUser getUserById(Long id) {
        AppUser user = userRepository.findUserById(id)
                .orElseThrow(() -> {
                    log.error("UserService::getUserById user by id '{}' not found;", id);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "user.by-id.not-found", id));
                });
        log.debug("UserService::getUserById user by ID '{}' recognized", id);
        return user;
    }

    @Override
    public void saveUserVerificationToken(AppUser user, String token) {
        String code = tokenService.generateActivationCode(6);
        VerificationToken verificationToken = new VerificationToken(user, token, code);
        verificationTokenRepository.save(verificationToken);
        log.debug("UserService::saveUserVerificationToken completed; user ID '{}';", user.getId());
    }

    @Override
    public void revokeAllUserTokens(AppUser user) {
        List<VerificationToken> validUserTokens = verificationTokenRepository
                .findValidVerificationTokensByUserId(user.getId());

        if (validUserTokens.isEmpty()) {
            log.debug("UserService::revokeAllUserTokens completed; No token to revoke; user ID '{}'", user.getId());
            return;
        }

        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
        });

        verificationTokenRepository.saveAll(validUserTokens);
        log.debug("UserService::revokeAllUserTokens completed; user ID '{}'", user.getId());
    }

    @Override
    public AppUser getUserByUsername(String username) {
        AppUser user = userRepository.findUserByUsername(username)
                .orElseThrow(() -> {
                    log.warn("UserService::getUserByUsername user by username '{}' not found", username);
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessageWithObject(
                                    "user.by-username.not-found", username));
                });
        log.debug("UserService::getUserByUsername completed; username '{}';", user.getUsername());
        return user;
    }

    @Override
    public AppUser getUserByEmail(String email) {
        log.trace("Fetching user by email '{}';", email);
        AppUser user = userRepository.findUserByEmail(email).orElseThrow(
                () -> {
                    log.warn("UserService::getUserByEmail user by email '{}' not found;", email);
                    return new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                            "user.by-email.not-found", email));
                }
        );
        log.debug("UserService::getUserByEmail completed; email '{}';", email);
        return user;
    }

    @Override
    public void deleteUserAccount(Long id) {
        Optional<AppUser> user = userRepository.findUserById(id);
        if (user.isPresent()) {
            log.trace("UserService::deleteUserAccount deleting user; user id '{}' ", id);
            List<VerificationToken> tokens = verificationTokenRepository.findAllVerificationTokensByUserId(id);
            verificationTokenRepository.deleteAll(tokens);
            userRepository.deleteById(id);
            log.trace("UserService::deleteUserAccount user by ID '{}' deleted;", id);
        } else {
            log.warn("UserService::deleteUserAccount user by ID '{}' not found;", id);
            throw new EntityNotFoundException(messageSourceUtil.getMessageWithObject(
                    "user.by-id.not-found", id));
        }
        log.debug("UserService::deleteUserAccount completed; user ID '{}';", id);
    }

    @Override
    public void validateUserForUpdate(Long id, String username, String email) {
        Optional<AppUser> optionalUserByUsername = userRepository.findUserByUsername(username);
        // Verify if user username is not already exists, excepting the processing user
        if (optionalUserByUsername.isPresent() && !optionalUserByUsername.get().getId().equals(id)) {
            log.warn("UserService::validateUserForUpdate username '{}' already exists", username);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "user.username-already-exists", username));
        }

        Optional<AppUser> optionalUserByEmail = userRepository.findUserByEmail(email);
        // Verify if user email is not already exists, excepting the processing user
        if (optionalUserByEmail.isPresent() && !optionalUserByEmail.get().getId().equals(id)) {
            log.warn("UserService::validateUserForUpdate email '{}' already exists", email);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "user.email-already-exists", email));
        }
        log.debug("UserService::validateUserForUpdate completed; user ID '{}';", id);
    }

    @Override
    public void updateConnectedUser(UserRequestDTO request, Principal principal) {
        AppUser user = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal());
        user.setFirstname(request.getFirstname());
        user.setLastname(request.getLastname());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        userRepository.save(user);
        log.info("UserService::updateConnectedUser completed; connected user '{}';", request.getLastname());
    }

    @Override
    public UserResponseDTO getConnectedUser(Principal principal) {
        Long connectedUserId = ((AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getId();
        AppUser connectedUser = getUserById(connectedUserId);
        if (connectedUser.isAdmin()) {
            log.debug("UserService::getConnectedUser completed; connected admin '{}';", principal.getName());
            return userMapper.toAdminDTO(connectedUser);
        } else if (connectedUser.isHrbp()) {
            log.debug("UserService::getConnectedUser completed; connected HRBP '{}';", principal.getName());
            return userMapper.toHrbpDTO(connectedUser);
        } else if (connectedUser.isHr()) {
            log.debug("UserService::getConnectedUser completed; connected HR '{}';", principal.getName());
            return userMapper.toHrDTO(connectedUser);
        } else {
            log.error("UserService::getConnectedUser unauthorized action; connected user '{}';", principal.getName());
            throw new UnauthorizedException();
        }
    }

    @Override
    public void validateUser(String username, String email) {
        if (userRepository.findUserByUsername(username).isPresent()) {
            log.warn("UserService::validateUser username already used '{}';", username);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                            "user.by-username.already-exists", username));
        }
        if (userRepository.findUserByEmail(email).isPresent()) {
            log.warn("UserService::validateUser email already used '{}';", email);
            throw new IllegalArgumentException(messageSourceUtil.getMessageWithObject(
                    "user.by-email.already-exists", email));
        }
        log.debug("UserService::validateUser completed; valid username '{}', valid email '{}';",
                username, email);
    }
}