package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.AuthenticationRequestDTO;
import com.capgemini.trialperiodapi.dto.request.RegisterRequestDTO;
import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.service.IEmailService;
import com.capgemini.trialperiodapi.service.IUserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthenticationService implements IAuthenticationService {
    public static final String BEARER = "Bearer ";

    private final AuthenticationManager authenticationManager;
    private final IVerificationTokenService tokenService;
    private final MessageSourceUtil messageSourceUtil;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final IEmailService emailService;
    private final IUserService userService;
    private final IJwtService jwtService;

    @Value("${app.frontend-activation-url}")
    private String activationUrl;

    @Override
    public AuthenticationResponseDTO register(@Valid RegisterRequestDTO request) {
        // Validate uniqueness of username and email
        userService.validateUser(request.getUsername(), request.getEmail());

        // Save User
        AppUser user = new AppUser();
        user.setFirstname(request.getFirstname());
        user.setLastname(request.getLastname());
        user.setEmail(request.getEmail());
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(Role.HRBP);
        userRepository.saveAndFlush(user);
        log.debug("AuthenticationService::register user saved successfully; username '{}';", user.getUsername());

        // Handle tokens
        AppUser saveUser = userService.getUserByEmail(request.getEmail());
        String accessToken = jwtService.generateAccessToken(saveUser);
        String refreshToken = jwtService.generateRefreshToken(saveUser);
        userService.saveUserVerificationToken(saveUser, accessToken);

        // Send activation email
        String activationCode = tokenService.getCodeByToken(accessToken);
        emailService.sendActivationAccountEmail(
                user.getEmail(),
                user.getUsername(),
                EmailTemplateName.ACTIVATE_ACCOUNT,
                activationUrl,
                activationCode
        );

        // Build response
        AuthenticationResponseDTO tokenResponse = AuthenticationResponseDTO
                .builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .build();
        log.info("AuthenticationService::register registration process completed; username '{}';",
                request.getUsername());
        return tokenResponse;
    }

    @Override
    public AuthenticationResponseDTO authenticate(AuthenticationRequestDTO request) {
        // Perform authentication
        String username = request.getUsername();
        authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(
                username, request.getPassword()));
        log.debug("AuthenticationService::register authentication performed; username '{}';", username);

        // Handle tokens
        AppUser user = userService.getUserByUsername(username);
        String accessToken = jwtService.generateAccessToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);
        userService.revokeAllUserTokens(user);
        userService.saveUserVerificationToken(user, accessToken);

        // Build response
        AuthenticationResponseDTO tokenResponse = AuthenticationResponseDTO.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .role(user.getRole())
                .build();
        log.info("AuthenticationService::register completed; username '{}';", username);
        return tokenResponse;
    }

    @Override
    public void refreshToken(HttpServletRequest request, HttpServletResponse response) {
        final String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
        final String refreshToken;
        final String username;

        // Validate bearer token
        if (authHeader == null || !authHeader.startsWith(BEARER)) {
            log.warn("AuthenticationService::refreshToken invalid or missing " +
                    "authorization header for '{}';", request.getRequestURI());
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.token.invalid")
            );
        }

        // Extract 'Bearer ' from bearer token
        refreshToken = authHeader.substring(7);

        username = jwtService.extractUsername(refreshToken);
        if (username != null) {
            AppUser user = userService.getUserByUsername(username);
            if (jwtService.isTokenValid(refreshToken, user)) {
                // Revoke old tokens
                userService.revokeAllUserTokens(user);

                // regenerate new token and build response
                String accessToken = jwtService.generateAccessToken(user);
                userService.saveUserVerificationToken(user, accessToken);
                AuthenticationResponseDTO authResponse = AuthenticationResponseDTO.builder()
                        .accessToken(accessToken)
                        .refreshToken(refreshToken)
                        .role(user.getRole())
                        .build();
                log.debug("AuthenticationService::refreshToken authentication response built; username '{}';", username);

                try {
                    new ObjectMapper().writeValue(response.getOutputStream(), authResponse);
                } catch (IOException e) {
                    log.error("AuthenticationService::refreshToken failed to build new token response; username '{}';",
                            username);
                    throw new IllegalStateException(messageSourceUtil.getMessage("exception.refresh-token.failed"));
                }
            }
        }
        log.debug("AuthenticationService::refreshToken completed; username '{}';", username);
    }

    @Override
    public void verifyEmail(String code) {
        if (code.isBlank()) {
            log.warn("AuthenticationService::verifyEmail code is blank;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.verification-code.blank")
            );
        }

        // Get user from code
        String token = tokenService.getTokenByCode(code);
        UserDetails userDetails = tokenService.getVerificationTokenByToken(token).getUser();
        AppUser user = userService.getUserByUsername(userDetails.getUsername());

        // No action required if user is already enabled
        if (user.isEnabled()) {
            log.warn("AuthenticationService::verifyEmail user is already enabled; user ID '{}';", user.getId());
            throw new IllegalStateException(
                    messageSourceUtil.getMessage("user.email-already-verified")
            );
        }

        // Verify if token is valid
        if (!jwtService.isTokenValid(token, userDetails)) {
            log.error("AuthenticationService::verifyEmail token already expired; user ID '{}';", user.getId());
            throw new ExpiredJwtException(null, null,
                    messageSourceUtil.getMessage("verification-token.invalid")
            );
        }

        // Enable user
        user.setEnabled(true);
        userRepository.save(user);
        log.info("AuthenticationService::verifyEmail completed; user ID '{}';", user.getId());
    }
}