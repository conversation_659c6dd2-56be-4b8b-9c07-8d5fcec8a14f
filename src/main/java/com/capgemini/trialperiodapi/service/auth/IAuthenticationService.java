package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.AuthenticationRequestDTO;
import com.capgemini.trialperiodapi.dto.request.RegisterRequestDTO;
import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

public interface IAuthenticationService {

    AuthenticationResponseDTO register(@Valid RegisterRequestDTO request);

    AuthenticationResponseDTO authenticate(AuthenticationRequestDTO request);

    void refreshToken(HttpServletRequest request, HttpServletResponse response);

    void verifyEmail(String code);
}
