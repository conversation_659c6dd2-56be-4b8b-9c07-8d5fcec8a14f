package com.capgemini.trialperiodapi.service.auth;

import org.springframework.security.core.userdetails.UserDetails;

import java.util.Map;

public interface IJwtService {
    String extractUsername(String token);

    String generateAccessToken(UserDetails userDetails);

    String generateAccessToken(Map<String, Object> extraClaims,
                               UserDetails userDetails);

    String buildToken(Map<String, Object> extraClaims,
                      UserDetails userDetails,
                      long expiration);

    String generateRefreshToken(UserDetails userDetails);

    boolean isTokenValid(String token, UserDetails userDetails);

    boolean isTokenExpired(String token);
}
