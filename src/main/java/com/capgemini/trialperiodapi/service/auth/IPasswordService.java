package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.ChangePasswordRequestDTO;
import com.capgemini.trialperiodapi.dto.request.ResetPasswordRequestDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.PasswordResetToken;
import jakarta.transaction.Transactional;

import java.security.Principal;

public interface IPasswordService {
    void changePassword(ChangePasswordRequestDTO passwordRequest, Principal principal);

    void forgotPassword(String email, String connectedUserSessionPassword);

    @Transactional
    String generatePasswordResetTokenForUser(AppUser user);

    void resetPassword(ResetPasswordRequestDTO passwordRequest, String code);

    PasswordResetToken getValidPasswordResetTokenByCode(String code);

    boolean isPasswordExpired(Principal principal);

    boolean isPasswordExpiringSoon(Principal userPrincipal);
}
