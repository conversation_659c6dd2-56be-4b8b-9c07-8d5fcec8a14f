package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
@Slf4j
@RequiredArgsConstructor
public class JwtService implements IJwtService {

    private final MessageSourceUtil messageSourceUtil;

    @Value("${app.jwt-secret-key}")
    private String secretKey;

    @Value("${app.access-token-expiration}")
    private Long accessTokenExpiration;

    @Value("${app.refresh-token-expiration}")
    private Long refreshTokenExpiration;

    @Override
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    @Override
    public String generateAccessToken(UserDetails userDetails) {
        return generateAccessToken(new HashMap<>(), userDetails);
    }

    @Override
    public String generateAccessToken(Map<String, Object> extraClaims,
                                      UserDetails userDetails) {
        String accessToken = buildToken(extraClaims, userDetails, accessTokenExpiration);
        log.debug("JwtService::generateAccessToken completed; username '{}';", userDetails.getUsername());
        return accessToken;
    }

    @Override
    public String generateRefreshToken(UserDetails userDetails) {
        String refreshToken = buildToken(new HashMap<>(), userDetails, refreshTokenExpiration);
        log.debug("JwtService::generateRefreshToken completed; username '{}';", userDetails.getUsername());
        return refreshToken;
    }

    @Override
    public String buildToken(Map<String, Object> extraClaims,
                             UserDetails userDetails,
                             long expiration) {
        String token = Jwts.builder()
                .setClaims(extraClaims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
        log.trace("JwtService::buildToken completed; username '{}';", userDetails.getUsername());
        return token;
    }

    @Override
    public boolean isTokenValid(String token, UserDetails userDetails) {
        try {
            String username = extractUsername(token);
            boolean isValid = username.equals(userDetails.getUsername()) && !isTokenExpired(token);
            log.debug("JwtService::isTokenValid completed; isTokenValid '{}'; username '{}';",
                    isValid, userDetails.getUsername());
            return isValid;

        } catch (ExpiredJwtException ex) {
            log.warn("JwtService::isTokenValid token validation failed due to expiration; username '{}';",
                    userDetails.getUsername());
            return false;

        } catch (Exception ex) {
            log.error("JwtService::isTokenValid token validation failed '{}'; username '{}';",
                    ex.getMessage(), userDetails.getUsername());
            return false;
        }
    }

    private <T> T extractClaim(String token, Function<Claims, T> claimResolver) {
        final Claims claims = extractAllClaims(token);
        T extractedClaim = claimResolver.apply(claims);
        log.trace("JwtService::extractClaim completed;");
        return extractedClaim;
    }

    private Claims extractAllClaims(String token) {
        try {
            Claims claims = Jwts
                    .parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            log.trace("JwtService::extractAllClaims completed;");
            return claims;

        } catch (ExpiredJwtException e) {
            log.error("JwtService::extractAllClaims get claims from expired token;");

            return e.getClaims();
        }
    }

    private Date extractExpiration(String token) {
        Date extractedExpirationDate = extractClaim(token, Claims::getExpiration);
        log.trace("JwtService::extractExpiration completed;");
        return extractedExpirationDate;
    }

    @Override
    public boolean isTokenExpired(String token) {
        try {
            boolean isExpired = extractExpiration(token).before(new Date());
            log.debug("JwtService::isTokenExpired completed; isTokenExpired '{}';", isExpired);
            return isExpired;

        } catch (ExpiredJwtException ex) {
            log.warn("JwtService::isTokenExpired token expired;");
            return true;

        } catch (Exception ex) {
            log.error("JwtService::isTokenExpired token expiration check failed;");
            throw new IllegalStateException(messageSourceUtil.getMessage("exception.token.invalid"));
        }
    }

    private Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        Key key = Keys.hmacShaKeyFor(keyBytes);
        log.trace("JwtService::getSigningKey completed;");
        return key;
    }
}