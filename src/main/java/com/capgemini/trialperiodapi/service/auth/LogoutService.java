package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class LogoutService implements LogoutHandler {
    private final MessageSourceUtil messageSourceUtil;
    private final VerificationTokenRepository verificationTokenRepository;

    @Override
    public void logout(HttpServletRequest request,
                       HttpServletResponse response,
                       Authentication authentication) {
        // Validate bearer token
        final String authHeader = request.getHeader("Authorization");
        final String jwt;
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return;
        }

        // Extract 'Bearer ' from bearer token
        jwt = authHeader.substring(7);

        VerificationToken storedToken = verificationTokenRepository.findVerificationTokenByToken(jwt)
                .orElseThrow(() -> {
                    log.error("LogoutService::logout verification token not found;");
                    throw new EntityNotFoundException(
                            messageSourceUtil.getMessage("verification-token.not-found")
                    );
                });

        // Revoke and expire token
        storedToken.setRevoked(true);
        storedToken.setExpired(true);
        verificationTokenRepository.save(storedToken);
        log.debug("LogoutService::logout token successfully expired and revoked;");
        log.info("LogoutService::logout completed;");
    }
}