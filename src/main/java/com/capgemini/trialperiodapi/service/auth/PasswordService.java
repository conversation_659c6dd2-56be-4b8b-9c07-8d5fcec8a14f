package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.ChangePasswordRequestDTO;
import com.capgemini.trialperiodapi.dto.request.ResetPasswordRequestDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.PasswordResetToken;
import com.capgemini.trialperiodapi.repository.PasswordResetTokenRepository;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.service.EmailService;
import com.capgemini.trialperiodapi.service.UserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class PasswordService implements IPasswordService {
    private final PasswordResetTokenRepository passwordTokenRepository;
    private final VerificationTokenService tokenService;
    private final MessageSourceUtil messageSourceUtil;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final EmailService emailService;
    private final UserService userService;

    @Value("${app.frontend-reset-password-url}")
    private String resetPasswordUrl;

    private static final long TOKEN_EXPIRATION = 3_600_000; // one hour

    @Override
    public void changePassword(ChangePasswordRequestDTO passwordRequest, Principal principal) {
        AppUser user = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();
        String currentPassword = user.getPassword();

        // Verify current password
        if (!passwordEncoder.matches(passwordRequest.getCurrentPassword(), currentPassword)) {
            log.warn("PasswordService::changePassword wrong password; username '{}';", user.getUsername());
            throw new BadCredentialsException(
                    messageSourceUtil.getMessage("password.wrong")
            );
        }

        // Verify confirm password
        if (!passwordRequest.getNewPassword().equals(passwordRequest.getConfirmPassword())) {
            log.warn("PasswordService::changePassword password are not the same");
            throw new IllegalStateException(messageSourceUtil.getMessage("password.not-match"));
        }

        // Verify if new password and old one are same
        if (passwordEncoder.matches(passwordRequest.getNewPassword(), currentPassword)) {
            log.warn("PasswordService::changePassword new password is similar to your old one");
            throw new IllegalStateException(messageSourceUtil.getMessage("password.similar-update"));
        }

        // Set the new password
        user.setPassword(passwordEncoder.encode(passwordRequest.getNewPassword()));
        user.setLastPasswordUpdatedOn(LocalDateTime.now());
        userRepository.save(user);
        log.info("PasswordService::changePassword completed;");
    }

    @Override
    @Transactional
    public void forgotPassword(String email, String connectedUserSessionPassword) {
        if (email.isBlank()) {
            log.warn("PasswordService::forgotPassword email is blank;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("password.forgot.required-email")
            );
        }

        AppUser user = userService.getUserByEmail(email);
        if (user == null) {
            log.error("PasswordService::forgotPassword user is blank;");
            throw new EntityNotFoundException(
                    messageSourceUtil.getMessageWithObject("user.by-email.not-found", email)
            );
        }

        // Create, save password reset token for user and generate 6 length code to send
        String code = generatePasswordResetTokenForUser(user);
        log.debug("PasswordService::forgotPassword reset token generated '{}';", user.getUsername());

        // Send forgot password email to user
        emailService.sendForgotPasswordEmail(
                user.getEmail(),
                user.getUsername(),
                resetPasswordUrl,
                code,
                messageSourceUtil.getMessage("mail.subject.reset-password"),
                connectedUserSessionPassword
        );
        log.info("PasswordService::forgotPassword forgot password email sent to '{}';", user.getUsername());
    }

    @Transactional
    @Override
    public String generatePasswordResetTokenForUser(AppUser user) {
        PasswordResetToken optionalToken = passwordTokenRepository.findPasswordResetTokenByUserId(user.getId())
                .orElse(null);
        String token = UUID.randomUUID().toString();

        if (optionalToken == null) {
            // Create new password reset token in case user haven't sent a reset request before
            String code = tokenService.generateActivationCode(6);
            PasswordResetToken passwordResetToken = new PasswordResetToken(token, code, user);
            passwordTokenRepository.save(passwordResetToken);
            log.debug("PasswordService::generatePasswordResetTokenForUser completed (by creating new token); username '{}';", user.getUsername());
            return code;

        } else {
            // Update old password reset token in case user have sent a reset request before
            String code = tokenService.generateActivationCode(6);
            optionalToken.setToken(token);
            optionalToken.setCode(code);
            optionalToken.setUser(user);
            optionalToken.setExpirationTime(new Date(System.currentTimeMillis() + TOKEN_EXPIRATION));
            passwordTokenRepository.save(optionalToken);
            log.debug("PasswordService::generatePasswordResetTokenForUser completed (by updating old token); username '{}';", user.getUsername());
            return code;
        }
    }

    @Override
    public void resetPassword(ResetPasswordRequestDTO passwordRequest, String code) {
        if (code.isBlank()) {
            log.warn("PasswordService::resetPassword code is blank;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.verification-code.blank")
            );
        }

        PasswordResetToken token = getValidPasswordResetTokenByCode(code);

        // Verify confirm password
        if (!passwordRequest.getNewPassword().equals(passwordRequest.getConfirmPassword())) {
            log.warn("PasswordService::resetPassword password are not the same;");
            throw new IllegalStateException(messageSourceUtil.getMessage("password.not-match"));
        }

        // Verify if new password and old one are same
        AppUser user = token.getUser();
        if (passwordEncoder.matches(passwordRequest.getNewPassword(), user.getPassword())) {
            log.warn("PasswordService::changePassword new password is similar to your old one");
            throw new IllegalStateException(messageSourceUtil.getMessage("password.similar-update"));
        }

        // Set new password for user
        user.setPassword(passwordEncoder.encode(passwordRequest.getNewPassword()));
        userRepository.save(user);
        log.info("PasswordService::resetPassword completed;");
    }

    private boolean isTokenExpired(PasswordResetToken token) {
        final Calendar calendar = Calendar.getInstance();
        boolean isTokenExpired = token.getExpirationTime().before(calendar.getTime());
        log.debug("PasswordService::isTokenExpired completed; isTokenExpired '{}';", isTokenExpired);
        return isTokenExpired;
    }

    @Override
    public PasswordResetToken getValidPasswordResetTokenByCode(String code) {
        if (code == null) {
            log.debug("PasswordService::getValidPasswordResetTokenByCode code is null;");
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.verification-code.blank")
            );
        }

        PasswordResetToken token = passwordTokenRepository.findPasswordResetTokenByCode(code);
        if (token == null) {
            log.debug("PasswordService::getValidPasswordResetTokenByCode no token matches code;");
            throw new IllegalArgumentException(messageSourceUtil.getMessage("password-token.not-found"));
        }
        if (isTokenExpired(token)) {
            log.debug("PasswordService::getValidPasswordResetTokenByCode token is expired;");
            throw new IllegalArgumentException(messageSourceUtil.getMessage("password-token.expired"));
        }
        log.info("PasswordService::getValidPasswordResetTokenByCode completed;");
        return token;
    }

    @Override
    public boolean isPasswordExpired(Principal principal) {
        boolean passwordExpiringAtDays = isPasswordExpiringAtDays(principal, 90);
        log.trace("PasswordService::isPasswordExpired completed; connected user '{}', password expired '{}'",
                principal == null ? null : principal.getName(), passwordExpiringAtDays);
        return passwordExpiringAtDays;
    }

    @Override
    public boolean isPasswordExpiringSoon(Principal principal) {
        boolean passwordExpiringAtDays = isPasswordExpiringAtDays(principal, 75);
        log.debug("PasswordService::isPasswordExpiringSoon completed; connected user '{}', password expire soon '{}'",
                principal == null ? null : principal.getName(), passwordExpiringAtDays);
        return passwordExpiringAtDays;
    }

    private boolean isPasswordExpiringAtDays(Principal principal, int numberOfDays) {
        if (principal == null) {
            return false;
        }
        AppUser user = (AppUser) ((UsernamePasswordAuthenticationToken) principal).getPrincipal();
        LocalDateTime lastPasswordChange = user.getLastPasswordUpdatedOn();
        LocalDateTime daysAgo = LocalDateTime.now().minusDays(numberOfDays);
        boolean isExpired = lastPasswordChange.isBefore(daysAgo);
        log.debug("PasswordService::isPasswordExpiringAtDays completed; connected user '{}', numberOfDays '{}', password expired '{}'",
                principal.getName(), numberOfDays, isExpired);
        return isExpired;
    }
}
