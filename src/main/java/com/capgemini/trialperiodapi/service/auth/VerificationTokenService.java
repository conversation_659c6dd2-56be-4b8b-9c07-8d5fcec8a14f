package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;

@Service
@RequiredArgsConstructor
@Slf4j
public class VerificationTokenService implements IVerificationTokenService {
    private final VerificationTokenRepository verificationTokenRepository;
    private final MessageSourceUtil messageSourceUtil;

    public VerificationToken getVerificationTokenByToken(String token) {
        VerificationToken verificationToken = verificationTokenRepository.findVerificationTokenByToken(token)
                .orElseThrow(() -> {
                    log.error("VerificationTokenService::getVerificationTokenByToken verification token not found;");
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessage("verification-token.not-found")
                    );
                });
        log.debug("VerificationTokenService::getVerificationTokenByToken completed;");
        return verificationToken;
    }

    @Override
    public String getTokenByCode(String code) {
        String token = verificationTokenRepository.findVerificationTokenByCode(code)
                .map(VerificationToken::getToken)
                .orElseThrow(() -> {
                    log.error("VerificationTokenService::getTokenByCode verification token not found;");
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessage("verification-token.not-found")
                    );
                });
        log.debug("VerificationTokenService::getTokenByCode completed;");
        return token;
    }

    @Override
    public String getCodeByToken(String token) {
        String verificationToken = verificationTokenRepository.findVerificationTokenByToken(token)
                .map(VerificationToken::getCode)
                .orElseThrow(() -> {
                    log.error("VerificationTokenService::getCodeByToken code not found;");
                    return new EntityNotFoundException(
                            messageSourceUtil.getMessage("verification-token.not-found")
                    );
                });
        log.debug("VerificationTokenService::getCodeByToken completed;");
        return verificationToken;
    }

    @Override
    public String generateActivationCode(int length) {
        String characters = "0123456789";
        StringBuilder codeBuilder = new StringBuilder();
        SecureRandom secureRandom = new SecureRandom();
        for (int i = 0; i < length; i++) {
            int randomIndex = secureRandom.nextInt(characters.length());
            codeBuilder.append(characters.charAt(randomIndex));
        }
        log.debug("VerificationTokenService::generateActivationCode completed;");
        return codeBuilder.toString();
    }
}
