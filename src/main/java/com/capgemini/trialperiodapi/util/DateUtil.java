package com.capgemini.trialperiodapi.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
@RequiredArgsConstructor
@Slf4j
public class DateUtil {
    public LocalDate parseDate(String date, DateTimeFormatter formatter) {
        if (isBlank(date)) {
            return null;
        } else {
            return LocalDate.parse(date, formatter);
        }
    }

    public LocalDateTime parseDateTime(String date, DateTimeFormatter formatter) {
        if (isBlank(date)) {
            return null;
        }
        return Optional.ofNullable(parseToLocalDateTime(date, formatter))
                .orElse(parseToLocalDate(date, formatter));
    }

    public LocalDateTime parseToLocalDate(String date, DateTimeFormatter formatter) {
        try {
            return LocalDate.parse(date, formatter).atStartOfDay();
        } catch (DateTimeParseException e) {
            log.trace("DateUtil::parseDateTime unable to parse string date '{}';", date, e);
            return null;
        }
    }

    public LocalDateTime parseToLocalDateTime(String date, DateTimeFormatter formatter) {
        try {
            return LocalDateTime.parse(date, formatter);
        } catch (DateTimeParseException e) {
            log.trace("DateUtil::parseToLocalDateTime cannot parse string date in LocalDateTime; string date '{}'", date, e);
            return null;
        }
    }
}