package com.capgemini.trialperiodapi.util;

import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
@Slf4j
public class FileUtil {

    private final MessageSourceUtil messageUtil;

    public void checkFileExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extension == null || !extension.equals("csv")) {
            log.warn("AbstractUploadService::upload not a CSV file;");
            throw new IllegalArgumentException(messageUtil.getMessage("exception.file.csv-format"));
        }
    }

    public void checkFileEncoding(MultipartFile file) {
        byte[] fileBytes;
        try {
            fileBytes = IOUtils.toByteArray(file.getInputStream());

            CharsetDetector detector = new CharsetDetector();
            detector.setText(fileBytes);
            CharsetMatch match = detector.detect();

            if (match == null || !StandardCharsets.UTF_8.name().equals(match.getName())) {
                log.warn("AbstractUploadService::checkFileEncoding bad encoding file;");
                throw new IllegalArgumentException(
                        messageUtil.getMessage("exception.file.invalid-encoding")
                );
            }
            log.debug("AbstractUploadService::checkFileEncoding completed; UTF_8 valid encoding;");
        } catch (IOException e) {
            log.error("AbstractUploadService::checkFileEncoding failed due to an IOException: {}", e.getMessage(), e);
            throw new IllegalArgumentException(
                    messageUtil.getMessage("exception.file.encoding-validation")
            );
        }
    }
}