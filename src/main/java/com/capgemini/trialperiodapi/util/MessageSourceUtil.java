package com.capgemini.trialperiodapi.util;

import lombok.RequiredArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class MessageSourceUtil {

    private final MessageSource messageSource;

    public String getMessage(String messageKey) {
        return messageSource.getMessage(messageKey, null, LocaleContextHolder.getLocale());
    }

    public String getMessageWithObject(String messageKey, Object object) {
        return messageSource
                .getMessage(messageKey, new Object[]{object}, LocaleContextHolder.getLocale());
    }

    public String getMessageWithObjects(String messageKey, List<Object> objects) {
        Object[] args = objects.toArray(new Object[0]);
        return messageSource.getMessage(messageKey, args, LocaleContextHolder.getLocale());
    }

    public Map<String, String> buildResponse(String messageKey) {
        String message = messageSource
                .getMessage(messageKey, null, LocaleContextHolder.getLocale());
        return Map.of("message", message);
    }

    public Map<String, String> buildResponseWithObject(String messageKey, Object object) {
        String message = messageSource
                .getMessage(messageKey, new Object[]{object}, LocaleContextHolder.getLocale());
        return Map.of("message", message);
    }
}
