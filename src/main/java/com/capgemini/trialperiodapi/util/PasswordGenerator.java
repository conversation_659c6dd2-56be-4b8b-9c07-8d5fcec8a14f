package com.capgemini.trialperiodapi.util;

public class PasswordGenerator {

    private PasswordGenerator() {
        throw new IllegalStateException("Utility class");
    }

    public static String generatePassword() {
        return "Password.0000";
        /** String allowedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789._%+-";
        Random random = new Random();
        StringBuilder password = new StringBuilder();

        while (password.length() < 12) {
            char randomChar = allowedCharacters.charAt(random.nextInt(allowedCharacters.length()));
            password.append(randomChar);
        }

        return password.toString(); **/
    }
}
