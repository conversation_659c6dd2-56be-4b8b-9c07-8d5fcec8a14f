package com.capgemini.trialperiodapi.util;

import java.util.regex.Pattern;

public class SearchValidator {

    private SearchValidator() {
        throw new IllegalStateException("Utility class");
    }

    private static final Pattern VALID_SEARCH_PATTERN = Pattern.compile("[a-zA-Z0-9 ]+");

    public static boolean isValidSearchTerm(String term) {
        return term != null && VALID_SEARCH_PATTERN.matcher(term).matches();
    }
}
