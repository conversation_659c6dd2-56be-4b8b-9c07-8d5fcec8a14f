package com.capgemini.trialperiodapi.util;

import com.capgemini.trialperiodapi.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@RequiredArgsConstructor
@Slf4j
public class StringUtil {
    private final MessageSourceUtil messageSourceUtil;
    private final UserRepository userRepository;

    public String extractEmailFromCsvColumn(String line) {
        int startIndex = line.indexOf('<');
        int endIndex = line.indexOf('>');
        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
            return line.substring(startIndex + 1, endIndex);
        } else {
            log.error("StringUtil::extractFirstnameFromCsvColumn extract email failed; line '{}'", line);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.file.extract-email-failed")
            );
        }
    }

    public String extractFirstnameFromCsvColumn(String line) {
        int endIndex = line.indexOf('<');
        if (endIndex != -1) {
            String fullNameWithComma = line.substring(0, endIndex).trim();
            return extractFirstname(fullNameWithComma);
        } else {
            log.error("StringUtil::extractFirstnameFromCsvColumn extract first name failed; line '{}'", line);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.file.extract-first-name-failed")
            );
        }
    }

    public String extractLastnameFromCsvColumn(String line) {
        int endIndex = line.indexOf('<');
        if (endIndex != -1) {
            String fullNameWithComma = line.substring(0, endIndex).trim();
            return extractLastname(fullNameWithComma);
        } else {
            log.error("StringUtil::extractFirstnameFromCsvColumn extract last name failed; line '{}'", line);
            throw new IllegalArgumentException(
                    messageSourceUtil.getMessage("exception.file.extract-last-name-failed")
            );
        }
    }

    public String generateUsernameFromFirstnameAndLastname(String firstname, String lastname) {
        String username = firstname.substring(0, 1).concat(lastname);
        if (userRepository.findUserByUsername(username).isPresent()) {
            String refinedFirstname = firstname.replace(" ", "-");
            String refinedLastname = lastname.replace(" ", "-");
            username = refinedFirstname.concat(".").concat(refinedLastname);
        }
        return username;
    }

    public static String extractFirstname(String fullName) {
        return extractFirstnameAndLastname(fullName)[1];
    }

    public static String extractLastname(String fullName) {
        return extractFirstnameAndLastname(fullName)[0];
    }
    
    private static String[] extractFirstnameAndLastname(String commaSeparated) {
        if (!commaSeparated.contains(",")) {
            throw new IllegalArgumentException("L'entrée doit être au format 'Nom, Prénom'");
        }
        String trimmedInput = commaSeparated.trim()
                .replaceAll("\\s*,\\s*", ",")
                .replaceAll("\\s+", " ");

        String[] parts = trimmedInput.split(",");

        if (parts.length != 2) {
            throw new IllegalArgumentException("L'entrée doit être au format 'Nom, Prénom'");
        }

        return parts;
    }
}
