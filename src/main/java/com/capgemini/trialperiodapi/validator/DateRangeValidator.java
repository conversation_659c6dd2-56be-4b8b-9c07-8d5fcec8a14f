package com.capgemini.trialperiodapi.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.time.LocalDate;

public class DateRangeValidator implements ConstraintValidator<ValidEntryDate, LocalDate> {
    @Override
    public void initialize(ValidEntryDate constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(LocalDate value, ConstraintValidatorContext constraintValidatorContext) {
        if (value == null) {
            return true;
        }

        LocalDate now = LocalDate.now();
        LocalDate oneYearAgo = now.minusYears(1);
        LocalDate oneWeekFromNow = now.plusWeeks(1);

        boolean isPastWithinOneYear = value.isAfter(oneYearAgo) && !value.isAfter(now);
        boolean isFutureWithinOneWeek = value.isAfter(now) && value.isBefore(oneWeekFromNow);

        return isPastWithinOneYear || isFutureWithinOneWeek;
    }
}
