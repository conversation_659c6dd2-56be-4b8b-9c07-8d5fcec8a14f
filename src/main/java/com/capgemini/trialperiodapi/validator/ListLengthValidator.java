package com.capgemini.trialperiodapi.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Collection;

public class ListLengthValidator implements ConstraintValidator<ListLength, Collection<?>> {
    private int min;

    @Override
    public void initialize(ListLength constraintAnnotation) {
        this.min = constraintAnnotation.min();
    }

    @Override
    public boolean isValid(Collection<?> collection, ConstraintValidatorContext context) {
        return collection != null && collection.size() >= min;
    }
}