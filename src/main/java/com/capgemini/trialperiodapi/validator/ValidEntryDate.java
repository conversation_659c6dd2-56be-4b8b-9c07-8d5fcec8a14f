package com.capgemini.trialperiodapi.validator;

import com.capgemini.trialperiodapi.validator.DateRangeValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = DateRangeValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEntryDate {

    String message() default "La date doit être dans l'année écoulée ou dans les 7 jours à venir.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
