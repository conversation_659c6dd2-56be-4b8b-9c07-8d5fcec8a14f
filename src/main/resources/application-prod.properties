# Profiles
spring.profiles.active=dev

# Server port
server.port=8081

# Application
spring.application.name=Trial Period
app.name=Trial Period

# PostgreSQL datasource configuration
spring.datasource.url=*********************************************
spring.datasource.username=${DATASOURCE_USERNAME:postgres}
spring.datasource.password=${DATASOURCE_PASSWORD:admin}
spring.sql.init.encoding=UTF-8

# Hibernate configuration
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Mailing
app.email-server-host=MTACOR1.capgemini.com
app.email-server-protocol=smtp
app.email-server-port=587

# i18n
spring.messages.basename=lang/messages

# ADMIN ACCOUNT
app.admin-username=username
app.admin-email=<EMAIL>

# Security
app.allowed-origins=http://localhost:4200
app.access-token-expiration=********
app.refresh-token-expiration=*********
app.reset-password-token-expiration=1440000

## Liquibase
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml

# Log formatted SQL for better readability (optional)
spring.jpa.properties.hibernate.format_sql=true

# Log SQL parameters to see the actual values used in the query
logging.level.org.hibernate.type.descriptor.sql=TRACE

# Jasypt
jasypt.encryptor.password=${JASYPT_PASSWORD:jasypt}

# Security
app.jwt-secret-key=ENC(bUi4yULI8pqejjBgpiuoLkYz2Su2fNOXmd/VBoC3eK2Uo0kIRasiydlEhvkZS7VHcjdUBqYH+njzGVnlmDxRm+k8g4nBRKKeDdEYE97hD+k+Dp0w7P44Bcth+XLmd1Nf+Yau9fFKxy84bz0hYzbovg==)

# Mailing
spring.mail.password=ENC(lcn+iLBf0ke2htwx7TlYZ+iaiNQJ9aExjVpfYJWvmQJm/yaMRy/QZaQiZ0pZWRFnH5Cw6p8gKg4o5o6uLTB0sQ==)
# Client
app.frontend-reset-password-url=https://trial-period.capgemini.com/reset-password
app.frontend-activation-url=https://trial-period.capgemini.com/verify-email
app.frontend-login-url=https://trial-period.capgemini.com/login

# Jasypt
jasypt.encryptor.algorithm=PBEWithHMACSHA512AndAES_256

# SSL
server.ssl.enabled=true
server.ssl.key-store-type=PKCS12
server.ssl.key-store=classpath:Cert.p12

# Endpoints control
app.registration-allowed=false

# SSL
server.ssl.key-store-password=ENC(7XJhviWGy1UkGRO+kPQ815t842EDIr92Q2MK0RJCS3opVGi7TXWYFEMCSWqS6DLW)