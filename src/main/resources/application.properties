# Profiles
spring.profiles.active=dev

# Externals
spring.config.import=optional:secrets.properties,optional:management.properties

# Server port
server.port=8081

# Application
spring.application.name=Trial Period
app.name=Trial Period

# PostgreSQL datasource configuration
spring.datasource.url=*********************************************
spring.datasource.username=${DATASOURCE_USERNAME:postgres}
spring.datasource.password=${DATASOURCE_PASSWORD:admin}
spring.sql.init.encoding=UTF-8

# Hibernate configuration
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Mailing
app.email-server-host=MTACOR1.capgemini.com
app.email-server-protocol=smtp
app.email-server-port=587

# i18n
spring.messages.basename=lang/messages

# ADMIN ACCOUNT
app.admin-username=username
app.admin-email=<EMAIL>

# Security
app.allowed-origins=http://localhost:4200
app.access-token-expiration=********
app.refresh-token-expiration=*********
app.reset-password-token-expiration=1440000
app.jwt-secret-key=ENC(bUi4yULI8pqejjBgpiuoLkYz2Su2fNOXmd/VBoC3eK2Uo0kIRasiydlEhvkZS7VHcjdUBqYH+njzGVnlmDxRm+k8g4nBRKKeDdEYE97hD+k+Dp0w7P44Bcth+XLmd1Nf+Yau9fFKxy84bz0hYzbovg==)

# Client
app.frontend-reset-password-url=http://localhost:4200/reset-password
app.frontend-activation-url=http://localhost:4200/verify-email
app.frontend-login-url=http://localhost:4200/login

# Jasypt
jasypt.encryptor.algorithm=PBEWithHMACSHA512AndAES_256
jasypt.encryptor.password=${JASYPT_PASSWORD:jasypt}

# Endpoints control
app.registration-allowed=false

# Log formatted SQL for better readability (optional)
spring.jpa.properties.hibernate.format_sql=true

# Log SQL parameters to see the actual values used in the query
logging.level.org.hibernate.type.descriptor.sql=TRACE

# Liquibase
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml

# Logs
#logging.level.org.springframework.web=INFO
#logging.level.org.hibernate=ERROR
#logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=OFF