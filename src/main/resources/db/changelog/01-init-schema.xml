<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="abounass (generated)" id="1737996324030-1">
        <sql>
            CREATE EXTENSION IF NOT EXISTS pgcrypto;
        </sql>
        <createTable tableName="collaborators">
            <column name="entry_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="interviewed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="tenure_date" type="date"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="collaborators_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="people_unit_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="project_id" type="BIGINT"/>
            <column name="assignment_status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="BYTEA"/>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="ggid" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="global_grade" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="local_grade" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phone_number" type="BYTEA"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-2">
        <createTable tableName="macro_people_units">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="macro_people_units_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="name" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-3">
        <createTable tableName="password_reset_tokens">
            <column name="expiration_time" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="password_reset_tokens_pkey"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="token" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-4">
        <createTable tableName="people_units">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="people_units_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="macro_people_unit_id" type="BIGINT"/>
            <column name="name" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-5">
        <createTable tableName="projects">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="projects_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="name" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-6">
        <createTable tableName="trial_periods">
            <column name="collaborator_email_sent" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="first_trial_period_end_date" type="date"/>
            <column name="first_trial_period_notification_date" type="date"/>
            <column name="hidden" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="scorecard_sent" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="second_trial_period_end_date" type="date"/>
            <column name="second_trial_period_notification_date" type="date"/>
            <column name="collaborator_id" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="first_trial_period_feedback_request_sent_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="first_trial_period_last_reminder_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="trial_periods_pkey"/>
            </column>
            <column name="last_computed_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="second_trial_period_feedback_request_sent_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="second_trial_period_last_reminder_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="comment" type="BYTEA"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-7">
        <createTable tableName="users">
            <column name="is_enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column autoIncrement="true" name="id" startWith="2" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_pkey"/>
            </column>
            <column name="last_password_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="role" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="password" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-8">
        <createTable tableName="verification_tokens">
            <column name="expired" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="revoked" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="expiration_time" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="verification_tokens_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_id" type="BIGINT"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="token" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-9">
        <createTable tableName="people_unit_managers">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="people_unit_managers_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-10">
        <createTable tableName="project_managers">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="project_managers_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-11">
        <createTable tableName="resource_managers">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="resource_managers_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-12">
        <addUniqueConstraint columnNames="ggid, deleted_on" constraintName="collaborators_ggid_deleted_on_key" tableName="collaborators"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-13">
        <addUniqueConstraint columnNames="name, deleted_on" constraintName="macro_people_units_name_deleted_on_key" tableName="macro_people_units"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-14">
        <addUniqueConstraint columnNames="token, code" constraintName="password_reset_tokens_token_code_key" tableName="password_reset_tokens"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-15">
        <addUniqueConstraint columnNames="user_id" constraintName="password_reset_tokens_user_id_key" tableName="password_reset_tokens"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-16">
        <addUniqueConstraint columnNames="name, deleted_on" constraintName="people_units_name_deleted_on_key" tableName="people_units"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-17">
        <addUniqueConstraint columnNames="name, deleted_on" constraintName="projects_name_deleted_on_key" tableName="projects"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-18">
        <addUniqueConstraint columnNames="collaborator_id" constraintName="trial_periods_collaborator_id_key" tableName="trial_periods"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-19">
        <addUniqueConstraint columnNames="email" constraintName="users_email_key" tableName="users"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-20">
        <addUniqueConstraint columnNames="username, deleted_on" constraintName="users_username_deleted_on_key" tableName="users"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-21">
        <addUniqueConstraint columnNames="token, code" constraintName="verification_tokens_token_code_key" tableName="verification_tokens"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-22">
        <addUniqueConstraint columnNames="email, deleted_on" constraintName="people_unit_managers_email_deleted_on_key" tableName="people_unit_managers"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-23">
        <addUniqueConstraint columnNames="email, deleted_on" constraintName="project_managers_email_deleted_on_key" tableName="project_managers"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-24">
        <addUniqueConstraint columnNames="email, deleted_on" constraintName="resource_managers_email_deleted_on_key" tableName="resource_managers"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-25">
        <createTable tableName="decision_maker">
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="decision_maker_pkey"/>
            </column>
            <column name="last_updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="firstname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="BYTEA">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-26">
        <createTable tableName="hibernate_sequences">
            <column name="next_val" type="BIGINT"/>
            <column name="sequence_name" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="hibernate_sequences_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-27">
        <createTable tableName="hrbps_macro_people_units">
            <column name="macro_people_unit_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="hrbps_macro_people_units_pkey"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="hrbps_macro_people_units_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-28">
        <createTable tableName="hrs_macro_people_units">
            <column name="hr_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="hrs_macro_people_units_pkey"/>
            </column>
            <column name="macro_people_unit_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="hrs_macro_people_units_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-29">
        <createTable tableName="project_project_manager">
            <column name="project_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="project_project_manager_pkey"/>
            </column>
            <column name="project_manager_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="project_project_manager_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-30">
        <createTable tableName="pums_macro_people_units">
            <column name="macro_people_unit_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pums_macro_people_units_pkey"/>
            </column>
            <column name="pum_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pums_macro_people_units_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-31">
        <createTable tableName="resource_managers_macro_people_units">
            <column name="macro_people_unit_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="resource_managers_macro_people_units_pkey"/>
            </column>
            <column name="resource_manager_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="resource_managers_macro_people_units_pkey"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-32">
        <addForeignKeyConstraint baseColumnNames="project_manager_id" baseTableName="project_project_manager" constraintName="fk459nfq6moete4jfrfl3jex0yb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="project_managers" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-33">
        <addForeignKeyConstraint baseColumnNames="macro_people_unit_id" baseTableName="hrs_macro_people_units" constraintName="fk47jadqf9uhtlie6exu38n48wa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="macro_people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-34">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="verification_tokens" constraintName="fk54y8mqsnq1rtyf581sfmrbp4f" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-35">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="collaborators" constraintName="fk7s91b5rsk225mesbtoqg74hh1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-36">
        <addForeignKeyConstraint baseColumnNames="collaborator_id" baseTableName="trial_periods" constraintName="fk7wrhltjpwoo8b29bky7lmipr9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="collaborators" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-37">
        <addForeignKeyConstraint baseColumnNames="people_unit_id" baseTableName="collaborators" constraintName="fk9chulpxspe671mlui5q24yiy1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-38">
        <addForeignKeyConstraint baseColumnNames="pum_id" baseTableName="pums_macro_people_units" constraintName="fkesgyxyyny4ou4l9lj2ij7ct34" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="people_unit_managers" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-39">
        <addForeignKeyConstraint baseColumnNames="macro_people_unit_id" baseTableName="pums_macro_people_units" constraintName="fkfvx6aaigipcm2jtlsy2g3j0fb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="macro_people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-40">
        <addForeignKeyConstraint baseColumnNames="macro_people_unit_id" baseTableName="resource_managers_macro_people_units" constraintName="fkit308ps4su116ip3vgwmvoisa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="macro_people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-41">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="password_reset_tokens" constraintName="fkk3ndxg5xp6v7wd4gjyusp15gq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-42">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="hrbps_macro_people_units" constraintName="fkl0l5r0kutyriarh8sfp6akvv3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-43">
        <addForeignKeyConstraint baseColumnNames="macro_people_unit_id" baseTableName="hrbps_macro_people_units" constraintName="fkmjvd0teno88gxyrk8q8oqdd0y" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="macro_people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-44">
        <addForeignKeyConstraint baseColumnNames="resource_manager_id" baseTableName="resource_managers_macro_people_units" constraintName="fkmk02qwql9n290mit0bmhbj1vm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="resource_managers" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-45">
        <addForeignKeyConstraint baseColumnNames="hr_id" baseTableName="hrs_macro_people_units" constraintName="fkmo8r9vmeasnnrtoxg94bxbn4s" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-46">
        <addForeignKeyConstraint baseColumnNames="macro_people_unit_id" baseTableName="people_units" constraintName="fkqvvaxam014vw7gb6wj058oonl" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="macro_people_units" validate="true"/>
    </changeSet>
    <changeSet author="abounass (generated)" id="1737996324030-47">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="project_project_manager" constraintName="fktvg7fo9vmiqqihvvu6hloqjt" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="projects" validate="true"/>
    </changeSet>
</databaseChangeLog>