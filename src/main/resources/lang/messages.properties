# mail subject messages
mail.subject.welcome=Bienvenue
mail.subject.verify-email=VÃ©rification d'e-mail
mail.subject.reset-password=RÃ©initialisation du mot de passe
mail.subject.delete-hr-account=Suppression de compte
mail.subject.feedback-request=Demande de feedback


# collaborator resource messages
collaborator.updated=Collaborateur mis Ã  jour avec succÃ¨s
collaborator.created=Collaborateur ajoutÃ© avec succÃ¨s
collaborator.deleted=Collaborateur supprimÃ© avec succÃ¨s
collaborator.imported={0} collaborateurs importÃ©s avec succÃ¨s
collaborator.by-id.not-found=Collaborateur avec l''identifiant {0} non trouvÃ©
collaborator.by-ggid.not-found=Collaborateur par GGID {0} non trouvÃ©
collaborator.ggid-already-exists=Le GGID {0} est dÃ©jÃ  utilisÃ©
collaborator.email-already-exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
collaborator.entry-date.empty=Date d''entrÃ©e pour le collaborateur "{0}" non spÃ©cifiÃ©e
collaborator.is-opp-0=Le collaborateur "{0}" est en intercontrat, il ne peut Ãªtre affectÃ© Ã  aucun projet


# hrbp resource messages
hrbp.created=HRBP ajoutÃ© avec succÃ¨s
hrbp.updated=HRBP mis Ã  jour avec succÃ¨s
hrbp.deleted=HRBP supprimÃ© avec succÃ¨s
hrbp.by-id.not-found=HRBP avec l''identifiant {0} non trouvÃ©


# admin resource messages
admin.created=Admin ajoutÃ© avec succÃ¨s
admin.updated=Admin mis Ã  jour avec succÃ¨s
admin.deleted=Admin supprimÃ© avec succÃ¨s
admin.by-id.not-found=Admin avec l''identifiant {0} non trouvÃ©


# user resource messages
user.profile-updated=Profil mis Ã  jour avec succÃ¨s
user.email-verified=Votre compte a Ã©tÃ© vÃ©rifiÃ© avec succÃ¨s. Vous pouvez maintenant vous connecter Ã  votre compte !
user.email-already-verified=Votre compte a dÃ©jÃ  Ã©tÃ© vÃ©rifiÃ©, vous pouvez vous connecter !
user.by-id.not-found=Utilisateur avec l''identifiant {0} non trouvÃ©
user.by-email.not-found=Utilisateur par email "{0}" non trouvÃ©
user.by-username.not-found=Utilisateur avec le nom d''utilisateur "{0}" non trouvÃ©
user.by-username.already-exists=Le nom d''utilisateur "{0}" est dÃ©jÃ  utilisÃ©
user.by-email.already-exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
user.username-already-exists=Le nom d''utilisateur "{0}" est dÃ©jÃ  utilisÃ©
user.email-already-exists=L''e-mail "{0}" est dÃ©jÃ  utilisÃ©


# people unit resource messages
practice.empty=La practice est vide
people-unit.imported={0} people units importÃ©es avec succÃ¨s
people-unit.by-name.not-found=People unit avec le nom "{0}" non trouvÃ©e
people-unit.by-name.already-exists=Le nom de PU "{0}" est dÃ©jÃ  utilisÃ©
people-unit.by-id.not-found=People unit avec l''identifiant {0} non trouvÃ©e
people-unit.created=People unit ajoutÃ©e avec succÃ¨s
people-unit.updated=People unit modifiÃ©e avec succÃ¨s
people-unit.deleted=People unit supprimÃ©e avec succÃ¨s
people-unit.empty-name=Le nom de la people unit est requise
people-unit.already-assigned-to-a-macro-pu=La PU "{0}" est dÃ©jÃ  affÃ©ctÃ©e Ã  la macro PU "{1}". Veuillez dÃ©tacher la PU avant de pouvoir continuer
people-unit.cannot-delete-pu-belongs-to-collaborator=La PU "{0}" sera supprimÃ©e. Veuillez dÃ©stiner une autre PU pour le collaborateur "{1}" avant de pouvoir continuer


# project manager resource messages
project-manager.by-email.not-found=Chef de projet par email "{0}" non trouvÃ©
project-manager.by-id.not-found=Chef de projet avec l''identifiant {0} non trouvÃ©
project-manager.by-email.exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
project-manager.created=Chef de projet ajoutÃ© avec succÃ¨s
project-manager.updated=Chef de projet modifiÃ© avec succÃ¨s
project-manager.deleted=Chef de projet supprimÃ© avec succÃ¨s
project-manager.cannot-delete-the-only-project-manager-for-project=Un projet ne peut pas avoir zÃ©ro EM. Veuillez dÃ©stiner un autre EM pour le projet "{0}" avant de pouvoir continuer


# hr resource messages
hr.by-email.exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
hr.created=ChargÃ© RH ajoutÃ© avec succÃ¨s
hr.updated=ChargÃ© RH modifiÃ© avec succÃ¨s
hr.deleted=ChargÃ© RH supprimÃ© avec succÃ¨s
hr.by-id.not-found=ChargÃ© RH avec l''identifiant {0} non trouvÃ©
hr.by-email.not-found=ChargÃ© RH par email "{0}" non trouvÃ©


# people unit manager resource messages
pum.by-email.not-found=PUM par email "{0}" non trouvÃ©
pum.by-id.not-found=PUM avec l''identifiant {0} non trouvÃ©
pum.created=PUM ajoutÃ© avec succÃ¨s
pum.deleted=PUM supprimÃ© avec succÃ¨s
pum.updated=PUM modifiÃ©e avec succÃ¨s
pum.by-email.already-exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
# people unit manager resource messages
resource-manager.by-email.not-found=Resource manager par email "{0}" non trouvÃ©
resource-manager.by-id.not-found=Resource manager avec l''identifiant {0} non trouvÃ©
resource-manager.by-email.exists=L''email "{0}" est dÃ©jÃ  utilisÃ©
resource-manager.created=Resource manager ajoutÃ© avec succÃ¨s
resource-manager.updated=Resource manager modifiÃ© avec succÃ¨s
resource-manager.deleted=Resource manager supprimÃ© avec succÃ¨s


# macro people unit resource messages
macro-people-unit.by-name.not-found=Macro people unit avec le nom "{0}" non trouvÃ©e
macro-people-unit.by-id.not-found=Macro People avec l''identifiant {0} non trouvÃ©e
macro-people-unit.by-name.already-exists=Le nom de macro PU "{0}" est dÃ©jÃ  utilisÃ©
macro-people-unit.created=Macro people unit ajoutÃ©e avec succÃ¨s
macro-people-unit.updated=Macro people unit modifiÃ©e avec succÃ¨s
macro-people-unit.deleted=Macro people unit supprimÃ©e avec succÃ¨s
macro-people-unit.cannot-delete-the-only-hrbp-for-macro-pu=Une macro PU ne peut pas avoir zÃ©ro HRBP. Veuillez dÃ©stiner un autre HRBP pour la macro PU "{0}" avant de pouvoir continuer
macro-people-unit.cannot-delete-the-only-hr-for-macro-pu=Une macro PU ne peut pas avoir zÃ©ro HR. Veuillez dÃ©stiner un autre HR pour la macro PU "{0}" avant de pouvoir continuer
macro-people-unit.cannot-delete-the-only-pum-for-macro-pu=Une macro PU ne peut pas avoir zÃ©ro PUM. Veuillez dÃ©stiner un autre PUM pour la macro PU "{0}" avant de pouvoir continuer
macro-people-unit.cannot-delete-the-only-people-unit-for-macro-pu=Une macro PU ne peut pas avoir zÃ©ro PU. Veuillez dÃ©stiner une autre PU pour la macro PU "{0}" avant de pouvoir continuer


# project resource messages
project.imported={0} projets importÃ©s avec succÃ¨s
project.by-name.not-found=Projet avec le nom "{0}" non trouvÃ©
project.by-name.exists=Le nom "{0}" est dÃ©jÃ  utilisÃ©
project.by-id.not-found=Projet avec l''identifiant {0} non trouvÃ©
project.has-no-decision-maker=Le projet n'est affectÃ© Ã  aucun dÃ©cisionnaire
project.created=Projet ajoutÃ© avec succÃ¨s
project.updated=Projet modifiÃ© avec succÃ¨s
project.deleted=Projet supprimÃ© avec succÃ¨s
project.incoherent-people-unit=Le project ne correspond pas Ã  la PU
project.cannot-delete-project-assigned-to-collaborator=Veuillez rÃ©affecter le collaborateur "{0}" avant de pouvoir supprimer le projet
project.cannot-delete-project-assigned-to-project-manager=Veuillez rÃ©affecter le chef de projet "{0}" avant de pouvoir supprimer le projet
project.name.empty=Le nom du projet est vide


# decision maker resource messages
decision-maker.by-name.not-found=DÃ©cisionnaire avec le nom "{0}" non trouvÃ©


# trial period resource messages
trial-period.feedback-request.sent=Demande de feedback envoyÃ©e avec succÃ¨s
trial-period.by-id.not-found=PÃ©riode d''essai avec l''identifiant {0} non trouvÃ©
trial-period.by-collaborator-id.not-found=PÃ©riode d''essai avec l''identifiant collaborateur {0} non trouvÃ©
trial-period.collaborator-already-tenured=Le collaborateur "{0}" est dÃ©jÃ  titularisÃ©
trial-period.scorecard-already-sent=La fiche d''Ã©valuation est dÃ©jÃ  reÃ§ue pour le collaborateur "{0}"
notification.hidden=Notification masquÃ©e avec succÃ¨s
notifications.hidden=Notifications masquÃ©es avec succÃ¨s


# password resource messages
password.updated=Mot de passe mis Ã  jour avec succÃ¨s
password.reset=Mot de passe rÃ©initialisÃ© avec succÃ¨s
password.verification-link.sent=Un e-mail de vÃ©rification vous a Ã©tÃ© envoyÃ© sur votre boite.
password.not-match=Les mots de passe saisis ne sont pas identiques
password.similar-update=Le nouveau mot de passe est similaire Ã  l'ancien
password.wrong=Mot de passe erronÃ©
password.forgot.required-email=L'e-mail ne pas pas Ãªtre vide


# verification token resource messages
verification-token.not-found=Le jeton de vÃ©rification est introuvable
verification-token.expired=Le jeton a dÃ©jÃ  expirÃ© !
verification-token.invalid=Le jeton est invalide !


# password token resource messages
password-token.not-found=Aucun jeton n'est associÃ© au code saisi
password-token.expired=Le jeton a dÃ©jÃ  expirÃ© !
password.expired=Le mot de passe a expirÃ©. Veuillez modifier votre mot de passe.
password.expire-soon=Votre mot de passe expirera dans {0} jours !


# Local grade resource messages
local-grade.incoherent=Le grade local ne correspond pas au grade global


# exception messages
exception.messaging=Une erreur s'est produite lors de l'envoi de l'e-mail. VÃ©rifiez votre connexion ou ressayez plus tard.
exception.refresh-token.failed=Une erreur s'est produite lors du rafraÃ®chissement du jeton
exception.token.invalid=Jeton invalide
exception.verification-code.blank=Le code de vÃ©rification ne pas pas Ãªtre vide
exception.file.not-found=Fichier introuvable
exception.file.csv-format=Le fichier doit Ãªtre au format CSV !
exception.file.upload-failed=Une erreur s'est produite lors de l'importation du fichier
exception.file.extract-email-failed=Une erreur s'est produite lors de l'extraction de l'e-mail
exception.file.extract-first-name-failed=Une erreur s'est produite lors de l'extraction du prÃ©nom
exception.file.extract-last-name-failed=Une erreur s'est produite lors de l'extraction du nom
exception.file.invalid-encoding=Le fichier n'est pas encodÃ© en UTF-8. Veuillez changer d'encodage vers UTF-8 !
exception.file.encoding-validation=Une erreur s'est produite lors de la validation d'encodage
exception.email-template.required=Le nom du template est obligatoire pour l'envoi de l'e-mail
exception.unauthorized-action=Action non autorisÃ©e
exception.invalid-boolean=La valeur "{0}" ne correspond pas Ã  un boolÃ©en


# validation messages
validation.username-required=Le nom d'utilisateur est requis
validation.password-required=Le mot de passe est requis
validation.current-password-required=Le mot de passe actuel est requis
validation.new-password-required=Un nouveau mot de passe est requis
validation.password-pattern=Le mot de passe doit comporter au moins 12 caractÃ¨res et contenir au moins une lettre majuscule, un chiffre et un caractÃ¨re spÃ©cial.
validation.confirm-password-required=La confirmation du mot de passe est requise
validation.passwords-not-match=Les mots de passe ne correspondent pas
validation.ggid-required=GGID est requis
validation.ggid-pattern=Le GGID doit Ãªtre composÃ© de 4 Ã  12 chiffres
validation.firstname-required=Le prÃ©nom est requis
validation.firstname-too-short=Le prÃ©nom est trop court
validation.firstname-too-long=Le prÃ©nom est trop long
validation.lastname-required=Le nom de famille est obligatoire
validation.lastname-too-short=Le nom de famille est trop court
validation.lastname-too-long=Le nom de famille est trop long
validation.email-pattern=Le format de l'email n'est pas valide
validation.email-too-long=L'email est trop long
validation.comment-too-long=L'email est trop long
validation.entry-date-required=La date d'entrÃ©e est requise
validation.global-grade-required=Le grade global est requis
validation.local-grade-required=Le grade local est requis
validation.collaborator-status-required=Le status du collaborateur est requis
validation.people-unit-required=La people unit est requise
validation.macro-people-unit-required=La macro PU est requise
validation.full-name-required=Le nom complet est requis
validation.full-name-too-short=Le nom complet est trop court
validation.full-name-too-long=Le nom complet est trop long
validation.username-too-short=Le nom d'utilisateur est trop court
validation.username-too-long=Le nom d'utilisateur est trop long
validation.email-required=L'adresse e-mail est requise
validation.people-unit-name-too-short=Le nom de la people unit est trop court
validation.people-unit-name-too-long=Le nom de la people unit est trop long
validation.macro-people-unit-name-required=La macro people unit est requise
validation.macro-people-unit-name-too-short=Le nom de la macro people unit est trop court
validation.macro-people-unit-name-too-long=Le nom de la macro people unit est trop long
validation.pu-list-is-empty=La liste des PU ne peut pas Ãªtre vide
validation.hr-list-is-empty=La liste des chargÃ©s RH ne peut pas Ãªtre vide
validation.hrbp-list-is-empty=La liste des HRBP ne peut pas Ãªtre vide
validation.project-manager-list-is-empty=La liste des EM ne peut pas Ãªtre vide
validation.people-unit-manager-list-is-empty=La liste des PUM ne peut pas Ãªtre vide
validation.macro-people-unit-list-is-empty=La liste des macro PU ne peut pas Ãªtre vide
validation.resource-manager-list-is-empty=La liste des resource managers ne peut pas Ãªtre vide
validation.project-name-required=Le nom du projet est requis
validation.project-name-too-short=Le nom du projet est trop court
validation.project-name-too-long=Le nom du projet est trop long
validation.name-no-slash=Le nom ne peut pas contenir de slash

# keywords
string.message=message