# Port dÃ©diÃ© aux endpoints Actuator (diffÃ©rent du port principal de l'application)
management.server.port=8088

# Activer les endpoints Actuator
management.endpoints.enabled-by-default=true
management.endpoints.web.exposure.include=health,metrics,loggers,env,info,threaddump,httptrace,beans,mappings,heapdump

# DÃ©sactiver des endpoints sensibles
management.endpoint.shutdown.enabled=false

# Configuration des logs via loggers
management.endpoint.loggers.enabled=true
management.endpoint.loggers.cache.time-to-live=10s

# Exposer les dÃ©tails des mÃ©triques et traces HTTP
management.trace.http.enabled=true
management.endpoint.httptrace.enabled=true

# Personnalisation des mÃ©triques et export vers Prometheus
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=trial-period-api

# DÃ©tails du health check seulement pour les utilisateurs autorisÃ©s
management.endpoint.health.show-details=when_authorized
management.endpoint.health.roles=ADMIN

# Exposition des informations systÃ¨me
management.endpoint.env.show-values=when_authorized

# ParamÃ¨tres pour activer les dumps de threads et de heap
management.endpoint.threaddump.enabled=true
management.endpoint.heapdump.enabled=true