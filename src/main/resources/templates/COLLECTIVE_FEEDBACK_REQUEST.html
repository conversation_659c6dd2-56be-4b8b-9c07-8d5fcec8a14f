<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="fr">
<head>
    <title>Fin de période d'essai</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid black;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #dbeafe;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        b {
            display: block;
            margin-bottom: 8px;
        }

        .deadline-th {
            background-color: rgba(255, 0, 0, 0.75);
            color: white;
        }

        .deadline-td {
            background-color: rgba(255, 0, 0, 0.1);
        }

        .intercontrat {
            font-style: italic;
            color: gray;
        }

        .highlight {
            background-color: yellow;
            font-weight: bold;
        }

        .container {
            margin: 10px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<div class="container">

    <h1>Période d'essai - De<PERSON>e de Feedback</h1>

    <p>Bonjour <span th:text="${username}"></span>,</p>

    <p>
        Nous arrivons au terme des périodes d'essai des collaborateurs suivants :
    </p>

    <table>
        <thead>
        <tr>
            <th>PU</th>
            <th>Nom</th>
            <th>Prénom</th>
            <th>Grade</th>
            <th>Projet</th>
            <th>Date d'entrée</th>
            <th>Fin PE</th>
            <th class="deadline-th">Deadline</th>
        </tr>
        </thead>
        <tbody>
        <th:block th:each="project : ${projects}">
            <tr th:each="collaborator : ${project.collaborators}">
                <td th:text="${collaborator.peopleUnit.name}"></td>
                <td th:text="${collaborator.lastname}"></td>
                <td th:text="${collaborator.firstname}"></td>
                <td th:text="${collaborator.localGrade}"></td>
                <td th:text="${collaborator.project.name}"
                    th:if="${collaborator.project != null}"></td>
                <td th:text="Intercontrat"
                    th:if="${collaborator.project == null}"
                    class="intercontrat"></td>
                <td th:text="${collaborator.entryDate}"></td>
                <td th:text="${collaborator.trialPeriod.status ==
        T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        collaborator.trialPeriod.firstTrialPeriodEndDate
         : collaborator.trialPeriod.secondTrialPeriodEndDate}"></td>
                <td th:text="${collaborator.trialPeriod.status ==
        T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        collaborator.trialPeriod.firstTrialPeriodNotificationDate
         : collaborator.trialPeriod.secondTrialPeriodNotificationDate}"
                    class="deadline-td"></td>
            </tr>
        </th:block>
        </tbody>
    </table>

    <p>Pourrais-tu stp compléter l'EPE opérationnel et nous faire un retour
        <strong class="highlight">avant les deadlines mentionnées</strong>.</p>

    <p>Cordialement,</p>
</div>
</body>
</html>