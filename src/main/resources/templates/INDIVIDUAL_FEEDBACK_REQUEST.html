<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="fr">
<head>
    <title>Fin de période d'essai</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid black;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #dbeafe;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        b {
            display: block;
            margin-bottom: 8px;
        }

        .highlight {
            background-color: yellow;
            font-weight: bold;
        }

        .deadline-th {
            background-color: rgba(255, 0, 0, 0.75);
            color: white;
        }

        .deadline-td {
            background-color: rgba(255, 0, 0, 0.1);
        }

        .intercontrat {
            font-style: italic;
            color: gray;
        }

        .container {
            margin: 10px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<div class="container">

    <h1>Période d'essai - Demande de Feedback</h1>

    <p>Bonjour <span th:text="${username}"></span>,</p>

    <p>
        Nous arrivons au terme de la
        <span class="highlight" th:text="${projects[0].collaborators[0].trialPeriod.status ==
         T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        '1ère' : '2ème'}">
        </span>
        période d'essai de
        <span class="highlight" th:text="${projects[0].collaborators[0].firstname + ' '
    + projects[0].collaborators[0].lastname}"></span>.
    </p>

    <table>
        <thead>
        <tr>
            <th>PU</th>
            <th>Nom</th>
            <th>Prénom</th>
            <th>Grade</th>
            <th>Projet</th>
            <th>Date d'entrée</th>
            <th>Fin PE</th>
            <th class="deadline-th">Deadline</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td th:text="${projects[0].collaborators[0].peopleUnit.name}"></td>
            <td th:text="${projects[0].collaborators[0].lastname}"></td>
            <td th:text="${projects[0].collaborators[0].firstname}"></td>
            <td th:text="${projects[0].collaborators[0].localGrade}"></td>
            <td th:text="${projects[0].collaborators[0].project.name}"
                th:if="${projects[0].collaborators[0].project != null}"></td>
            <td th:text="Intercontrat"
                th:if="${projects[0].collaborators[0].project == null}"
                class="intercontrat"></td>
            <td th:text="${projects[0].collaborators[0].entryDate}"></td>
            <td th:text="${projects[0].collaborators[0].trialPeriod.status ==
        T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        projects[0].collaborators[0].trialPeriod.firstTrialPeriodEndDate :
        projects[0].collaborators[0].trialPeriod.secondTrialPeriodEndDate}"></td>
            <td th:text="${projects[0].collaborators[0].trialPeriod.status ==
        T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        projects[0].collaborators[0].trialPeriod.firstTrialPeriodNotificationDate :
        projects[0].collaborators[0].trialPeriod.secondTrialPeriodNotificationDate}"
                class="deadline-td"></td>
        </tr>
        </tbody>
    </table>

    <p>Pourrais-tu stp compléter l'EPE opérationnel et nous faire un retour
        <strong class="highlight">avant la deadline mentionnée</strong>.</p>

    <p>Cordialement,</p>

</div>

</body>
</html>