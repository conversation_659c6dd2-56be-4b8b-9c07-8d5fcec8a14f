<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="fr">
<head>
    <title>Fin de période d'essai</title>
    <style>
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 600px;
            margin: 10px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<div class="container">

    <!-- <img src="../static/images/favicon-192x192.png" alt="Trial Period"> -->

    <h1>Période d'essai - Demande de Feedback</h1>
    <p>Bonjour <span th:text="${username}"></span>,</p>

    <p>
        Je reviens vers toi au sujet de la période d’essai de
        <span class="highlight" th:text="${projects[0].collaborators[0].firstname + ' '
    + projects[0].collaborators[0].lastname}"></span> qui prend fin le
        <b><span class="highlight" th:text="${projects[0].collaborators[0].trialPeriod.status ==
        T(com.capgemini.trialperiodapi.model.TrialPeriodStatus).IN_PROGRESS ?
        projects[0].collaborators[0].trialPeriod.firstTrialPeriodEndDate :
        projects[0].collaborators[0].trialPeriod.secondTrialPeriodEndDate}"></span></b>
    </p>

    <p>
        Peux-tu stp partager avec nous <strong>dans la journée</strong> la fiche d’évaluation avec la décision ?
    </p>

    <p>En te remerciant,</p>

    <p>Cordialement,</p>
</div>
</body>
</html>