package com.capgemini.trialperiodapi;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test simple pour vérifier que JUnit fonctionne
 */
class SimpleTest {

    @Test
    void simpleTest() {
        // Test basique pour vérifier que l'environnement de test fonctionne
        assertEquals(2, 1 + 1);
        assertTrue(true);
        assertNotNull("test");
    }

    @Test
    void applicationClassExists() {
        // Vérifier que la classe principale existe
        assertDoesNotThrow(() -> {
            Class.forName("com.capgemini.trialperiodapi.TrialPeriodApiApplication");
        });
    }
}
