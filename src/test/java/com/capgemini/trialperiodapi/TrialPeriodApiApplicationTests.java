package com.capgemini.trialperiodapi;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    // Base de données H2 en mémoire
    "spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.datasource.username=sa",
    "spring.datasource.password=",
    "spring.jpa.database-platform=org.hibernate.dialect.H2Dialect",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.jpa.show-sql=false",

    // Désactiver Liquibase
    "spring.liquibase.enabled=false",

    // Configuration application
    "app.jwt-secret-key=test-secret-key-for-testing-minimum-32-characters-long",
    "app.admin-username=testadmin",
    "app.admin-email=<EMAIL>",
    "app.access-token-expiration=86400000",
    "app.refresh-token-expiration=604800000",
    "app.reset-password-token-expiration=1440000",
    "app.frontend-activation-url=http://localhost:4200/verify-email",
    "app.frontend-reset-password-url=http://localhost:4200/reset-password",
    "app.frontend-login-url=http://localhost:4200/login",
    "app.allowed-origins=http://localhost:4200",
    "app.registration-allowed=true",

    // Jasypt
    "jasypt.encryptor.password=test",
    "jasypt.encryptor.algorithm=PBEWithMD5AndDES",

    // Email (désactivé pour les tests)
    "spring.mail.host=localhost",
    "spring.mail.port=25",
    "app.email-server-host=localhost",
    "app.email-server-protocol=smtp",
    "app.email-server-port=25"
})
class TrialPeriodApiApplicationTests {

	@Test
	void contextLoads() {
		// Test simple pour vérifier que le contexte Spring se charge
	}

}
