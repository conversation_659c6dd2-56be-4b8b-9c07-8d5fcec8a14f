package com.capgemini.trialperiodapi;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.datasource.username=sa",
    "spring.datasource.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.liquibase.enabled=false",
    "app.jwt-secret-key=test-secret-key-for-testing",
    "app.admin-username=testadmin",
    "app.admin-email=<EMAIL>",
    "jasypt.encryptor.password=test"
})
class TrialPeriodApiApplicationTests {

	@Test
	void contextLoads() {
		// Test simple pour vérifier que le contexte Spring se charge
	}

}
