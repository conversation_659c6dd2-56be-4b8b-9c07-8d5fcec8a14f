package com.capgemini.trialperiodapi.config;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.service.auth.JwtService;
import com.capgemini.trialperiodapi.service.auth.VerificationTokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.io.IOException;
import java.util.Calendar;
import java.util.UUID;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JwtAuthenticationFilterTest {
    @Mock
    private JwtService jwtService;

    @Mock
    private UserDetailsService userDetailsService;

    @Mock
    private VerificationTokenService tokenService;

    @InjectMocks
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Test
    void doFilterInternal_ValidToken_SuccessfulAuthentication() throws ServletException, IOException {

        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        String jwt = "validToken";
        String userEmail = "<EMAIL>";

        when(request.getHeader("Authorization")).thenReturn("Bearer " + jwt);
        when(jwtService.extractUsername(jwt)).thenReturn(userEmail);

        UserDetails userDetails = mock(UserDetails.class);
        when(userDetailsService.loadUserByUsername(userEmail)).thenReturn(userDetails);

        when(jwtService.isTokenValid(jwt, userDetails)).thenReturn(true);
        when(tokenService.getVerificationTokenByToken(jwt)).thenReturn(createValidVerificationToken());

        jwtAuthenticationFilter.doFilterInternal(request, response, filterChain);

        Authentication authentication = new UsernamePasswordAuthenticationToken(
                userDetails, null, userDetails.getAuthorities());

        SecurityContextHolder.getContext().setAuthentication(authentication);

        verify(filterChain).doFilter(request, response);
    }

    private VerificationToken createValidVerificationToken() {
        AppUser user = new AppUser();

        String token = UUID.randomUUID().toString();

        Calendar expirationTime = Calendar.getInstance();
        expirationTime.add(Calendar.HOUR, 1);

        String code = UUID.randomUUID().toString();

        VerificationToken verificationToken = new VerificationToken(user, token, code);
        verificationToken.setExpirationTime(expirationTime.getTime());

        verificationToken.setRevoked(false);
        verificationToken.setExpired(false);

        return verificationToken;
    }

}