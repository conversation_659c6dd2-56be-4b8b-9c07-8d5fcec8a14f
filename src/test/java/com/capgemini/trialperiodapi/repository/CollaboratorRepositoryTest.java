package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.*;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.LocalDate;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class CollaboratorRepositoryTest {

    @Autowired
    CollaboratorRepository collaboratorRepository;

    @Autowired
    PeopleUnitRepository peopleUnitRepository;

    @Test
    void CollaboratorRepositoryTest_findCollaboratorById_returnExpectedCollaborator() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("People Unit Name")
                .build();
        peopleUnitRepository.save(peopleUnit);

        Long collaboratorId = 1L;
        Collaborator collaborator = Collaborator.builder()
                .id(collaboratorId)
                .ggid("ggid")
                .firstname("junit")
                .lastname("testing")
                .email("<EMAIL>")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .peopleUnit(peopleUnit)
                .build();

        collaboratorRepository.save(collaborator);

        // When
        Optional<Collaborator> optionalCollaborator =
                collaboratorRepository.findCollaboratorById(collaboratorId);

        // Then
        assertNotNull(optionalCollaborator);
        assertEquals(collaboratorId, optionalCollaborator.get().getId());
    }

    @Test
    void CollaboratorRepositoryTest_findCollaboratorById_returnEmptyObject() {
        // Given
        Long nonExistingCollaboratorId = 1L;

        // When
        Optional<Collaborator> result =
                collaboratorRepository.findCollaboratorById(nonExistingCollaboratorId);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void CollaboratorRepositoryTest_findCollaboratorByEmail_returnExpectedCollaborator() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("People Unit Name")
                .build();
        peopleUnitRepository.save(peopleUnit);

        String collaboratorEmail = "<EMAIL>";
        String sameCollaboratorEmail = "<EMAIL>";

        Collaborator collaborator = Collaborator.builder()
                .ggid("ggid")
                .firstname("junit")
                .lastname("testing")
                .email(collaboratorEmail)
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .peopleUnit(peopleUnit)
                .build();

        collaboratorRepository.save(collaborator);

        // When
        Optional<Collaborator> optionalCollaborator =
                collaboratorRepository.findCollaboratorByEmail(sameCollaboratorEmail);

        // Then
        assertNotNull(optionalCollaborator);
        assertEquals(collaboratorEmail, optionalCollaborator.get().getEmail());
    }

    @Test
    void CollaboratorRepositoryTest_findCollaboratorByEmail_returnEmptyObject() {
        // Given
        String nonExistingEmail = "<EMAIL>";

        // When
        Optional<Collaborator> result =
                collaboratorRepository.findCollaboratorByEmail(nonExistingEmail);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void CollaboratorRepositoryTest_findCollaboratorByGgid_returnExpectedCollaborator() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("People Unit Name")
                .build();
        peopleUnitRepository.save(peopleUnit);

        String collaboratorGgid = "123456";
        Collaborator collaborator = Collaborator.builder()
                .ggid(collaboratorGgid)
                .firstname("junit")
                .lastname("testing")
                .email("<EMAIL>")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .peopleUnit(peopleUnit)
                .build();

        collaboratorRepository.save(collaborator);

        // When
        Optional<Collaborator> optionalCollaborator =
                collaboratorRepository.findCollaboratorByGgid(collaboratorGgid);

        // Then
        assertNotNull(optionalCollaborator);
        assertEquals(collaboratorGgid, optionalCollaborator.get().getGgid());
    }

    @Test
    void CollaboratorRepositoryTest_findCollaboratorByGgid_returnEmptyObject() {
        // Given
        String nonExistingGgid = "123456";

        // When
        Optional<Collaborator> result =
                collaboratorRepository.findCollaboratorByGgid(nonExistingGgid);

        // Then
        assertTrue(result.isEmpty());
    }
}