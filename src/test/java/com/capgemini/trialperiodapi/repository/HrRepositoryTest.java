package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class HrRepositoryTest {

    @Autowired
    private HrRepository hrRepository;
    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void HrRepository_findHrByEmail_returnHr() {
        // Given
        String email = "<EMAIL>";

        Hr hr = Hr.builder()
                .username("username")
                .password("Pass0000")
                .firstname("firstname")
                .lastname("lastname")
                .email(email)
                .build();

        hrRepository.save(hr);

        // When
        Optional<Hr> resultedHr = hrRepository.findHrByEmail(email);

        // Then
        assertNotNull(resultedHr);
        assertEquals(email, resultedHr.get().getEmail());
    }

    @Test
    void HrRepository_findHrByEmail_returnEmptyObject() {
        // Given
        String nonExistingEmail = "<EMAIL>";

        // When
        Optional<Hr> result = hrRepository.findHrByEmail(nonExistingEmail);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void HrRepository_findAllHrsByMacroPeopleUnitName_returnsListOfHrs() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        Hr hr1 = Hr.builder()
                .username("username1")
                .password("Pass0000")
                .firstname("hr 11")
                .lastname("hr 12")
                .email("<EMAIL>")
                .macroPeopleUnits(Set.of(macroPeopleUnit))
                .build();

        Hr hr2 = Hr.builder()
                .username("username2")
                .password("Pass0000")
                .firstname("hr 21")
                .lastname("hr 22")
                .email("<EMAIL>")
                .macroPeopleUnits(Set.of(macroPeopleUnit))
                .build();

        hrRepository.saveAll(List.of(hr1, hr2));
        macroPeopleUnit.setHrs(Set.of(hr1, hr2));
        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<Hr> resultedList = hrRepository.findAllHrsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertNotNull(resultedList);
        assertEquals(2, resultedList.size());
        resultedList.forEach(hr -> assertEquals(macroPeopleUnitName,
                hr.getMacroPeopleUnits().stream().toList().get(0).getName()));
    }

    @Test
    void HrRepository_findAllHrsByMacroPeopleUnitName_returnsEmptyList() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<Hr> resultedList = hrRepository.findAllHrsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(0, resultedList.size());
    }

    @Test
    void HrRepository_findHrsByKeyword_returnsListOfHrs() {
        // Given
        String keyword = "test";

        Hr hr1 = Hr.builder()
                .username("username1")
                .password("Pass0000")
                .firstname("hr test1")
                .lastname("hr test2")
                .email("<EMAIL>")
                .build();

        Hr hr2 = Hr.builder()
                .username("username2")
                .password("Pass0000")
                .firstname("TEst1")
                .lastname("TEst2")
                .email("<EMAIL>")
                .build();

        Hr hr3 = Hr.builder()
                .username("username3")
                .password("Pass0000")
                .firstname("hr 31")
                .lastname("hr 32")
                .email("<EMAIL>")
                .build();

        Hr hr4 = Hr.builder()
                .username("username4")
                .password("Pass0000")
                .firstname("hr 41")
                .lastname("hr 42")
                .email("<EMAIL>")
                .build();

        hrRepository.saveAll(List.of(hr1, hr2, hr3, hr4));

        // When
        List<Hr> actualList = hrRepository.findHrsByKeyword(keyword);

        // Then
        assertNotNull(actualList);
        assertEquals(3, actualList.size());
    }

    @Test
    void HrRepository_findHrsByKeyword_returnsEmptyList() {
        // Given
        String keyword = "keyword";

        // When
        List<Hr> result = hrRepository.findHrsByKeyword(keyword);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void HrRepository_findAllHrsByKeyword_returnsListOfHrs() {
        // Given
        String keyword = "test";

        Hr hr1 = Hr.builder()
                .username("username1")
                .password("Pass0000")
                .firstname("hr test1")
                .lastname("hr test2")
                .email("<EMAIL>")
                .build();

        Hr hr2 = Hr.builder()
                .username("username2")
                .password("Pass0000")
                .firstname("TEst1")
                .lastname("TEst2")
                .email("<EMAIL>")
                .build();

        Hr hr3 = Hr.builder()
                .username("username3")
                .password("Pass0000")
                .firstname("hr 31")
                .lastname("hr 32")
                .email("<EMAIL>")
                .build();

        hrRepository.saveAll(List.of(hr1, hr2, hr3));
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<Hr> resultedList = hrRepository.findAllHrsByKeyword(keyword, pageable);

        // Then
        assertNotNull(resultedList);
        assertEquals(3, resultedList.getTotalElements());
    }

    @Test
    void HrRepository_findAllHrsByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<Hr> resultedList = hrRepository.findAllHrsByKeyword(keyword, pageable);

        // Then
        assertEquals(0, resultedList.getContent().size());
    }
}
