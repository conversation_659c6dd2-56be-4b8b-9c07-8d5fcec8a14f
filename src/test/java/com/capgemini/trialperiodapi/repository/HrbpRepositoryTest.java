package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.Role;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class HrbpRepositoryTest {

    @Autowired
    private HrbpRepository hrbpRepository;

    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void UserRepository_findAllHrbpsByMacroPeopleUnitName_returnListOfHrbps() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);


        Hrbp hrbp1 = Hrbp.builder()
                .firstname("hrbp 11")
                .lastname("hrbp 12")
                .username("junit")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .macroPeopleUnits(Set.of(macroPeopleUnit))
                .build();

        Hrbp hrbp2 = Hrbp.builder()
                .firstname("hrbp 21")
                .lastname("hrbp 22")
                .username("test")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .macroPeopleUnits(Set.of(macroPeopleUnit))
                .build();

        hrbpRepository.saveAll(List.of(hrbp1, hrbp2));
        macroPeopleUnit.setHrbps(Set.of(hrbp1, hrbp2));
        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<Hrbp> actualList = hrbpRepository.findAllHrbpsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertNotNull(actualList);
        assertEquals(2, actualList.size());
        actualList.forEach(appUser -> assertEquals(macroPeopleUnitName,
                appUser.getMacroPeopleUnits().stream().toList().get(0).getName()));
    }

    @Test
    void UserRepository_findAllHrbpsByMacroPeopleUnitName_returnEmptyList() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<Hrbp> result = hrbpRepository.findAllHrbpsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(0, result.size());
    }
}