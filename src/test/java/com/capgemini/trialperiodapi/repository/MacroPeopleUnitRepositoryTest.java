package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
class MacroPeopleUnitRepositoryTest {

    @Autowired
    MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void MacroPeopleUnitRepository_findMacroPeopleUnitByName_returnMacroPeopleUnit() {

        // Given
        String macroPeopleUnitName = "macroPU";

        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName)
                .build();

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        Optional<MacroPeopleUnit> actualMacroPeopleUnit = macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName);

        // Then
        assertNotNull(actualMacroPeopleUnit);
        assertEquals(macroPeopleUnitName, actualMacroPeopleUnit.get().getName());

    }

    @Test
    void MacroPeopleUnitRepository_findMacroPeopleUnitByName_returnMacroPeopleUnitWhenCaseInsensitive() {

        // Given
        String name = "MacroPU";
        String macroPeopleUnitName = "macropu";

        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName)
                .build();

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        Optional<MacroPeopleUnit> actualMacroPeopleUnit = macroPeopleUnitRepository.findMacroPeopleUnitByName(name);

        // Then
        assertNotNull(actualMacroPeopleUnit);
        assertEquals(macroPeopleUnitName, actualMacroPeopleUnit.get().getName());
    }

    @Test
    void MacroPeopleUnitRepository_findMacroPeopleUnitByName_returnEmptyObjectCausedByNameDoesNotExist() {

        // Given
        String nonExistingName = "test";

        //
        Optional<MacroPeopleUnit> result = macroPeopleUnitRepository.findMacroPeopleUnitByName(nonExistingName);

        // Then
        assertThat(result).isEmpty();
    }

    /*
     * 14/06/2024
     * Il faut gérer les espaces dans la recherche pour s'assurer que les résultats incluent toutes les correspondances pertinentes.
     */
    @Test
    void MacroPeopleUnitRepository_findPeopleUnitNamesByKeyword_returnsListMacroPeopleUnitNames() {
        // Given
        String keyword = "macro";

        MacroPeopleUnit macroPeopleUnit1 = MacroPeopleUnit.builder()
                .name("macroPeopleUnitName1")
                .build();
        MacroPeopleUnit macroPeopleUnit2 = MacroPeopleUnit.builder()
                .name("macroPeopleUnitName2")
                .build();
        MacroPeopleUnit macroPeopleUnit3 = MacroPeopleUnit.builder()
                .name("PeopleUnit3")
                .deletedOn(null)
                .build();

        macroPeopleUnitRepository.save(macroPeopleUnit1);
        macroPeopleUnitRepository.save(macroPeopleUnit2);
        macroPeopleUnitRepository.save(macroPeopleUnit3);
        List<String> expectedList = List.of(macroPeopleUnit1.getName(), macroPeopleUnit2.getName());

        // When
        List<String> actualList = macroPeopleUnitRepository.findMacroPeopleUnitNamesByKeyword(keyword);

        // Then
        assertNotNull(actualList);
        assertEquals(2, actualList.size());
        assertEquals(expectedList, actualList);
    }

    @Test
    void MacroPeopleUnitRepository_findPeopleUnitNamesByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<String> resultList = macroPeopleUnitRepository.findMacroPeopleUnitNamesByKeyword(keyword);

        // Then
        assertEquals(0, resultList.size());
    }

}
