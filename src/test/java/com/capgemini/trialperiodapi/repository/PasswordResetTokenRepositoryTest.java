package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.PasswordResetToken;
import com.capgemini.trialperiodapi.model.auth.Role;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Date;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class PasswordResetTokenRepositoryTest {

    @Autowired
    PasswordResetTokenRepository passwordResetTokenRepository;
    @Autowired
    private UserRepository userRepository;


    @Test
    void PasswordResetTokenRepository_findPasswordResetTokenByCode_returnToken() {
        // Given
        String code = "code123";
        AppUser user = AppUser.builder()
                .firstname("userFirstname")
                .lastname("userLastname")
                .username("userName")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("123456")
                .build();

        userRepository.save(user);

        PasswordResetToken passwordResetToken = new PasswordResetToken();
        passwordResetToken.setUser(user);
        passwordResetToken.setCode(code);
        passwordResetToken.setToken("qwsexdrcftvgyuhnjikol");
        passwordResetToken.setExpirationTime(new Date());

        passwordResetTokenRepository.save(passwordResetToken);

        // When
        PasswordResetToken resultPwdToken = passwordResetTokenRepository.findPasswordResetTokenByCode(code);

        // Then
        assertNotNull(resultPwdToken);
        assertEquals(code, resultPwdToken.getCode());
    }

    @Test
    void PasswordResetTokenRepository_findPasswordResetTokenByCode_returnNullCausedByCodeDoesntExist() {
        // Given
        String nonExistingCode = "test";

        // When
        PasswordResetToken result = passwordResetTokenRepository.findPasswordResetTokenByCode(nonExistingCode);

        // Then
        assertNull(result);
    }

    @Test
    void PasswordResetTokenRepository_deleteAllPasswordResetTokensByUserId_tokensDeletedSuccessfully() {
        // Given

        AppUser user = AppUser.builder()
                .firstname("userFirstname")
                .lastname("userLastname")
                .username("userName")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("123456")
                .build();

        userRepository.save(user);

        PasswordResetToken passwordResetToken = new PasswordResetToken();
        passwordResetToken.setUser(user);
        passwordResetToken.setCode("code");
        passwordResetToken.setToken("qwsexdrcftvgyuhnjikol");
        passwordResetToken.setExpirationTime(new Date());

        passwordResetTokenRepository.save(passwordResetToken);

        // When
        passwordResetTokenRepository.deleteAllPasswordResetTokensByUserId(user.getId());
        Optional<PasswordResetToken> deletedPwdToken = passwordResetTokenRepository.findPasswordResetTokenByUserId(user.getId());

        // Then
        assertThat(deletedPwdToken).isEmpty();
    }

    @Test
    void PasswordResetTokenRepository_findPasswordResetTokenByUserId_returnToken() {
        // Given
        Long userId = 1L;
        AppUser user = AppUser.builder()
                .id(userId)
                .firstname("userFirstname")
                .lastname("userLastname")
                .username("userName")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("123456")
                .build();

        userRepository.save(user);
        PasswordResetToken passwordResetToken = new PasswordResetToken();
        passwordResetToken.setUser(user);
        passwordResetToken.setCode("code");
        passwordResetToken.setToken("qwsexdrcftvgyuhnjikol");
        passwordResetToken.setExpirationTime(new Date());

        passwordResetTokenRepository.save(passwordResetToken);

        // When
        Optional<PasswordResetToken> resultPwdResetToken = passwordResetTokenRepository.findPasswordResetTokenByUserId(userId);

        // Then
        assertNotNull(resultPwdResetToken);
        assertEquals(userId, resultPwdResetToken.get().getUser().getId());
    }

    @Test
    void PasswordResetTokenRepository_findPasswordResetTokenByUserId_returnEmptyObject() {
        // Given
        Long userId = 1L;

        // When
        Optional<PasswordResetToken> result = passwordResetTokenRepository.findPasswordResetTokenByUserId(userId);

        // Then
        assertThat(result).isEmpty();
    }
}
