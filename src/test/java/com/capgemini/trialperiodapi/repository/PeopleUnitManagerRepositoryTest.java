package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
class PeopleUnitManagerRepositoryTest {

    @Autowired
    PeopleUnitManagerRepository peopleUnitManagerRepository;
    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void PeopleUnitManagerRepository_findPumByEmail_returnPum() {

        // Given
        String email = "<EMAIL>";
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setEmail(email);
        peopleUnitManager.setFirstname("firstname");
        peopleUnitManager.setLastname("lastname");

        peopleUnitManagerRepository.save(peopleUnitManager);

        // When
        Optional<PeopleUnitManager> resultPum = peopleUnitManagerRepository.findPumByEmail(email);

        // Then
        assertNotNull(resultPum);
        assertEquals(email, resultPum.get().getEmail());
    }

    @Test
    void PeopleUnitManagerRepository_findPumByEmail_returnEmptyObject() {

        // Given
        String nonExistingEmail = "<EMAIL>";

        // When
        Optional<PeopleUnitManager> result = peopleUnitManagerRepository.findPumByEmail(nonExistingEmail);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void PeopleUnitManagerRepository_findAllPumsByMacroPeopleUnitName_returnListOfPums() {

        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        PeopleUnitManager pum1 = new PeopleUnitManager();
        pum1.getMacroPeopleUnits().add(macroPeopleUnit);
        pum1.setFirstname("pum 11");
        pum1.setLastname("pum 12");
        pum1.setEmail("<EMAIL>");

        PeopleUnitManager pum2 = new PeopleUnitManager();
        pum2.getMacroPeopleUnits().add(macroPeopleUnit);
        pum2.setFirstname("pum 21");
        pum2.setLastname("pum 22");
        pum2.setEmail("<EMAIL>");

        macroPeopleUnit.getPums().add(pum1);
        macroPeopleUnit.getPums().add(pum2);

        peopleUnitManagerRepository.save(pum1);
        peopleUnitManagerRepository.save(pum2);
        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<PeopleUnitManager> resultPums = peopleUnitManagerRepository
                .findAllPumsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertNotNull(resultPums);
        assertEquals(2, resultPums.size());
        resultPums.forEach(pum -> assertEquals(macroPeopleUnitName,
                pum.getMacroPeopleUnits().stream().toList().get(0).getName()));

    }

    @Test
    void PeopleUnitManagerRepository_findAllPumsByMacroPeopleUnitName_returnEmptyList() {

        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<PeopleUnitManager> result = peopleUnitManagerRepository.findAllPumsByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void PeopleUnitManagerRepository_findPeopleUnitManagersByKeyword_returnsPeopleUnitManagersList() {
        // Given
        String keyword = "people";

        PeopleUnitManager pum1 = new PeopleUnitManager();
                pum1.setFirstname("people Unit1");
                pum1.setLastname("people Unit2");
        pum1.setEmail("<EMAIL>");


        PeopleUnitManager pum2 = new PeopleUnitManager();
        pum2.setFirstname("People Unit Manager 1");
        pum2.setLastname("People Unit Manager 2");
        pum2.setEmail("<EMAIL>");


        PeopleUnitManager pum3 = new PeopleUnitManager();
        pum3.setFirstname("Unit Manager1");
        pum3.setLastname("Unit Manager2");
        pum3.setEmail("<EMAIL>");


        PeopleUnitManager pum4 = new PeopleUnitManager();
        pum4.setFirstname("Manager1");
        pum4.setLastname("Manager2");
        pum4.setEmail("<EMAIL>");

        peopleUnitManagerRepository.save(pum1);
        peopleUnitManagerRepository.save(pum2);
        peopleUnitManagerRepository.save(pum3);
        peopleUnitManagerRepository.save(pum4);

        // When
        List<PeopleUnitManager> resultList = peopleUnitManagerRepository.findPeopleUnitManagersByKeyword(keyword);

        // Then
        assertNotNull(resultList);
        assertEquals(3, resultList.size());
    }
    @Test
    void PeopleUnitManagerRepository_findPeopleUnitManagersByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<PeopleUnitManager> resultList = peopleUnitManagerRepository.findPeopleUnitManagersByKeyword(keyword);

        // Then
        assertEquals(0, resultList.size());
    }

}
