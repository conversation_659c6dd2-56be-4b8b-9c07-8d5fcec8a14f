package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class PeopleUnitRepositoryTest {

    @Autowired
    PeopleUnitRepository peopleUnitRepository;
    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;


    @Test
    void PeopleUnitRepository_findPeopleUnitById_returnPeopleUnit() {
        // Given
        Long peopleUnitId = 1L;

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .id(peopleUnitId)
                .name("test")
                .build();

        peopleUnitRepository.save(peopleUnit);

        // When
        Optional<PeopleUnit> actualPeopleUnit = peopleUnitRepository.findPeopleUnitById(peopleUnitId);

        // Then
        assertNotNull(actualPeopleUnit);
        assertEquals(peopleUnitId, actualPeopleUnit.get().getId());
    }

    @Test
    void PeopleUnitRepository_findPeopleUnitById_returnEmptyObjectCausedByIdDoesNotExist() {
        // Given
        Long nonExistingId = 2L;

        // When
        Optional<PeopleUnit> result = peopleUnitRepository.findPeopleUnitById(nonExistingId);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void PeopleUnitRepository_findPeopleUnitByName_returnPeopleUnit() {
        // Given
        String peopleUnitName = "test";
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name(peopleUnitName)
                .build();
        peopleUnitRepository.save(peopleUnit);

        // When
        Optional<PeopleUnit> actualPeopleUnit = peopleUnitRepository.findPeopleUnitByName(peopleUnitName);

        // Then
        assertNotNull(actualPeopleUnit);
        assertEquals(peopleUnitName, actualPeopleUnit.get().getName());
    }

    @Test
    void PeopleUnitRepository_findPeopleUnitByName_returnEmptyObjectCausedByNameDoesNotExist() {
        // Given
        String nonExistingName = "test";

        // When
        Optional<PeopleUnit> result = peopleUnitRepository.findPeopleUnitByName(nonExistingName);

        // Then
        assertThat(result).isEmpty();
    }

    /*
     * 13/06/2024
     * Il faut gérer les espaces dans la recherche pour s'assurer que les résultats incluent toutes les correspondances pertinentes.
     */
    @Test
    void PeopleUnitRepository_findPeopleUnitNamesByKeyword_returnPeopleUnitNames() {
        // Given
        String keyword = "peopleUnit";
        PeopleUnit peopleUnit1 = PeopleUnit.builder()
                .name("peopleUnit1")
                .build();
        PeopleUnit peopleUnit2 = PeopleUnit.builder()
                .name("peopleUnit2")
                .build();

        peopleUnitRepository.save(peopleUnit1);
        peopleUnitRepository.save(peopleUnit2);

        List<String> expectedList = List.of(peopleUnit1.getName(), peopleUnit2.getName());

        // When
        List<String> actualList = peopleUnitRepository.findPeopleUnitNamesByKeyword(keyword);

        // Then
        assertNotNull(actualList);
        assertEquals(expectedList, actualList);
    }


    @Test
    void PeopleUnitRepository_findPeopleUnitNamesByKeyword_returnEmptyList() {
        // Given
        String keyword = "keyword";

        // When
        List<String> resultList = peopleUnitRepository.findPeopleUnitNamesByKeyword(keyword);

        // Then
        assertEquals(0, resultList.size());
    }

    @Test
    void PeopleUnitRepository_findAllPeopleUnitByMacroPeopleUnitName_returnListOfPeopleUnits() {
        // Given
        String macroPeopleUnitName = "macroPeopleUnit";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        PeopleUnit peopleUnit1 = PeopleUnit.builder()
                .name("peopleUnit1")
                .macroPeopleUnit(macroPeopleUnit)
                .build();
        peopleUnitRepository.save(peopleUnit1);

        PeopleUnit peopleUnit2 = PeopleUnit.builder()
                .name("peopleUnit2")
                .macroPeopleUnit(macroPeopleUnit)
                .build();
        peopleUnitRepository.save(peopleUnit2);

        macroPeopleUnit.setPeopleUnits(new HashSet<>(Set.of(peopleUnit1)));
        macroPeopleUnit.setPeopleUnits(new HashSet<>(Set.of(peopleUnit2)));
        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<PeopleUnit> actualList = peopleUnitRepository.findAllPeopleUnitByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertNotNull(actualList);
        assertEquals(2, actualList.size());
        actualList.forEach(peopleUnit -> assertEquals(macroPeopleUnitName,
                peopleUnit.getMacroPeopleUnit().getName()));
    }

    @Test
    void PeopleUnitRepository_findAllPeopleUnitByMacroPeopleUnitName_returnEmptyList() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<PeopleUnit> resultList = peopleUnitRepository.findAllPeopleUnitByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(0, resultList.size());
    }

    @Test
    void PeopleUnitRepository_deletePeopleUnitById_deletePeopleUnit() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("peopleUnitName")
                .build();
        peopleUnitRepository.save(peopleUnit);

        // When
        peopleUnitRepository.delete(peopleUnit);

        // Then
        Optional<PeopleUnit> deletedPeopleUnit = peopleUnitRepository.findPeopleUnitById(peopleUnit.getId());
        assertThat(deletedPeopleUnit).isEmpty();
    }
}
