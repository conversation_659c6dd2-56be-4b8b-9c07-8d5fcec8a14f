package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.ProjectManager;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class ProjectManagerRepositoryTest {

    @Autowired
    private ProjectManagerRepository projectManagerRepository;

    @Autowired
    private ProjectRepository projectRepository;


    @Test
    void ProjectManagerRepository_findAllProjectManagersByProjectName_returnAllProjectManagersOfGivingProjectName() {

        // Given
        String projectName = "projectTest";
        Project project = new Project();
        project.setName(projectName);

        ProjectManager projectManager1 = new ProjectManager();
        projectManager1.setFirstname("test11");
        projectManager1.setLastname("test12");
        projectManager1.setEmail("<EMAIL>");
        projectManager1.getProjects().add(project);

        ProjectManager projectManager2 = new ProjectManager();
        projectManager2.setFirstname("test21");
        projectManager2.setLastname("test22");
        projectManager2.setEmail("<EMAIL>");
        projectManager2.getProjects().add(project);

        project.getProjectManagers().add(projectManager1);
        project.getProjectManagers().add(projectManager2);

        projectManagerRepository.save(projectManager1);
        projectManagerRepository.save(projectManager2);
        projectRepository.save(project);

        // When
        Set<ProjectManager> projectManagersList = projectManagerRepository.findAllProjectManagersByProjectName(projectName);

        // Then
        assertNotNull(projectManagersList);
        assertEquals(2, projectManagersList.size());
        projectManagersList.forEach(projectManager -> assertEquals(projectName,
                projectManager.getProjects().stream().toList().get(0).getName()));
    }

    @Test
    void ProjectManagerRepository_findAllProjectManagersByProjectName_returnEmptyList() {
        // Given
        String projectName = "projectTest";
        Project project = new Project();
        project.setName(projectName);
        projectRepository.save(project);

        // When
        Set<ProjectManager> result = projectManagerRepository.findAllProjectManagersByProjectName(projectName);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void ProjectManagerRepository_findProjectManagerByEmail_returnProjectManager() {

        // Given
        String email = "<EMAIL>";
        ProjectManager projectManager = new ProjectManager();
        projectManager.setEmail(email);
        projectManager.setFirstname("test1");
        projectManager.setLastname("test2");

        projectManagerRepository.save(projectManager);

        // When
        Optional<ProjectManager> actualProjectManager = projectManagerRepository.findProjectManagerByEmail(email);

        // Then
        assertNotNull(actualProjectManager);
        assertEquals(email, actualProjectManager.get().getEmail());
    }

    @Test
    void ProjectManagerRepository_findProjectManagerByEmail_returnEmptyObject() {

        // Given
        String nonExistingEmail = "<EMAIL>";

        // When
        Optional<ProjectManager> result = projectManagerRepository.findProjectManagerByEmail(nonExistingEmail);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void ProjectManagerRepository_findProjectManagersByKeyword_returnsListOfProjectManagers() {
        // Given
        String keyword = "test";
        ProjectManager projectManager1 = new ProjectManager();
        projectManager1.setFirstname("Test11");
        projectManager1.setLastname("Test12");
        projectManager1.setEmail("<EMAIL>");

        ProjectManager projectManager2 = new ProjectManager();
        projectManager2.setFirstname("test21");
        projectManager2.setLastname("test22");
        projectManager2.setEmail("<EMAIL>");

        ProjectManager projectManager3 = new ProjectManager();
        projectManager3.setFirstname("junit31");
        projectManager3.setLastname("junit32");
        projectManager3.setEmail("<EMAIL>");

        ProjectManager projectManager4 = new ProjectManager();
        projectManager4.setFirstname("junit41");
        projectManager4.setLastname("junit42");
        projectManager4.setEmail("<EMAIL>");

        projectManagerRepository.saveAll(List.of(projectManager1, projectManager2, projectManager3, projectManager4));

        // When
        List<ProjectManager> actuallList = projectManagerRepository.findProjectManagersByKeyword(keyword);

        // Then
        assertNotNull(actuallList);
        assertEquals(3, actuallList.size());
    }

    @Test
    void ProjectManagerRepository_findProjectManagersByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<ProjectManager> resultList = projectManagerRepository.findProjectManagersByKeyword(keyword);

        // Then
        assertEquals(0, resultList.size());
    }

    @Test
    void ProjectManagerRepository_deleteProjectManagerByEmail_deleteProjectManager() {
        // Given
        ProjectManager projectManager = new ProjectManager();
        projectManager.setFirstname("firstname");
        projectManager.setLastname("lastname");
        projectManager.setEmail("<EMAIL>");

        projectManagerRepository.save(projectManager);

        // When
        projectManagerRepository.deleteProjectManagerByEmail(projectManager.getEmail());

        // Then
        Optional<ProjectManager> deletedProjectManager = projectManagerRepository.findProjectManagerByEmail(projectManager.getEmail());
        assertThat(deletedProjectManager).isEmpty();
    }

    @Test
    void ProjectManagerRepository_findAllProjectManagersByKeyword_returnsProjectManagers() {
        // Given
        String keyword = "test";
        Pageable pageable = PageRequest.of(0, 10);

        ProjectManager projectManager1 = new ProjectManager();
        projectManager1.setFirstname("Test1");
        projectManager1.setLastname("Test2");
        projectManager1.setEmail("<EMAIL>");

        ProjectManager projectManager2 = new ProjectManager();
        projectManager2.setFirstname("test21");
        projectManager2.setLastname("test22");
        projectManager2.setEmail("<EMAIL>");

        ProjectManager projectManager3 = new ProjectManager();
        projectManager3.setFirstname("junit1");
        projectManager3.setLastname("junit2");
        projectManager3.setEmail("<EMAIL>");

        ProjectManager projectManager4 = new ProjectManager();
        projectManager4.setFirstname("junit21");
        projectManager4.setLastname("junit22");
        projectManager4.setEmail("<EMAIL>");

        projectManagerRepository.saveAll(List.of(projectManager1, projectManager2, projectManager3, projectManager4));

        // When
        Page<ProjectManager> actuallList = projectManagerRepository.findAllProjectManagersByKeyword(keyword, pageable);

        // Then
        assertNotNull(actuallList);
        assertEquals(3, actuallList.getContent().size());
    }

    @Test
    void ProjectManagerRepository_findAllProjectManagersByKeyword_returnEmptyList() {
        // Given
        String keyword = "test";
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<ProjectManager> resultList = projectManagerRepository.findAllProjectManagersByKeyword(keyword, pageable);

        // Then
        assertEquals(0, resultList.getContent().size());
    }
}
