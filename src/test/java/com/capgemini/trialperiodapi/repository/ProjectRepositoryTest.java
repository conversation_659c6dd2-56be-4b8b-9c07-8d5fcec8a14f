package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.Project;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
class ProjectRepositoryTest {

    @Autowired
    private ProjectRepository projectRepository;

    @Test
    void ProjectRepository_findProjectByName_returnsProject() {
        // Given
        String projectName = "junit";
        Project project = new Project();
        project.setName(projectName);

        projectRepository.save(project);

        // When
        Project foundProject = projectRepository.findProjectByName(projectName).orElseThrow();

        // Then
        assertNotNull(foundProject);
        assertEquals(projectName, foundProject.getName());
    }

    @Test
    void ProjectRepository_findProjectByName_returnsEmptyObject() {
        // Given
        String nonExistingProjectName = "test";

        // When
        Optional<Project> result = projectRepository.findProjectByName(nonExistingProjectName);

        // Then
        assertThat(result).isEmpty();
    }

    /*
     * 14/06/2024
     * Il faut gérer les espaces dans la recherche pour s'assurer que les résultats incluent toutes les correspondances pertinentes.
     */
    @Test
    void ProjectRepository_findProjectNamesByKeyword_returnsProjectNamesList() {
        // Given
        String keyword = "test";

        Project project1 = Project.builder()
                .name("test")
                .build();
        Project project2 = Project.builder()
                .name("test2")
                .build();

        projectRepository.save(project1);
        projectRepository.save(project2);

        List<String> expectedProjectNames = List.of(project1.getName(), project2.getName());

        // When
        List<String> actualProjectNames = projectRepository.findProjectNamesByKeyword(keyword);

        // Then
        assertNotNull(actualProjectNames);
        assertEquals(2, actualProjectNames.size());
        assertEquals(expectedProjectNames, actualProjectNames);
    }

    @Test
    void ProjectRepository_findProjectNamesByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<String> result = projectRepository.findProjectNamesByKeyword(keyword);

        // Then
        assertEquals(0, result.size());
    }
}
