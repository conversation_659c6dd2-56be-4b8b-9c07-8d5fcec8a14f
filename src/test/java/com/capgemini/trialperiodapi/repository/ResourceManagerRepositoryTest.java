package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.ResourceManager;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
class ResourceManagerRepositoryTest {

    @Autowired
    ResourceManagerRepository resourceManagerRepository;
    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void ResourceManagerRepository_findResourceManagerByEmail_returnResourceManager() {
        // Given
        String email = "<EMAIL>";
        ResourceManager resourceManager = new ResourceManager();
         resourceManager.setFirstname("firstname");
         resourceManager.setLastname("lastname");
         resourceManager.setEmail(email);

        resourceManagerRepository.save(resourceManager);

        // When
        Optional<ResourceManager> resourceManagerByEmail = resourceManagerRepository.findResourceManagerByEmail(email);

        // Then
        assertNotNull(resourceManagerByEmail);
        assertEquals(email, resourceManagerByEmail.get().getEmail());
    }

    @Test
    void ResourceManagerRepository_findResourceManagerByEmail_returnEmptyObjectCausedByEmailNotFound() {
        // Given
        String email = "<EMAIL>";

        // When
        Optional<ResourceManager> resourceManagerByEmail = resourceManagerRepository.findResourceManagerByEmail(email);

        // Then
        assertThat(resourceManagerByEmail).isEmpty();
    }

    @Test
    void ResourceManagerRepository_findResourceManagersByMacroPeopleUnitName_returnsListOfResourceManagers() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        ResourceManager rm1 = new ResourceManager();
        rm1.setFirstname("unit test11");
        rm1.setLastname("unit test12");
        rm1.setEmail("<EMAIL>");
        rm1.setMacroPeopleUnits(Set.of(macroPeopleUnit));

        ResourceManager rm2 = new ResourceManager();
        rm2.setFirstname("unit test21");
        rm2.setLastname("unit test22");
        rm2.setEmail("<EMAIL>");
        rm2.setMacroPeopleUnits(Set.of(macroPeopleUnit));

        resourceManagerRepository.saveAll(List.of(rm1,rm2));
        macroPeopleUnit.setResourceManagers(Set.of(rm1,rm2));
        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<ResourceManager> resultedList = resourceManagerRepository.findResourceManagersByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertNotNull(resultedList);
        assertEquals(2,resultedList.size());
        resultedList.forEach(resourceManager -> assertEquals(macroPeopleUnitName,
                resourceManager.getMacroPeopleUnits().stream().toList().get(0).getName()));
    }

    @Test
    void ResourceManagerRepository_findResourceManagersByMacroPeopleUnitName_returnsEmptyList() {
        // Given
        String macroPeopleUnitName = "test";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        macroPeopleUnitRepository.save(macroPeopleUnit);

        // When
        List<ResourceManager> resultedList = resourceManagerRepository.findResourceManagersByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(0, resultedList.size());
    }

    @Test
    void ResourceManagerRepository_findResourceManagersByKeyword_returnsResourceManagersByKeyword() {
        // Given
        String keyword = "Test";
        ResourceManager rm1 = new ResourceManager();
        rm1.setFirstname("unit test11");
        rm1.setLastname("unit test12");
        rm1.setEmail("<EMAIL>");

        ResourceManager rm2 = new ResourceManager();
        rm2.setFirstname("unit Test21");
        rm2.setLastname("unit Test22");
        rm2.setEmail("<EMAIL>");

        ResourceManager rm3 = new ResourceManager();
        rm3.setFirstname("junit1");
        rm3.setLastname("junit2");
        rm3.setEmail("<EMAIL>");

        ResourceManager rm4 = new ResourceManager();
        rm4.setFirstname("rm41");
        rm4.setLastname("rm42");
        rm4.setEmail("<EMAIL>");

        resourceManagerRepository.saveAll(List.of(rm1,rm2,rm3,rm4));

        // When
        List<ResourceManager> resultedList = resourceManagerRepository.findResourceManagersByKeyword(keyword);

        // Then
        assertNotNull(resultedList);
        assertEquals(3,resultedList.size());
    }
    @Test
    void ResourceManagerRepository_findResourceManagersByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<ResourceManager> resultedList = resourceManagerRepository.findResourceManagersByKeyword(keyword);

        // Then
        assertEquals(0,resultedList.size());
    }
}
