package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.*;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.time.LocalDate;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
class TrialPeriodRepositoryTest {

    @Autowired
    private TrialPeriodRepository trialPeriodRepository;

    @Autowired
    private CollaboratorRepository collaboratorRepository;

    @Autowired
    private PeopleUnitRepository peopleUnitRepository;

    @Test
    void TrialPeriodRepository_findTrialPeriodByCollaboratorId_returnsTrialPeriod() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("People Unit Name")
                .build();
        peopleUnitRepository.save(peopleUnit);

        Collaborator collaborator = Collaborator.builder()
                .id(1L)
                .ggid("123")
                .firstname("junit")
                .lastname("test")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .assignmentStatus(AssignmentStatus.SHADOW)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .peopleUnit(peopleUnit)
                .build();
        collaboratorRepository.save(collaborator);

        TrialPeriod trialPeriod = new TrialPeriod();
        trialPeriod.setCollaborator(collaborator);
        trialPeriod.setStatus(TrialPeriodStatus.CONFIRMED);
        trialPeriodRepository.save(trialPeriod);

        collaborator.setTrialPeriod(trialPeriod);
        collaboratorRepository.save(collaborator);

        // When
        TrialPeriod foundTrialPeriod = trialPeriodRepository.findTrialPeriodByCollaboratorId(collaborator.getId()).orElseThrow();

        // Then
        assertNotNull(foundTrialPeriod);
        assertEquals(collaborator.getId(), foundTrialPeriod.getCollaborator().getId());
    }

    @Test
    void TrialPeriodRepository_findTrialPeriodByCollaboratorId_returnsEmptyObject() {
        // Given
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name("People Unit Name")
                .build();
        peopleUnitRepository.save(peopleUnit);

        Collaborator collaborator = Collaborator.builder()
                .id(1L)
                .ggid("123")
                .firstname("junit")
                .lastname("test")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .assignmentStatus(AssignmentStatus.SHADOW)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .peopleUnit(peopleUnit)
                .build();
        collaboratorRepository.save(collaborator);

        // When
        Optional<TrialPeriod> result = trialPeriodRepository.findTrialPeriodByCollaboratorId(collaborator.getId());

        // Then
        assertThat(result).isEmpty();
    }
}
