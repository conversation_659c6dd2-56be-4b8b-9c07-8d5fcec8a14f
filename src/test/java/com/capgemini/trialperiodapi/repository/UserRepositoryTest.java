package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.Role;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class UserRepositoryTest {

    @Autowired
    UserRepository userRepository;

    @Autowired
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Test
    void UserRepository_findHrbpByUsername_returnHrbp() {
        // Given
        String username = "hrbp";
        AppUser hrbp = AppUser.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username(username)
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .build();
        userRepository.save(hrbp);

        // When
        Optional<AppUser> hrbpByUsername = userRepository.findUserByUsername(username);

        // Then
        assertNotNull(hrbpByUsername);
        assertEquals(username, hrbpByUsername.get().getUsername());
    }

    @Test
    void UserRepository_findHrbpByUsername_returnEmptyObjectCausedByUsernameNotFound() {
        // Given/
        String username = "hrbp";

        // When
        Optional<AppUser> result = userRepository.findUserByUsername(username);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void UserRepository_findHrbpByEmail_returnHrbp() {
        // Given
        String email = "<EMAIL>";
        AppUser hrbp = AppUser.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("test")
                .email(email)
                .role(Role.HRBP)
                .password("password")
                .build();
        userRepository.save(hrbp);

        // When
        Optional<AppUser> hrbpByEmail = userRepository.findUserByEmail(email);

        // Then
        assertNotNull(hrbpByEmail);
        assertEquals(email, hrbpByEmail.get().getEmail());
    }

    @Test
    void UserRepository_findHrbpByEmail_returnEmptyObjectCausedByEmailNotFound() {
        // Given/
        String email = "<EMAIL>";

        // When
        Optional<AppUser> result = userRepository.findUserByEmail(email);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void UserRepository_findHrbpById_returnHrbp() {
        // Given
        Long userId = 1L;
        AppUser hrbp = AppUser.builder()
                .id(userId)
                .firstname("firstname")
                .lastname("lastname")
                .username("test")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .build();
        userRepository.save(hrbp);

        // When
        Optional<AppUser> hrbpById = userRepository.findUserById(userId);

        // Then
        assertNotNull(hrbpById);
        assertEquals(userId, hrbpById.get().getId());
    }

    @Test
    void UserRepository_findHrbpById_returnEmptyObjectCausedByIdNotFound() {
        // Given/
        Long userId = 1L;

        // When
        Optional<AppUser> result = userRepository.findUserById(userId);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void UserRepository_findUsersByKeyword_returnsHrbp() {
        // Given
        String keyword = "TeSt";

        AppUser hrbp1 = AppUser.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .build();

        AppUser hrbp2 = AppUser.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("name")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("password")
                .build();
        userRepository.save(hrbp2);
        userRepository.save(hrbp1);

        // When
        List<AppUser> actualList = userRepository.findUsersByKeyword(keyword);

        // Then
        assertNotNull(actualList);
        assertEquals(2, actualList.size());
    }

    @Test
    void UserRepository_findUsersByKeyword_returnsEmptyList() {
        // Given
        String keyword = "test";

        // When
        List<AppUser> result = userRepository.findUsersByKeyword(keyword);

        // Then
        assertEquals(0, result.size());
    }
}
