package com.capgemini.trialperiodapi.repository;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Disabled
@DataJpaTest
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class VerificationTokenRepositoryTest {

    @Autowired
    private VerificationTokenRepository verificationTokenRepository;

    @Autowired
    private UserRepository userRepository;


    @Test
    void VerificationTokenRepository_findValidVerificationTokensByUserId_getThreeValidTokens() {
        // Given
        String tokenExample1 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyNzk1NCwiZXhwIjoxNzEyMzI4MDQxfQ.Cs2xCF12HNQ7T6E2bLAZg9RQJYoHwiqkxqI89AtVhbAb";
        String tokenExample2 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyNzk3MiwiZXhwIjoxNzEyMzI4MDU4fQ.yKzjIwS2b_AIMqPoNb2HANUaHoDWwMx2jafHztkIpHkc";
        String tokenExample3 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyNzk4NCwiZXhwIjoxNzEyMzI4MDcxfQ.fhFl7b8sG0jJ9nWfwL13K6BDpuvKC1l556F623RIKCId";
        String tokenExample4 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyODAwMCwiZXhwIjoxNzEyMzI4MDg3fQ.p7ElNkD0nFO5uDL-mEHjO53HTBa192afjEYMqdk8WVUe";

        AppUser user = AppUser.builder()
                .id(1L)
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("12345678")
                .build();
        userRepository.save(user);


        VerificationToken validToken = new VerificationToken();
        validToken.setToken(tokenExample1);
        validToken.setExpired(false);
        validToken.setRevoked(false);
        validToken.setUser(user);
        validToken.setCode("wxcvbn");
        validToken.setExpirationTime(new Date());

        VerificationToken nonValidToken1 = new VerificationToken();
        nonValidToken1.setToken(tokenExample2);
        nonValidToken1.setExpired(true);
        nonValidToken1.setRevoked(false);
        nonValidToken1.setUser(user);
        nonValidToken1.setCode("derftgyuhjio");
        nonValidToken1.setExpirationTime(new Date());

        VerificationToken nonValidToken2 = new VerificationToken();
        nonValidToken2.setToken(tokenExample3);
        nonValidToken2.setExpired(false);
        nonValidToken2.setRevoked(true);
        nonValidToken2.setUser(user);
        nonValidToken2.setCode("wxcv");
        nonValidToken2.setExpirationTime(new Date());

        VerificationToken nonValidToken3 = new VerificationToken();
        nonValidToken3.setToken(tokenExample4);
        nonValidToken3.setExpired(true);
        nonValidToken3.setRevoked(true);
        nonValidToken3.setUser(user);
        nonValidToken3.setCode("dfghjkl");
        nonValidToken3.setExpirationTime(new Date());

        verificationTokenRepository.saveAll(List.of(validToken, nonValidToken1, nonValidToken2, nonValidToken3));

        // When
        List<VerificationToken> validTokens = verificationTokenRepository.findValidVerificationTokensByUserId(user.getId());

        // Then
        assertThat(validTokens.size()).isEqualTo(1);
        assertTrue(validTokens.contains(validToken));
    }

    @Test
    void VerificationTokenRepository_findValidVerificationTokensByUserId_returnEmptyList() {
        // Given
        String tokenExample1 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyNzk3MiwiZXhwIjoxNzEyMzI4MDU4fQ.yKzjIwS2b_AIMqPoNb2HANUaHoDWwMx2jafHztkIpHk";
        String tokenExample2 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyNzk4NCwiZXhwIjoxNzEyMzI4MDcxfQ.fhFl7b8sG0jJ9nWfwL13K6BDpuvKC1l556F623RIKCI";
        String tokenExample3 = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhYm91bmFzcyIsImlhdCI6MTcxMjMyODAwMCwiZXhwIjoxNzEyMzI4MDg3fQ.p7ElNkD0nFO5uDL-mEHjO53HTBa192afjEYMqdk8WVU";

        AppUser user = AppUser.builder()
                .id(1L)
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("12345678")
                .build();
        userRepository.save(user);

        VerificationToken nonValidToken1 = new VerificationToken();
        nonValidToken1.setToken(tokenExample1);
        nonValidToken1.setExpired(true);
        nonValidToken1.setRevoked(false);
        nonValidToken1.setUser(user);
        nonValidToken1.setCode("derftgyuhjio");
        nonValidToken1.setExpirationTime(new Date());

        VerificationToken nonValidToken2 = new VerificationToken();
        nonValidToken2.setToken(tokenExample2);
        nonValidToken2.setExpired(false);
        nonValidToken2.setRevoked(true);
        nonValidToken2.setUser(user);
        nonValidToken2.setCode("wxcvbn");
        nonValidToken2.setExpirationTime(new Date());

        VerificationToken nonValidToken3 = new VerificationToken();
        nonValidToken3.setToken(tokenExample3);
        nonValidToken3.setExpired(true);
        nonValidToken3.setRevoked(true);
        nonValidToken3.setUser(user);
        nonValidToken3.setCode("sdfghjk");
        nonValidToken3.setExpirationTime(new Date());

        verificationTokenRepository.saveAll(List.of(nonValidToken1, nonValidToken2, nonValidToken3));

        // When
        List<VerificationToken> validTokens = verificationTokenRepository.findValidVerificationTokensByUserId(user.getId());

        // Then
        assertEquals(0, validTokens.size());
    }

    @Test
    void VerificationTokenRepository_deleteVerificationTokenByToken_tokenDeletedSuccessfully() {
        // Given
        String tokenExample1 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
        String tokenExample2 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4iLCJpYXQiOjE1MTYyMzkwMjJ9.A6Ak1IC1KhtSzAor4-i-bZhmCHQya-sRlPy9-DGgQwA";

        VerificationToken token1 = new VerificationToken();
        token1.setToken(tokenExample1);
        token1.setCode("poljh");
        token1.setExpirationTime(new Date());
        verificationTokenRepository.save(token1);

        VerificationToken token2 = new VerificationToken();
        token2.setToken(tokenExample2);
        token2.setCode("wxdfgyu");
        token2.setExpirationTime(new Date());
        verificationTokenRepository.save(token2);

        // When
        verificationTokenRepository.deleteVerificationTokenByToken(token1.getToken());

        // Then
        assertThat(verificationTokenRepository.findAll().size()).isEqualTo(1);
        assertThat(verificationTokenRepository.findAll().get(0).getToken()).isEqualTo(tokenExample2);
    }

    @Test
    void VerificationTokenRepository_findVerificationTokenByToken_returnTheToken() {
        // Given
        String tokenExample = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

        VerificationToken token = new VerificationToken();
        token.setToken(tokenExample);
        token.setCode("derftgio");
        token.setExpirationTime(new Date());
        verificationTokenRepository.save(token);

        // When
        VerificationToken retrieveToken = verificationTokenRepository.findVerificationTokenByToken(tokenExample).orElseThrow();

        // Then
        assertThat(retrieveToken).isNotNull();
        assertThat(retrieveToken.getToken()).isEqualTo(tokenExample);
    }

    @Test
    void VerificationTokenRepository_findVerificationTokenByToken_returnEmptyObject() {
        // Given
        String tokenExample = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

        // When
        Optional<VerificationToken> retrieveToken = verificationTokenRepository.findVerificationTokenByToken(tokenExample);

        // Then
        assertThat(retrieveToken).isEmpty();
    }

    @Test
    void VerificationTokenRepository_findVerificationTokenByCode_returnTheToken() {
        // Given
        String code = "b46fd8";

        VerificationToken token = new VerificationToken();
        token.setCode(code);
        token.setToken("sdxfcvghjkl");
        token.setExpirationTime(new Date());
        verificationTokenRepository.save(token);

        // When
        VerificationToken retrieveToken = verificationTokenRepository.findVerificationTokenByCode(code).orElseThrow();

        // Then
        assertThat(retrieveToken).isNotNull();
        assertThat(retrieveToken.getCode()).isEqualTo(code);
    }

    @Test
    void VerificationTokenRepository_findVerificationTokenByCode_returnEmpty() {
        // Given
        String code = "b56fd8";

        // When
        Optional<VerificationToken> result = verificationTokenRepository.findVerificationTokenByCode(code);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void VerificationTokenRepository_deleteAllVerificationTokensByUserId_deleteOneOfTwo() {
        // Given
        String tokenExample1 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4iLCJpYXQiOjE1MTYyMzkwMjJ9.A6Ak1IC1KhtSzAor4-i-bZhmCHQya-sRlPy9-DGgQwA";
        String tokenExample2 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvIG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.WlT_QjFWd85BQKBiUwiiBTyMC9MJnSvhk6XA8KcX4H8";

        AppUser user1 = AppUser.builder()
                .id(1L)
                .username("junit")
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("12345678")
                .build();

        AppUser user2 = AppUser.builder()
                .id(2L)
                .username("junit2")
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .password("1234567")
                .build();

        userRepository.saveAll(List.of(user1, user2));

        VerificationToken token1 = new VerificationToken();
        token1.setToken(tokenExample1);
        token1.setUser(user1);
        token1.setCode("dfgh");
        token1.setExpirationTime(new Date());


        VerificationToken token2 = new VerificationToken();
        token2.setToken(tokenExample2);
        token2.setCode("vbnb");
        token2.setExpirationTime(new Date());
        token2.setUser(user2);

        verificationTokenRepository.saveAll(List.of(token1, token2));

        // When
        verificationTokenRepository.deleteAllVerificationTokensByUserId(user1.getId());

        // Then
        assertThat(verificationTokenRepository.findAll().size()).isEqualTo(1);
    }

    @Test
    void VerificationTokenRepository_findAllVerificationTokensByUserId_returnAllTokensOfTheUser() {
        // Given
        String tokenExample1 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4iLCJpYXQiOjE1MTYyMzkwMjJ9a.A6Ak1IC1KhtSzAor4-i-bZhmCHQya-sRlPy9-DGgQwA";
        String tokenExample2 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvIG9lIiwiaWF0IjoxNTE2MjM5MDIyfQa.WlT_QjFWd85BQKBiUwiiBTyMC9MJnSvhk6XA8KcX4H8";
        String tokenExample3 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvIHMgb2UiLCJpYXQiOjE1MTYyMzkwMjJ9aa.uVWOtv2cKHJBDtvGZhpw9q2TamT_ym_K8NqBj_V4C90";

        AppUser user1 = AppUser.builder()
                .id(1L)
                .firstname("firstname")
                .lastname("lastname")
                .username("test")
                .password("123456")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .build();

        AppUser user2 = AppUser.builder()
                .id(2L)
                .firstname("firstname")
                .lastname("lastname")
                .username("test2")
                .password("12345678")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .build();

        userRepository.saveAll(List.of(user1, user2));

        VerificationToken token1 = new VerificationToken();
        token1.setToken(tokenExample1);
        token1.setUser(user1);
        token1.setCode("vbnb");
        token1.setExpirationTime(new Date());

        VerificationToken token2 = new VerificationToken();
        token2.setToken(tokenExample2);
        token2.setUser(user1);
        token2.setCode("xcvb");
        token2.setExpirationTime(new Date());

        VerificationToken token3 = new VerificationToken();
        token3.setToken(tokenExample3);
        token3.setUser(user2);
        token3.setCode("fghjk");
        token3.setExpirationTime(new Date());

        verificationTokenRepository.saveAll(List.of(token1, token2, token3));

        // When
        List<VerificationToken> tokens = verificationTokenRepository.findAllVerificationTokensByUserId(user1.getId());

        // Then
        assertThat(tokens.size()).isEqualTo(2);
    }


}