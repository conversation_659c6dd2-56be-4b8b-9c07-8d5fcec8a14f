package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class AbstractUploadServiceTest {
    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private FileUtil fileUtil;

    @Mock
    private MultipartFile multipartFile;

    private AbstractUploadServiceImpl uploadService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        uploadService = new AbstractUploadServiceImpl(messageSourceUtil, fileUtil);
    }

    // Internal concrete class
    private class AbstractUploadServiceImpl extends AbstractUploadService {

        public AbstractUploadServiceImpl(MessageSourceUtil messageUtil, FileUtil fileUtil) {
            super(messageUtil, fileUtil);
            this.messageUtil = messageUtil;
            this.fileUtil = fileUtil;
        }

        @Override
        protected Integer parseCsv(MultipartFile file) {
            return 10;
        }
    }

    @Test
    void AbstractUploadService_upload_Success() {
        // Given
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getOriginalFilename()).thenReturn("test.csv");

        doNothing().when(fileUtil).checkFileExtension(multipartFile);
        doNothing().when(fileUtil).checkFileEncoding(multipartFile);

        Integer expectedParsedResult = 10;

        // When
        Integer result = uploadService.upload(multipartFile);

        // Then
        verify(fileUtil).checkFileExtension(multipartFile);
        verify(fileUtil).checkFileEncoding(multipartFile);
        assertEquals(expectedParsedResult, result);
    }

    @Test
    void AbstractUploadService_upload_EmptyFile() {
        // Given
        when(multipartFile.isEmpty()).thenReturn(true);
        when(messageSourceUtil.getMessage("exception.file.not-found"))
                .thenReturn("File not found");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            uploadService.upload(multipartFile);
        });
        assertEquals("File not found", exception.getMessage());
        verify(fileUtil, never()).checkFileExtension(any());
        verify(fileUtil, never()).checkFileEncoding(any());
    }

    @Test
    void AbstractUploadService_upload_InvalidFileExtension() {
        // Given
        when(multipartFile.isEmpty()).thenReturn(false);
        doThrow(new IllegalArgumentException("Invalid file extension"))
                .when(fileUtil).checkFileExtension(multipartFile);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            uploadService.upload(multipartFile);
        });
        assertEquals("Invalid file extension", exception.getMessage());
        verify(fileUtil, never()).checkFileEncoding(any());
    }

    @Test
    void AbstractUploadService_upload_InvalidFileEncoding() {
        // Given
        when(multipartFile.isEmpty()).thenReturn(false);
        doNothing().when(fileUtil).checkFileExtension(multipartFile);
        doThrow(new IllegalArgumentException("Invalid file encoding"))
                .when(fileUtil).checkFileEncoding(multipartFile);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            uploadService.upload(multipartFile);
        });
        assertEquals("Invalid file encoding", exception.getMessage());
        verify(fileUtil).checkFileExtension(multipartFile);
        verify(fileUtil).checkFileEncoding(multipartFile);
    }
}