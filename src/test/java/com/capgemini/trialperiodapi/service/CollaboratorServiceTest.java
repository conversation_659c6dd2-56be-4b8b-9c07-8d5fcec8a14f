package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.CollaboratorCriteria;
import com.capgemini.trialperiodapi.dto.CollaboratorCsvRepresentation;
import com.capgemini.trialperiodapi.dto.request.CollaboratorRequestDTO;
import com.capgemini.trialperiodapi.dto.response.CollaboratorResponseDTO;
import com.capgemini.trialperiodapi.mapper.CollaboratorMapper;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.CollaboratorRepository;
import com.capgemini.trialperiodapi.repository.TrialPeriodRepository;
import com.capgemini.trialperiodapi.util.DateUtil;
import com.capgemini.trialperiodapi.util.FileUtil;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class CollaboratorServiceTest {
    @InjectMocks
    private CollaboratorService collaboratorService;

    @Mock
    private CollaboratorRequestDTO collaboratorRequestDTO;

    @Mock
    private CollaboratorRepository collaboratorRepository;

    @Mock
    private TrialPeriodRepository trialPeriodRepository;

    @Mock
    private ITrialPeriodService trialPeriodService;

    @Mock
    private CollaboratorMapper collaboratorMapper;

    @Mock
    private IPeopleUnitService peopleUnitService;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private ProjectService projectService;

    @Mock
    private IUserService userService;

    @Mock
    private FileUtil fileUtil;

    @Mock
    private DateUtil dateUtil;

    @BeforeEach
    void setUp() {
        openMocks(this);
        collaboratorService = new CollaboratorService(collaboratorRepository, trialPeriodRepository, trialPeriodService, collaboratorMapper, peopleUnitService, messageSourceUtil, projectService, userService, fileUtil, dateUtil);
    }

    @Test
    void CollaboratorService_getAllCollaborators_returnListOfCollaborators() {
        // Given
        CollaboratorCriteria criteria = new CollaboratorCriteria(
                "test", "A",
                "A1", Collections.singleton("PE en cours"),
                "Opportunity 0%", "Employé"
        );

        Collaborator collaborator1 = Collaborator.builder()
                .id(1L)
                .ggid("ggid1")
                .firstname("firstname 1")
                .lastname("lastname 1")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .build();

        Collaborator collaborator2 = Collaborator.builder()
                .id(2L)
                .ggid("ggid1")
                .firstname("firstname 2")
                .lastname("lastname 2")
                .entryDate(LocalDate.now())
                .status(CollaboratorStatus.EMPLOYEE)
                .globalGrade(GlobalGrade.A)
                .localGrade(LocalGrade.A1)
                .build();

        CollaboratorResponseDTO collaboratorResponseDTO1 =
                CollaboratorResponseDTO.builder()
                        .id(1L)
                        .build();
        CollaboratorResponseDTO collaboratorResponseDTO2 =
                CollaboratorResponseDTO.builder()
                        .id(2L)
                        .build();

        List<Collaborator> collaboratorList = List.of(collaborator1, collaborator2);

        Pageable pageable = PageRequest.of(0, 10);
        PageImpl<Collaborator> collaboratorPage = new PageImpl<>(collaboratorList, pageable, collaboratorList.size());

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(collaboratorRepository.findAll((Specification<Collaborator>) any(), any(Pageable.class)))
                .thenReturn(collaboratorPage);
        when(collaboratorMapper.toCollaboratorResponseDTO(collaborator1))
                .thenReturn(collaboratorResponseDTO1);
        when(collaboratorMapper.toCollaboratorResponseDTO(collaborator2))
                .thenReturn(collaboratorResponseDTO2);

        // When
        Page<CollaboratorResponseDTO> collaboratorResponseDTOs = collaboratorService.getAllCollaborators(criteria, principal, pageable);

        // Then
        assertEquals(2, collaboratorResponseDTOs.getContent().size());
        verify(collaboratorRepository).findAll((Specification<Collaborator>) any(), any(Pageable.class));
        verify(collaboratorMapper).toCollaboratorResponseDTO(collaborator1);
        verify(collaboratorMapper).toCollaboratorResponseDTO(collaborator2);
    }

    @Test
    void CollaboratorService_withFilters() {
        // Given
        CollaboratorCriteria criteria = new CollaboratorCriteria(
                "test", "A",
                "A1", Collections.singleton("PE en cours"),
                "Opportunity 0%", "Employé"
        );

        AppUser connectedUser = AppUser.builder()
                .id(1L)
                .username("junit")
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(principal.getPrincipal()).thenReturn(connectedUser);

        // When
        Specification<Collaborator> collaborator = collaboratorService.withFilters(criteria, principal);

        // Then
        assertNotNull(collaborator);
    }

    @Disabled("Needs PowerMockito to mock private method (which has vulnerabilities)")
    void CollaboratorService_saveCollaborator_collaboratorSavedSuccessfully() {
        // Given
        CollaboratorRequestDTO requestDTO = CollaboratorRequestDTO.builder()
                .ggid("ggid")
                .firstname("firstName")
                .lastname("lastName")
                .entryDate(LocalDate.now())
                .status(String.valueOf(CollaboratorStatus.EMPLOYEE))
                .globalGrade(String.valueOf(GlobalGrade.A))
                .localGrade(String.valueOf(LocalGrade.A1))
                .trialPeriodStatus(String.valueOf(TrialPeriodStatus.IN_PROGRESS))
                .assignmentStatus(String.valueOf(AssignmentStatus.SHADOW))
                .projectName("projectName")
                .peopleUnitName("peopleUnitName")
                .build();

        when(peopleUnitService.getPeopleUnitByName(requestDTO.getPeopleUnitName()))
                .thenReturn(new PeopleUnit());
        when(projectService.getProjectByName(requestDTO.getProjectName()))
                .thenReturn(new Project());

        when(collaboratorRepository.saveAndFlush(any(Collaborator.class))).thenReturn(new Collaborator());

        // When
        collaboratorService.saveCollaborator(requestDTO);

        // Then
        verify(collaboratorRepository).saveAndFlush(any(Collaborator.class));
        verify(trialPeriodService).handleTrialPeriod(
                any(Collaborator.class), any(TrialPeriodStatus.class),
                anyBoolean(), anyBoolean(), anyString());
    }

    @Test
    void CollaboratorService_getCollaboratorDTOById_succeed() {
        // Given
        Long collaboratorId = 1L;
        Collaborator collaborator = new Collaborator();
        collaborator.setId(collaboratorId);

        CollaboratorResponseDTO collaboratorResponseDTO = CollaboratorResponseDTO.builder()
                .id(1L)
                .build();

        when(collaboratorRepository.findCollaboratorById(collaboratorId))
                .thenReturn(Optional.of(collaborator));

        when(collaboratorMapper.toCollaboratorResponseDTO(collaborator))
                .thenReturn(collaboratorResponseDTO);

        // When
        CollaboratorResponseDTO responseDTO = collaboratorService.getCollaboratorDTOById(collaboratorId);

        // Then
        assertNotNull(responseDTO);
        assertEquals(collaboratorId, responseDTO.getId());
        verify(collaboratorRepository).findCollaboratorById(collaboratorId);
        verify(collaboratorMapper).toCollaboratorResponseDTO(collaborator);
    }

    @Test
    void CollaboratorService_getCollaboratorDTOById_throwsEntityNotFoundException() {
        // Given
        Long nonExistingI = 1L;
        when(collaboratorRepository.findCollaboratorById(nonExistingI))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> collaboratorService.getCollaboratorDTOById(nonExistingI));
    }

    @Test
    void CollaboratorService_getCollaboratorByGgid_succeed() {
        // Given
        String collaboratorGgid = "ggid";
        Collaborator collaborator = new Collaborator();
        collaborator.setGgid(collaboratorGgid);

        when(collaboratorRepository.findCollaboratorByGgid(collaboratorGgid))
                .thenReturn(Optional.of(collaborator));

        // When
        Collaborator actualCollaborator = collaboratorService.getCollaboratorByGgid(collaboratorGgid);

        // Then
        assertNotNull(actualCollaborator);
        assertEquals(collaboratorGgid, actualCollaborator.getGgid());
    }

    @Test
    void CollaboratorService_getCollaboratorByGgid_throwsEntityNotFoundException() {
        // Given
        String nonExistingGgid = "ggid";
        when(collaboratorRepository.findCollaboratorByGgid(nonExistingGgid))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> collaboratorService.getCollaboratorByGgid(nonExistingGgid));
    }

    @Test
    void CollaboratorService_updateCollaborator_collaboratorUpdatedSuccessfully() {
        // Given
        Long collaboratorId = 1L;
        Collaborator existCollaborator = Collaborator.builder()
                .entryDate(LocalDate.now())
                .peopleUnit(PeopleUnit.builder().name("People Unit Name").build())
                .trialPeriod(new TrialPeriod())
                .id(collaboratorId)
                .build();


        CollaboratorRequestDTO requestDTO = CollaboratorRequestDTO.builder()
                .ggid("ggid")
                .firstname("firstName")
                .lastname("lastName")
                .entryDate(LocalDate.now())
                .status(String.valueOf(CollaboratorStatus.EMPLOYEE))
                .globalGrade(String.valueOf(GlobalGrade.A))
                .localGrade(String.valueOf(LocalGrade.A1))
                .trialPeriodStatus(String.valueOf(TrialPeriodStatus.IN_PROGRESS))
                .assignmentStatus(String.valueOf(AssignmentStatus.SHADOW))
                .projectName("projectName")
                .peopleUnitName("peopleUnitName")
                .build();

        when(collaboratorRepository.findCollaboratorById(collaboratorId))
                .thenReturn(Optional.ofNullable(existCollaborator));

        // When
        collaboratorService.updateCollaborator(collaboratorId, requestDTO);

        // Then
        verify(collaboratorRepository).findCollaboratorById(collaboratorId);
        verify(collaboratorRepository).saveAndFlush(existCollaborator);
        assertEquals(requestDTO.getGgid(), existCollaborator.getGgid());
        assertEquals(requestDTO.getFirstname(), existCollaborator.getFirstname());
        assertEquals(requestDTO.getEmail(), existCollaborator.getEmail());
    }

    @Test
    void CollaboratorService_deleteCollaboratorById_CollaboratorDeletedSuccessfully() {
        // Given
        String ggid = "ggid";
        TrialPeriod trialPeriod = new TrialPeriod();
        Collaborator collaborator = new Collaborator();
        collaborator.setGgid(ggid);
        collaborator.setTrialPeriod(trialPeriod);

        when(collaboratorRepository.findCollaboratorByGgid(ggid))
                .thenReturn(Optional.of(collaborator));

        // When
        collaboratorService.deleteCollaboratorByGgid(ggid);

        // Then
        assertNull(collaborator.getProject());
        assertNull(collaborator.getPeopleUnit());
        verify(collaboratorRepository).save(collaborator);
        verify(trialPeriodRepository).deleteById(collaborator.getTrialPeriod().getId());
        verify(collaboratorRepository).deleteById(collaborator.getId());
    }

    @Test
    void testSaveCollaborator_Success() {
        // Given
        when(collaboratorRequestDTO.getGgid()).thenReturn("GGID123");
        when(collaboratorRequestDTO.getEmail()).thenReturn("<EMAIL>");
        when(collaboratorRequestDTO.getFirstname()).thenReturn("John");
        when(collaboratorRequestDTO.getLastname()).thenReturn("Doe");
        when(collaboratorRequestDTO.getEntryDate()).thenReturn(LocalDate.now());
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("A1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(collaboratorRequestDTO.getPeopleUnitName()).thenReturn("PeopleUnit1");
        when(collaboratorRequestDTO.getProjectName()).thenReturn("Project1");
        when(collaboratorRequestDTO.getTrialPeriodStatus()).thenReturn("IN_PROGRESS");
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("SHADOW");
        when(collaboratorRequestDTO.getComment()).thenReturn("Some words");
        when(collaboratorRequestDTO.isInterviewed()).thenReturn(true);

        PeopleUnit peopleUnit = new PeopleUnit();
        when(peopleUnitService.getPeopleUnitByName("PeopleUnit1")).thenReturn(peopleUnit);

        Project project = new Project();
        when(projectService.getProjectByName("Project1")).thenReturn(project);

        // When
        collaboratorService.saveCollaborator(collaboratorRequestDTO);

        // Then
        verify(collaboratorRepository).saveAndFlush(any(Collaborator.class));
        verify(trialPeriodService).handleTrialPeriod(any(Collaborator.class), any(TrialPeriodStatus.class),
                anyBoolean(), anyBoolean(), anyString());
    }

    /*@Test
    void testSaveCollaborator_DuplicateGgidOrEmail() {
        // Arrange
        when(collaboratorRequestDTO.getGgid()).thenReturn("GGID123");
        when(collaboratorRequestDTO.getEmail()).thenReturn("<EMAIL>");

        // Simulez la validation de l'unicité
        doThrow(new IllegalArgumentException("GGID ou email déjà existant"))
                .when(collaboratorService).validateCollaborator("GGID123", "<EMAIL>");

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> collaboratorService.saveCollaborator(collaboratorRequestDTO));
    }*/

    @Test
    void testSaveCollaborator_ProjectNotFoundForOpp0() {
        // Given
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("OPPORTUNITY_0");
        when(collaboratorRequestDTO.getProjectName()).thenReturn("Project1");
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("A1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(messageSourceUtil.getMessageWithObject(eq("collaborator.is-opp-0"), anyString())).thenReturn("Collaborator is OPP0");

        // When & Then
        Exception exception = assertThrows(IllegalStateException.class, () ->
                collaboratorService.saveCollaborator(collaboratorRequestDTO));
        assertEquals("Collaborator is OPP0", exception.getMessage());
    }

    @Test
    void testSaveCollaborator_ProjectNameRequiredForFirmOrShadow() {
        // Given
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("FIRM_PROJECT");
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("A1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(collaboratorRequestDTO.getProjectName()).thenReturn(null);
        when(messageSourceUtil.getMessageWithObject(eq("validation.project-name-required"), anyString())).thenReturn("Project is required");

        // When & Then
        Exception exception = assertThrows(IllegalStateException.class, () -> collaboratorService.saveCollaborator(collaboratorRequestDTO));
        assertEquals("Project is required", exception.getMessage());
    }

    @Test
    void testSaveCollaborator_IncoherentLocalAndGlobalGrades() {
        // Given
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("B1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("OPPORTUNITY_0");
        when(messageSourceUtil.getMessage("local-grade.incoherent")).thenReturn("Local grade is incoherent");

        // When & Then
        Exception exception = assertThrows(IllegalStateException.class, () -> collaboratorService.saveCollaborator(collaboratorRequestDTO));
        assertEquals("Local grade is incoherent", exception.getMessage());
    }

    @Test
    void testSaveCollaborator_TrialPeriodHandling() {
        // Given
        when(collaboratorRequestDTO.getTrialPeriodStatus()).thenReturn("IN_PROGRESS");
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("A1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("FIRM_PROJECT");
        when(collaboratorRequestDTO.getProjectName()).thenReturn("Project Name");
        when(collaboratorRequestDTO.getComment()).thenReturn("Some words");
        doNothing().when(trialPeriodService).handleTrialPeriod(
                any(Collaborator.class), any(TrialPeriodStatus.class),
                anyBoolean(), anyBoolean(), anyString()
        );

        // When
        collaboratorService.saveCollaborator(collaboratorRequestDTO);

        // Then
        verify(trialPeriodService).handleTrialPeriod(
                any(Collaborator.class), any(TrialPeriodStatus.class),
                anyBoolean(), anyBoolean(), anyString()
        );
    }

    @Disabled("trialPeriod is null")
    void testSaveCollaborator_ConfirmTrialPeriodOnTenureDate() {
        // Given
        TrialPeriod trialPeriod = new TrialPeriod();
        // Initialisez le statut de TrialPeriod si nécessaire
        trialPeriod.setStatus(TrialPeriodStatus.IN_PROGRESS); // ou toute valeur par défaut

        Collaborator collaborator = mock(Collaborator.class);
        when(collaborator.getTrialPeriod()).thenReturn(trialPeriod);

        // Mocks pour CollaboratorRequestDTO
        when(collaboratorRepository.saveAndFlush(any(Collaborator.class))).thenReturn(collaborator);
        when(collaboratorRequestDTO.getTenureDate()).thenReturn(LocalDate.now());
        when(collaboratorRequestDTO.getGlobalGrade()).thenReturn("A");
        when(collaboratorRequestDTO.getLocalGrade()).thenReturn("A1");
        when(collaboratorRequestDTO.getStatus()).thenReturn("EMPLOYEE");
        when(collaboratorRequestDTO.getAssignmentStatus()).thenReturn("FIRM_PROJECT");
        when(collaboratorRequestDTO.getTrialPeriodStatus()).thenReturn("IN_PROGRESS");
        when(collaboratorRequestDTO.getProjectName()).thenReturn("Project Name");

        doNothing().when(trialPeriodService).handleTrialPeriod(
                any(Collaborator.class), any(TrialPeriodStatus.class),
                anyBoolean(), anyBoolean(), anyString()
        );

        // When
        collaboratorService.saveCollaborator(collaboratorRequestDTO);

        // Then
        verify(trialPeriodRepository).save(any(TrialPeriod.class));
        assertEquals(TrialPeriodStatus.CONFIRMED, collaborator.getTrialPeriod().getStatus());
    }
}