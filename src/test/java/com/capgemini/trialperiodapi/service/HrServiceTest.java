package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.Admin;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.repository.HrRepository;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.security.Principal;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HrServiceTest {

    @InjectMocks
    HrService hrService;

    @Mock
    HrRepository hrRepository;

    @Mock
    UserMapper userMapper;

    @Mock
    MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Mock
    MessageSourceUtil messageSourceUtil;

    @Mock
    private IEmailService emailService;

    @Mock
    MacroPeopleUnitService macroPeopleUnitService;

    @Mock
    UserService userService;

    @Mock
    PasswordEncoder passwordEncoder;

    @Test
    void hrService_getHrByEmail_returnHr() {
        // Given
        String email = "<EMAIL>";
        Hr hr = mock(Hr.class);
        hr.setEmail(email);

        when(hrRepository.findHrByEmail(email)).thenReturn(Optional.of(hr));

        // When
        AppUser returnHr = hrService.getHrByEmail(email);

        // Then
        assertEquals(hr, returnHr);
    }

    @Test
    void hrService_getHrByEmail_HrNotFound() {
        // Given
        String email = "<EMAIL>";
        Hr hr = mock(Hr.class);
        hr.setEmail(email);

        when(hrRepository.findHrByEmail(email)).thenReturn(Optional.empty());

        when(messageSourceUtil.getMessageWithObject("user.by-email.not-found", email)).thenReturn("Not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrService.getHrByEmail(email));
        assertEquals("Not found", exception.getMessage());
    }

    @Test
    void hrService_getHrByUsername_returnHr() {
        // Given
        String username = "<EMAIL>";
        Hr hr = mock(Hr.class);
        hr.setEmail(username);

        when(hrRepository.findHrByUsername(username)).thenReturn(Optional.of(hr));

        // When
        AppUser returnHr = hrService.getHrByUsername(username);

        // Then
        assertEquals(hr, returnHr);
    }

    @Test
    void hrService_getHrByUsername_HrNotFound() {
        // Given
        String username = "<EMAIL>";
        Hr hr = mock(Hr.class);
        hr.setEmail(username);

        when(hrRepository.findHrByUsername(username)).thenReturn(Optional.empty());

        when(messageSourceUtil.getMessageWithObject("user.by-username.not-found", username)).thenReturn("Not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrService.getHrByUsername(username));
        assertEquals("Not found", exception.getMessage());
    }

    @Test
    void hrService_getHrById_succeed() {
        // Given
        Long hrId = 1L;

        Hr hr = new Hr();
        hr.setId(hrId);

        when(hrRepository.findById(hrId)).thenReturn(Optional.of(hr));

        // When
        AppUser retrievedUser = hrService.getHrById(hrId);

        // Then
        assertNotNull(retrievedUser);
        assertEquals(hrId, retrievedUser.getId());
    }

    @Test
    void hrService_getHrById_ThrowsNotFoundException() {
        // Given
        Long hrId = 1L;

        Hr hr = new Hr();
        hr.setId(hrId);

        when(hrRepository.findById(hrId)).thenReturn(Optional.empty());
        when(messageSourceUtil.getMessageWithObject("user.by-id.not-found", hrId))
                .thenReturn("HR not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrService.getHrById(hrId));
        assertNotNull(exception);
        assertEquals("HR not found", exception.getMessage());
    }


    @Test
    void hrService_saveHr_savedSuccessfully() {
        // Given
        Set<String> macroPeopleUnitNames = Set.of("macro1", "macro2");

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrs(new HashSet<>())
                .build();
        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrs(new HashSet<>())
                .build();

        HrRequestDTO hrRequestDTO = HrRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(macroPeopleUnitNames)
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro2")).thenReturn(macro2);

        // When
        hrService.saveHr(hrRequestDTO, principal, "password");

        // Then
        verify(hrRepository).save(any(Hr.class));
        verify(macroPeopleUnitService).getMacroPeopleUnitByName("macro1");
        verify(macroPeopleUnitService).getMacroPeopleUnitByName("macro2");
        verify(macroPeopleUnitRepository).save(macro1);
        verify(macroPeopleUnitRepository).save(macro2);
        verify(emailService).sendWelcomeEmail(
                anyString(),
                anyString(),
                any(EmailTemplateName.class),
                anyString(),
                anyString(),
                any(Principal.class),
                anyString());
    }

    @Test
    void hrService_getHrDtoById_SucceedByAdmin() {
        // Given
        Long connectedUserId = 2L;

        Long hrId = 1L;
        Hr hr = Hr.builder().id(hrId).build();
        UserResponseDTO hrDTO = UserResponseDTO.builder().id(hrId).build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrRepository.findById(hrId)).thenReturn(Optional.of(hr));
        when(userMapper.toHrDTO(hr)).thenReturn(hrDTO);

        // When
        UserResponseDTO hrDTOById = hrService.getHrDtoById(hrId, principal);

        // Then
        assertEquals(hrId, hrDTOById.getId());
        verify(userMapper).toHrDTO(hr);
    }

    @Test
    void hrService_getHrDtoById_SucceedByHr() {
        // Given
        Long connectedUserId = 2L;

        Long hrId = 1L;
        Hr hr = Hr.builder().id(hrId).build();
        UserResponseDTO hrDTO = UserResponseDTO.builder().id(hrId).build();

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrs(Set.of(hr))
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro1);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hr connectedUser = Hr.builder()
                .id(connectedUserId)
                .role(Role.HR)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrRepository.findById(hrId)).thenReturn(Optional.of(hr));
        when(userMapper.toHrDTO(hr)).thenReturn(hrDTO);

        // When
        UserResponseDTO hrDTOById = hrService.getHrDtoById(hrId, principal);

        // Then
        assertEquals(hrId, hrDTOById.getId());
        verify(userMapper).toHrDTO(hr);
    }

    @Test
    void hrService_getHrDtoById_UnauthorizedByHrbp() {
        // Given
        Long connectedUserId = 2L;

        Long hrId = 1L;
        Hr hr = Hr.builder().id(hrId).build();

        MacroPeopleUnit macro = MacroPeopleUnit.builder()
                .name("macro")
                .hrs(Set.of())
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hrbp connectedUser = Hrbp.builder()
                .id(connectedUserId)
                .role(Role.HRBP)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrRepository.findById(hrId)).thenReturn(Optional.of(hr));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrService.getHrDtoById(hrId, principal));
    }

    @Test
    void hrService_getHrDtoById_UnauthorizedCausedByRoleAbsence() {
        // Given
        Long connectedUserId = 2L;
        Long hrId = 1L;

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrService.getHrDtoById(hrId, principal));
    }

    @Test
    void hrService_saveHr_ThrowsIllegalArgumentExceptionCausedByUsernameOrEmailAlreadyExists() {
        // Given
        HrRequestDTO hrRequestDTO = HrRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        doThrow(IllegalArgumentException.class).when(userService).validateUser("junit", "<EMAIL>");

        // When & Then
        assertThrows(IllegalArgumentException.class, () ->
                hrService.saveHr(hrRequestDTO, principal, "password"));
    }

    @Test
    void hrService_updateHr_UserUpdatedSuccessfully() {
        // Given
        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrs(new HashSet<>())
                .build();
        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrs(new HashSet<>())
                .build();

        Long hrId = 1L;
        Hr existHr = Hr.builder()
                .id(hrId)
                .macroPeopleUnits(Set.of(macro1, macro2))
                .build();

        HrRequestDTO hrRequestDTO = HrRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("macro1", "macro2"))
                .build();

        Admin connectedUser = Admin.builder()
                .id(1L)
                .username("oldUsername")
                .email("<EMAIL>")
                .firstname("old firstname")
                .lastname("old lastname")
                .role(Role.ADMIN)
                .build();
        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(1L)).thenReturn(connectedUser);
        when(hrRepository.findById(hrId)).thenReturn(Optional.of(existHr));
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro2")).thenReturn(macro2);

        // When
        hrService.updateHr(hrId, hrRequestDTO, principal);

        // Then
        verify(hrRepository).save(existHr);
    }

    @Test
    void hrService_updateHr_UnauthorizedCausedByRoleAbsence() {
        // Given
        Long connectedUserId = 2L;
        Long hrId = 1L;

        HrRequestDTO hrRequestDTO = HrRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(new HashSet<>())
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrService.updateHr(hrId, hrRequestDTO, principal));
    }

    @Test
    void HrService_updateHr_ThrowsIllegalStateExceptionCausedByMacroPeopleUnitHavingNoHr() {
        // Given
        Long connectedUserId = 1L;
        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrs(new HashSet<>())
                .build();

        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrs(new HashSet<>())
                .build();

        Long hrId = 2L;
        Hr existHr = Hr.builder()
                .id(hrId)
                .macroPeopleUnits(Set.of(macro2))
                .build();

        macro2.getHrs().add(existHr);

        HrRequestDTO userRequestDTO = HrRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("macro1"))
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrRepository.findById(hrId)).thenReturn(Optional.of(existHr));
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);

        // When & Then
        assertThrows(IllegalStateException.class, () ->
                hrService.updateHr(hrId, userRequestDTO, principal));
    }

    @Test
    void hrService_getAllHrs_returnListOfHrs() {
        // Given
        String keyword = "test";

        Admin connectedUser = Admin.builder()
                .id(1L)
                .username("oldUsername")
                .email("<EMAIL>")
                .firstname("old firstname")
                .lastname("old lastname")
                .role(Role.ADMIN)
                .build();

        Hr user1 = new Hr();
        user1.setId(1L);

        Hr user2 = new Hr();
        user2.setId(2L);

        UserResponseDTO userDTO1 = UserResponseDTO.builder()
                .id(1L)
                .build();

        UserResponseDTO userDTO2 = UserResponseDTO.builder()
                .id(2L)
                .build();

        List<Hr> userList = List.of(user1, user2);

        Pageable pageable = PageRequest.of(0, 10);
        Page<Hr> userPage = new PageImpl<>(userList, pageable, userList.size());

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(1L)).thenReturn(connectedUser);

        when(hrRepository.findAll((Specification<Hr>) any(), any(Pageable.class)))
                .thenReturn(userPage);
        when(userMapper.toHrDTO(user1)).thenReturn(userDTO1);
        when(userMapper.toHrDTO(user2)).thenReturn(userDTO2);

        // When
        Page<UserResponseDTO> resultList = hrService.getAllHrs(keyword, pageable, principal);

        // Then
        assertEquals(2, resultList.getTotalElements());
        verify(hrRepository).findAll((Specification<Hr>) any(), any(Pageable.class));
        verify(userMapper).toHrDTO(user1);
        verify(userMapper).toHrDTO(user2);
    }

    @Test
    void hrService_searchHrsByKeyword_ReturnHr() {
        // Given
        String keyword = "ab";
        Long connectedUserId = 2L;

        Long hrId = 1L;
        Hr hr = Hr.builder()
                .id(hrId)
                .firstname("abcde")
                .build();
        UserResponseDTO hrDTO = UserResponseDTO.builder()
                .id(hrId)
                .firstname("abcde")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        when(hrRepository.findHrsByKeyword(keyword)).thenReturn(List.of(hr));
        when(userMapper.toHrDTO(hr)).thenReturn(hrDTO);

        // When
        List<UserResponseDTO> results = hrService.searchHrsByKeyword(keyword, principal);

        // Then
        assertThat(results.contains(hrDTO));
        assertEquals(1, results.size());
    }

    @Test
    void hrService_searchHrsByKeyword_NoResult() {
        // Given
        String keyword = "xyz";

        Long hrId = 1L;
        UserResponseDTO hrDTO = UserResponseDTO.builder()
                .id(hrId)
                .firstname("abcde")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(hrRepository.findHrsByKeyword(keyword)).thenReturn(List.of());

        // When
        List<UserResponseDTO> results = hrService.searchHrsByKeyword(keyword, principal);

        // Then
        assertThat(results.contains(hrDTO));
        assertEquals(0, results.size());
    }

    @Test
    void hrService_deleteHrByUsername_HrDeletedSuccessfully() {
        // Given
        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        String username1 = "username1";
        String username2 = "username2";

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .build();

        Hr hr1 = Hr.builder()
                .id(1L)
                .username(username1)
                .macroPeopleUnits(Set.of(macro1))
                .build();
        Hr hr2 = Hr.builder()
                .id(2L)
                .username(username2)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        macro1.setHrs(new HashSet<>(Set.of(hr1, hr2)));

        when(hrRepository.findHrByUsername(username1)).thenReturn(Optional.of(hr1));

        // When
        hrService.deleteHrByUsername(username1, principal, "password");

        // Then
        hrRepository.findHrByUsername(username1);
        verify(hrRepository).delete(hr1);
        verify(emailService).sendAccountDeletedEmail(eq(hr1.getEmail()), eq(hr1.getFirstname()), eq(principal), anyString());
    }

    @Test
    void hrService_deleteHrByUsername_HrNotFound() {
        // Given
        Principal principal = mock(Principal.class);

        String username = "username";

        when(hrRepository.findHrByUsername(username)).thenReturn(Optional.empty());
        when(messageSourceUtil.getMessageWithObject("user.by-username.not-found",
                username)).thenReturn("HR not found by username");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrService.deleteHrByUsername(username, principal, "password"));
        assertEquals("HR not found by username", exception.getMessage());
    }

    @Test
    void hrService_deleteHrByUsername_ThrowsIllegalStateExceptionCausedBySoleHrForMacroPU() {
        // Given
        String username = "username";

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro2")
                .build();

        Hr hr = Hr.builder()
                .username(username)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        macro1.setHrs(new HashSet<>(Set.of(hr)));

        when(hrRepository.findHrByUsername(username)).thenReturn(Optional.of(hr));
        when(messageSourceUtil.getMessageWithObject("macro-people-unit.cannot-delete-the-only-hr-for-macro-pu",
                macro1.getName())).thenReturn("Cannot delete HR");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                hrService.deleteHrByUsername(username, principal, "password"));
        assertEquals("Cannot delete HR", exception.getMessage());
    }
}