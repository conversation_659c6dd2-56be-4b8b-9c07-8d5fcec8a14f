package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.HrbpRequestDTO;
import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.Admin;
import com.capgemini.trialperiodapi.model.Hr;
import com.capgemini.trialperiodapi.model.Hrbp;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.repository.HrbpRepository;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.security.Principal;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class HrbpServiceTest {

    @InjectMocks
    private HrbpService hrbpService;

    @Mock
    private HrbpRepository hrbpRepository;

    @Mock
    private UserMapper userMapper;

    @Mock
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Mock
    private IMacroPeopleUnitService macroPeopleUnitService;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private IUserService userService;

    @Mock
    private IEmailService emailService;

    @Mock
    PasswordEncoder passwordEncoder;

    @Test
    void HrbpService_getHrbpByEmail_returnHrbp() {
        // Given
        String email = "<EMAIL>";
        Hrbp hrbp = mock(Hrbp.class);
        hrbp.setEmail(email);

        when(hrbpRepository.findHrbpByEmail(email)).thenReturn(Optional.of(hrbp));

        // When
        AppUser returnHrbp = hrbpService.getHrbpByEmail(email);

        // Then
        assertEquals(hrbp, returnHrbp);
    }

    @Test
    void HrbpService_getHrbpByEmail_HrbpNotFound() {
        // Given
        String email = "<EMAIL>";
        Hrbp hrbp = mock(Hrbp.class);
        hrbp.setEmail(email);

        when(hrbpRepository.findHrbpByEmail(email)).thenReturn(Optional.empty());

        when(messageSourceUtil.getMessageWithObject("user.by-email.not-found", email)).thenReturn("Not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrbpService.getHrbpByEmail(email));
        assertEquals("Not found", exception.getMessage());
    }

    @Test
    void HrbpService_getHrbpByUsername_returnHrbp() {
        // Given
        String username = "<EMAIL>";
        Hrbp hrbp = mock(Hrbp.class);
        hrbp.setEmail(username);

        when(hrbpRepository.findHrbpByUsername(username)).thenReturn(Optional.of(hrbp));

        // When
        AppUser returnHrbp = hrbpService.getHrbpByUsername(username);

        // Then
        assertEquals(hrbp, returnHrbp);
    }

    @Test
    void HrbpService_getHrbpByUsername_HrbpNotFound() {
        // Given
        String username = "<EMAIL>";
        Hrbp hrbp = mock(Hrbp.class);
        hrbp.setEmail(username);

        when(hrbpRepository.findHrbpByUsername(username)).thenReturn(Optional.empty());

        when(messageSourceUtil.getMessageWithObject("user.by-username.not-found", username)).thenReturn("Not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrbpService.getHrbpByUsername(username));
        assertEquals("Not found", exception.getMessage());
    }

    @Test
    void HrbpService_getHrbpById_succeed() {
        // Given
        Long hrbpId = 1L;

        Hrbp hrbp = new Hrbp();
        hrbp.setId(hrbpId);

        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));

        // When
        AppUser retrievedUser = hrbpService.getHrbpById(hrbpId);

        // Then
        assertNotNull(retrievedUser);
        assertEquals(hrbpId, retrievedUser.getId());
    }

    @Test
    void HrbpService_getHrbpById_ThrowsNotFoundException() {
        // Given
        Long hrbpId = 1L;

        Hrbp hrbp = new Hrbp();
        hrbp.setId(hrbpId);

        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.empty());
        when(messageSourceUtil.getMessageWithObject("user.by-id.not-found", hrbpId))
                .thenReturn("HRBP not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrbpService.getHrbpById(hrbpId));
        assertNotNull(exception);
        assertEquals("HRBP not found", exception.getMessage());
    }


    @Test
    void HrbpService_saveHrbp_savedSuccessfully() {
        // Given
        Set<String> macroPeopleUnitNames = Set.of("macro1", "macro2");

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(new HashSet<>())
                .build();
        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrbps(new HashSet<>())
                .build();

        HrbpRequestDTO hrbpRequestDTO = HrbpRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(macroPeopleUnitNames)
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro2")).thenReturn(macro2);

        // When
        hrbpService.saveHrbp(hrbpRequestDTO, principal, "password");

        // Then
        verify(hrbpRepository).save(any(Hrbp.class));
        verify(macroPeopleUnitService).getMacroPeopleUnitByName("macro1");
        verify(macroPeopleUnitService).getMacroPeopleUnitByName("macro2");
        verify(macroPeopleUnitRepository).save(macro1);
        verify(macroPeopleUnitRepository).save(macro2);
        verify(emailService).sendWelcomeEmail(
                anyString(),
                anyString(),
                any(EmailTemplateName.class),
                anyString(),
                anyString(),
                any(Principal.class),
                anyString());
    }

    @Test
    void HrbpService_getHrbpDTOById_SucceedByAdmin() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder().id(hrbpId).build();
        UserResponseDTO hrbpDTO = UserResponseDTO.builder().id(hrbpId).build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));
        when(userMapper.toHrbpDTO(hrbp)).thenReturn(hrbpDTO);

        // When
        UserResponseDTO hrbpDTOById = hrbpService.getHrbpDTOById(hrbpId, principal);

        // Then
        assertEquals(hrbpId, hrbpDTOById.getId());
        verify(userMapper).toHrbpDTO(hrbp);
    }

    @Test
    void HrbpService_getHrbpDTOById_SucceedByHrbp() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder().id(hrbpId).build();
        UserResponseDTO hrbpDTO = UserResponseDTO.builder().id(hrbpId).build();

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(Set.of(hrbp))
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro1);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hrbp connectedUser = Hrbp.builder()
                .id(connectedUserId)
                .role(Role.HRBP)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));
        when(userMapper.toHrbpDTO(hrbp)).thenReturn(hrbpDTO);

        // When
        UserResponseDTO hrbpDTOById = hrbpService.getHrbpDTOById(hrbpId, principal);

        // Then
        assertEquals(hrbpId, hrbpDTOById.getId());
        verify(userMapper).toHrbpDTO(hrbp);
    }

    @Test
    void HrbpService_getHrbpDTOById_UnauthorizedByHrbp() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder().id(hrbpId).build();

        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrbps(Set.of())
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro2);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hrbp connectedUser = Hrbp.builder()
                .id(connectedUserId)
                .role(Role.HRBP)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrbpService.getHrbpDTOById(hrbpId, principal));
    }

    @Test
    void HrbpService_getHrbpDTOById_SucceedByHr() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder().id(hrbpId).build();
        UserResponseDTO hrbpDTO = UserResponseDTO.builder().id(hrbpId).build();

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(Set.of(hrbp))
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro1);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hr connectedUser = Hr.builder()
                .id(connectedUserId)
                .role(Role.HR)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));
        when(userMapper.toHrbpDTO(hrbp)).thenReturn(hrbpDTO);

        // When
        UserResponseDTO hrbpDTOById = hrbpService.getHrbpDTOById(hrbpId, principal);

        // Then
        assertEquals(hrbpId, hrbpDTOById.getId());
        verify(userMapper).toHrbpDTO(hrbp);
    }

    @Test
    void HrbpService_getHrbpDTOById_UnauthorizedByHr() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder().id(hrbpId).build();

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(Set.of())
                .build();

        Set<MacroPeopleUnit> macroPeopleUnits = Set.of(macro1);

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hr connectedUser = Hr.builder()
                .id(connectedUserId)
                .role(Role.HR)
                .macroPeopleUnits(macroPeopleUnits)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(hrbp));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrbpService.getHrbpDTOById(hrbpId, principal));
    }

    @Test
    void HrbpService_getHrbpDTOById_UnauthorizedCausedByNoRole() {
        // Given
        Long connectedUserId = 2L;

        Long hrbpId = 1L;

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(Set.of())
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        // When & Then
        assertThrows(UnauthorizedException.class, () -> hrbpService.getHrbpDTOById(hrbpId, principal));
    }

    @Test
    void HrbpService_saveHrbp_ThrowsIllegalArgumentExceptionCausedByUsernameOrEmailAlreadyExists() {
        // Given
        HrbpRequestDTO hrbpRequestDTO = HrbpRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        doThrow(IllegalArgumentException.class).when(userService).validateUser("junit", "<EMAIL>");

        // When & Then
        assertThrows(IllegalArgumentException.class, () ->
                hrbpService.saveHrbp(hrbpRequestDTO, principal, "password"));
    }

    @Test
    void HrbpService_updateHrbp_UserUpdatedSuccessfully() {
        // Given
        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(new HashSet<>())
                .build();
        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrbps(new HashSet<>())
                .build();

        Long hrbpId = 1L;
        Hrbp existHrbp = Hrbp.builder()
                .id(hrbpId)
                .macroPeopleUnits(Set.of(macro1, macro2))
                .build();

        HrbpRequestDTO userRequestDTO = HrbpRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("macro1", "macro2"))
                .build();

        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(existHrbp));
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro2")).thenReturn(macro2);

        // When
        hrbpService.updateHrbp(hrbpId, userRequestDTO);

        // Then
        verify(hrbpRepository).save(existHrbp);
    }

    @Test
    void HrbpService_updateHrbp_ThrowsIllegalStateExceptionCausedByMacroPeopleUnitHavingNoHrbp() {
        // Given
        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .hrbps(new HashSet<>())
                .build();

        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .hrbps(new HashSet<>())
                .build();

        Long hrbpId = 1L;
        Hrbp existHrbp = Hrbp.builder()
                .id(hrbpId)
                .macroPeopleUnits(Set.of(macro2))
                .build();

        macro2.getHrbps().add(existHrbp);

        HrbpRequestDTO userRequestDTO = HrbpRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .username("junit")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("macro1"))
                .build();

        when(hrbpRepository.findHrbpById(hrbpId)).thenReturn(Optional.of(existHrbp));
        when(macroPeopleUnitService.getMacroPeopleUnitByName("macro1")).thenReturn(macro1);

        // When & Then
        assertThrows(IllegalStateException.class, () ->
                hrbpService.updateHrbp(hrbpId, userRequestDTO));
    }

    @Test
    void HrbpService_getAllHrbps_returnListOfHrs() {
        // Given
        String keyword = "test";

        Admin connectedUser = Admin.builder()
                .id(1L)
                .username("oldUsername")
                .email("<EMAIL>")
                .firstname("old firstname")
                .lastname("old lastname")
                .role(Role.ADMIN)
                .build();

        Hrbp user1 = new Hrbp();
        user1.setId(1L);

        Hrbp user2 = new Hrbp();
        user2.setId(2L);

        UserResponseDTO userDTO1 = UserResponseDTO.builder()
                .id(1L)
                .build();

        UserResponseDTO userDTO2 = UserResponseDTO.builder()
                .id(2L)
                .build();

        List<Hrbp> userList = List.of(user1, user2);

        Pageable pageable = PageRequest.of(0, 10);
        Page<Hrbp> userPage = new PageImpl<>(userList, pageable, userList.size());

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(1L)).thenReturn(connectedUser);

        when(hrbpRepository.findAll((Specification<Hrbp>) any(), any(Pageable.class)))
                .thenReturn(userPage);
        when(userMapper.toHrbpDTO(user1)).thenReturn(userDTO1);
        when(userMapper.toHrbpDTO(user2)).thenReturn(userDTO2);

        // When
        Page<UserResponseDTO> resultList = hrbpService.getAllHrbps(keyword, pageable, principal);

        // Then
        assertEquals(2, resultList.getTotalElements());
        verify(hrbpRepository).findAll((Specification<Hrbp>) any(), any(Pageable.class));
        verify(userMapper).toHrbpDTO(user1);
        verify(userMapper).toHrbpDTO(user2);
    }

    @Test
    void HrbpService_searchHrbpsByKeyword_ReturnHrbp() {
        // Given
        String keyword = "ab";
        Long connectedUserId = 2L;

        Long hrbpId = 1L;
        Hrbp hrbp = Hrbp.builder()
                .id(hrbpId)
                .firstname("abcde")
                .build();
        UserResponseDTO hrbpDTO = UserResponseDTO.builder()
                .id(hrbpId)
                .firstname("abcde")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        when(hrbpRepository.findHrbpsByKeyword(keyword)).thenReturn(List.of(hrbp));
        when(userMapper.toHrbpDTO(hrbp)).thenReturn(hrbpDTO);

        // When
        List<UserResponseDTO> results = hrbpService.searchHrbpsByKeyword(keyword, principal);

        // Then
        assertThat(results.contains(hrbpDTO));
        assertEquals(1, results.size());
    }

    @Test
    void HrbpService_searchHrbpsByKeyword_NoResult() {
        // Given
        String keyword = "xyz";

        Long hrbpId = 1L;
        UserResponseDTO hrbpDTO = UserResponseDTO.builder()
                .id(hrbpId)
                .firstname("abcde")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(hrbpRepository.findHrbpsByKeyword(keyword)).thenReturn(List.of());

        // When
        List<UserResponseDTO> results = hrbpService.searchHrbpsByKeyword(keyword, principal);

        // Then
        assertThat(results.contains(hrbpDTO));
        assertEquals(0, results.size());
    }

    @Test
    void HrbpService_deleteHrbpByUsername_HrbpDeletedSuccessfully() {
        // Given
        Principal principal = mock(Principal.class);

        String username1 = "username1";
        String username2 = "username2";

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .build();

        Hrbp hrbp1 = Hrbp.builder()
                .username(username1)
                .macroPeopleUnits(Set.of(macro1))
                .build();
        Hrbp hrbp2 = Hrbp.builder()
                .username(username2)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        macro1.setHrbps(new HashSet<>(Set.of(hrbp1, hrbp2)));

        when(hrbpRepository.findHrbpByUsername(username1)).thenReturn(Optional.of(hrbp1));

        // When
        hrbpService.deleteHrbpByUsername(username1, "password", principal);

        // Then
        verify(hrbpRepository).delete(hrbp1);
        verify(emailService).sendAccountDeletedEmail(eq(hrbp1.getEmail()), eq(hrbp1.getFirstname()), eq(principal), anyString());
    }

    @Test
    void HrbpService_deleteHrbpByUsername_HrbpNotFound() {
        // Given
        Principal principal = mock(Principal.class);

        String username = "username";

        when(hrbpRepository.findHrbpByUsername(username)).thenReturn(Optional.empty());
        when(messageSourceUtil.getMessageWithObject("user.by-username.not-found",
                username)).thenReturn("HRBP not found by username");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                hrbpService.deleteHrbpByUsername(username, "password", principal));
        assertEquals("HRBP not found by username", exception.getMessage());
    }

    @Test
    void HrbpService_deleteHrbpByUsername_ThrowsIllegalStateExceptionCausedBySoleHrbpForMacroPU() {
        // Given
        Principal principal = mock(Principal.class);

        String username = "username";

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro2")
                .build();

        Hrbp hrbp = Hrbp.builder()
                .username(username)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        macro1.setHrbps(new HashSet<>(Set.of(hrbp)));

        when(hrbpRepository.findHrbpByUsername(username)).thenReturn(Optional.of(hrbp));
        when(messageSourceUtil.getMessageWithObject("macro-people-unit.cannot-delete-the-only-hrbp-for-macro-pu",
                macro1.getName())).thenReturn("Cannot delete HRBP");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                hrbpService.deleteHrbpByUsername(username, "password", principal));
        assertEquals("Cannot delete HRBP", exception.getMessage());
    }
}