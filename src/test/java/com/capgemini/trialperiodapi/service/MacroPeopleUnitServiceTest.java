package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.MacroPeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.*;
import com.capgemini.trialperiodapi.exception.UnauthorizedException;
import com.capgemini.trialperiodapi.mapper.*;
import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.repository.*;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class MacroPeopleUnitServiceTest {

    @Spy
    @InjectMocks
    private MacroPeopleUnitService macroPeopleUnitService;

    @Mock
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private PeopleUnitService peopleUnitService;

    @Mock
    private PeopleUnitRepository peopleUnitRepository;

    @Mock
    private HrRepository hrRepository;

    @Mock
    private PeopleUnitManagerMapper peopleUnitManagerMapper;

    @Mock
    private ResourceManagerMapper resourceManagerMapper;

    @Mock
    private PeopleUnitMapper peopleUnitMapper;

    @Mock
    private MacroPeopleUnitMapper macroPeopleUnitMapper;

    @Mock
    private UserMapper userMapper;

    @Mock
    private UserService userService;

    @Mock
    private HrbpRepository hrbpRepository;

    @Mock
    private PeopleUnitManagerRepository pumRepository;

    @Mock
    private ResourceManagerRepository resourceManagerRepository;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }


    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByName_returnsMacroPeopleUnitByName() {
        // Given
        String macroPeopleUnitName = "macroTest";
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName(macroPeopleUnitName);

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.of(macroPeopleUnit));

        // When
        MacroPeopleUnit expectedMacroPeopleUnit = macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName);

        // Then
        assertNotNull(expectedMacroPeopleUnit);
        assertEquals(macroPeopleUnitName, expectedMacroPeopleUnit.getName());
        verify(macroPeopleUnitRepository).findMacroPeopleUnitByName(macroPeopleUnitName);
    }

    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByNameWithPrincipal_returnsMacroPeopleUnitByName() {
        // Given
        Long connectedUserId = 1L;
        String macroName = "macro1";

        MacroPeopleUnit macro = MacroPeopleUnit.builder()
                .name(macroName)
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Admin connectedUser = Admin.builder()
                .id(connectedUserId)
                .role(Role.ADMIN)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macro.getName())).thenReturn(Optional.of(macro));

        // When
        MacroPeopleUnit macroPeopleUnitByName = macroPeopleUnitService.getMacroPeopleUnitByName(macro.getName(), principal);

        // Then
        assertEquals(macroName, macroPeopleUnitByName.getName());
    }

    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByNameWithPrincipal_UnauthorizedByHrbp() {
        // Given
        Long connectedUserId = 1L;

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .build();

        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hrbp connectedUser = Hrbp.builder()
                .id(connectedUserId)
                .role(Role.HRBP)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macro2.getName())).thenReturn(Optional.of(macro2));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> macroPeopleUnitService.getMacroPeopleUnitByName(macro2.getName(), principal));
    }

    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByNameWithPrincipal_UnauthorizedCausedByRoleAbsence() {
        // Given
        Long connectedUserId = 1L;

        MacroPeopleUnit macro = MacroPeopleUnit.builder()
                .name("macro")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);

        // When & Then
        assertThrows(UnauthorizedException.class, () -> macroPeopleUnitService.getMacroPeopleUnitByName(macro.getName(), principal));
    }

    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByNameWithPrincipal_UnauthorizedByHr() {
        // Given
        Long connectedUserId = 2L;

        MacroPeopleUnit macro1 = MacroPeopleUnit.builder()
                .name("macro1")
                .build();

        MacroPeopleUnit macro2 = MacroPeopleUnit.builder()
                .name("macro2")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        Hr connectedUser = Hr.builder()
                .id(connectedUserId)
                .role(Role.HR)
                .macroPeopleUnits(Set.of(macro1))
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(userService.getUserById(connectedUserId)).thenReturn(connectedUser);
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macro2.getName())).thenReturn(Optional.of(macro2));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> macroPeopleUnitService.getMacroPeopleUnitByName(macro2.getName(), principal));
    }

    @Test
    void MacroPeopleUnitService_getMacroPeopleUnitByName_throwIllegalArgumentExceptionCausedByMacroPeopleUnitNotFound() {
        // Given
        String macroPeopleUnitName = "macroTest";
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> macroPeopleUnitService.getMacroPeopleUnitByName(macroPeopleUnitName));
    }

    @Test
    void MacroPeopleUnitService_saveMacroPeopleUnit_SavedSuccessfully() {
        // Given
        String macroPeopleUnitName = "macroPeopleUnit";

        MacroPeopleUnitRequestDTO request = MacroPeopleUnitRequestDTO.builder()
                .name(macroPeopleUnitName)
                .peopleUnitNames(new HashSet<>())
                .build();
        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName)
                .build();

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.empty());
        when(macroPeopleUnitMapper.toMacroPeopleUnitEntity(request)).thenReturn(macroPeopleUnit);

        // When
        macroPeopleUnitService.saveMacroPeopleUnit(request);

        // Then
        verify(macroPeopleUnitRepository).save(any(MacroPeopleUnit.class));
    }

    @Test
    void MacroPeopleUnitService_saveMacroPeopleUnit_ThrowsIllegalArgumentExceptionCausedByAlreadyAssigned() {
        // Given
        String newMacroPeopleUnitName = "newMacroPeopleUnitName";
        String assignedMacroPeopleUnitName = "assignedMacroPeopleUnitName";
        String peopleUnitName = "peopleUnitName";

        MacroPeopleUnit assignedMacroPeopleUnit = MacroPeopleUnit.builder()
                .name(assignedMacroPeopleUnitName)
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name(peopleUnitName)
                .macroPeopleUnit(assignedMacroPeopleUnit)
                .build();

        MacroPeopleUnit newMacroPeopleUnit = MacroPeopleUnit.builder()
                .name(newMacroPeopleUnitName)
                .peopleUnits(Set.of(peopleUnit))
                .build();

        MacroPeopleUnitRequestDTO request = MacroPeopleUnitRequestDTO.builder()
                .name(newMacroPeopleUnitName)
                .peopleUnitNames(Set.of(peopleUnitName))
                .build();

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(newMacroPeopleUnitName))
                .thenReturn(Optional.empty());
        when(macroPeopleUnitMapper.toMacroPeopleUnitEntity(request)).thenReturn(newMacroPeopleUnit);
        when(peopleUnitService.getPeopleUnitByName(peopleUnitName)).thenReturn(peopleUnit);

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> macroPeopleUnitService.saveMacroPeopleUnit(request));
    }

    @Test
    void MacroPeopleUnitService_saveMacroPeopleUnit_ThrowsException_NameExists() {
        // Given
        String macroPeopleUnitName = "macroPeopleUnit";
        MacroPeopleUnitRequestDTO request = new MacroPeopleUnitRequestDTO();
        request.setName(macroPeopleUnitName);

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.of(new MacroPeopleUnit()));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> macroPeopleUnitService.saveMacroPeopleUnit(request));
    }

    @Test
    void MacroPeopleUnitService_updateMacroPeopleUnit_UpdatedSuccessfully() {
        // Given
        String macroPeopleUnitName = "Macro PU Name";
        MacroPeopleUnitRequestDTO request = new MacroPeopleUnitRequestDTO();
        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName).build();

        when(macroPeopleUnitMapper.toMacroPeopleUnitEntity(request)).thenReturn(macroPeopleUnit);
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.of(macroPeopleUnit));
        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(request.getName()))
                .thenReturn(Optional.empty());

        // When
        macroPeopleUnitService.updateMacroPeopleUnit(macroPeopleUnitName, request);

        // Then
        verify(macroPeopleUnitRepository).save(any(MacroPeopleUnit.class));
    }

    @Test
    void MacroPeopleUnitService_updateMacroPeopleUnit_ThrowsEntityNotFoundException() {
        // Given
        String macroPeopleUnitName = "Macro PU Name";
        MacroPeopleUnitRequestDTO request = new MacroPeopleUnitRequestDTO();

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName))
                .thenReturn(Optional.empty());
        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> macroPeopleUnitService.updateMacroPeopleUnit(macroPeopleUnitName, request));
    }

    @Test
    void MacroPeopleUnitService_updateMacroPeopleUnit_ThrowsIllegalArgumentExceptionCausedByAlreadyAssigned() {
        // Given
        String updateMacroPeopleUnitName = "updateMacroPeopleUnitName";
        String oldMacroPeopleUnitName = "oldMacroPeopleUnitName";
        String peopleUnitName = "peopleUnitName";

        MacroPeopleUnit assignedMacroPeopleUnit = MacroPeopleUnit.builder()
                .name(oldMacroPeopleUnitName)
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .name(peopleUnitName)
                .macroPeopleUnit(assignedMacroPeopleUnit)
                .build();

        MacroPeopleUnit updateMacroPeopleUnit = MacroPeopleUnit.builder()
                .name(updateMacroPeopleUnitName)
                .peopleUnits(Set.of(peopleUnit))
                .build();

        MacroPeopleUnitRequestDTO request = MacroPeopleUnitRequestDTO.builder()
                .name(updateMacroPeopleUnitName)
                .peopleUnitNames(Set.of(peopleUnitName))
                .build();

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(updateMacroPeopleUnitName))
                .thenReturn(Optional.of(updateMacroPeopleUnit));
        when(macroPeopleUnitMapper.toMacroPeopleUnitEntity(request)).thenReturn(updateMacroPeopleUnit);
        when(peopleUnitService.getPeopleUnitByName(peopleUnitName)).thenReturn(peopleUnit);

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> macroPeopleUnitService.updateMacroPeopleUnit(updateMacroPeopleUnit.getName(), request));
    }

    @Test
    void MacroPeopleUnitService_delete_DeletedSuccessfully() {
        // Given
        Long macroPeopleUnitId = 1L;
        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .id(macroPeopleUnitId)
                .peopleUnits(new HashSet<>())
                .build();

        when(macroPeopleUnitRepository.findById(macroPeopleUnitId))
                .thenReturn(Optional.of(macroPeopleUnit));

        // When
        macroPeopleUnitService.deleteMacroPeopleUnitById(macroPeopleUnitId);

        // Then
        verify(macroPeopleUnitRepository).save(any(MacroPeopleUnit.class));
    }

    @Test
    void MacroPeopleUnitService_getSpocsHrPumByMacroPeopleUnitName_SpocsReturned() {
        // Given
        String macroPeopleUnitName = "macro";
        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .name(macroPeopleUnitName)
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder().id(1L).build();
        PeopleUnitResponseDTO peopleUnitDTO = PeopleUnitResponseDTO.builder().id(1L).build();
        List<PeopleUnit> peopleUnits = List.of(peopleUnit);
        List<PeopleUnitResponseDTO> peopleUnitDTOs = List.of(peopleUnitDTO);

        String hrUsername = "hrUsername";
        Hr hr = Hr.builder().username(hrUsername).build();
        UserResponseDTO hrDTO = UserResponseDTO.builder().username(hrUsername).build();
        List<Hr> hrs = List.of(hr);
        List<UserResponseDTO> hrDTOs = List.of(hrDTO);

        String hrbpUsername = "hrbpUsername";
        Hrbp hrbp = Hrbp.builder().username(hrbpUsername).build();
        UserResponseDTO hrbpDTO = UserResponseDTO.builder().username(hrbpUsername).build();
        List<Hrbp> hrbps = List.of(hrbp);
        List<UserResponseDTO> hrbpDTOs = List.of(hrbpDTO);

        ResourceManager resourceManager = new ResourceManager();
        ResourceManagerResponseDTO resourceManagerDTO = ResourceManagerResponseDTO.builder().id(1L).build();
        List<ResourceManager> resourceManagers = List.of(resourceManager);
        List<ResourceManagerResponseDTO> resourceManagerDTOs = List.of(resourceManagerDTO);

        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        PeopleUnitManagerResponseDTO peopleUnitManagerDTO = PeopleUnitManagerResponseDTO.builder().id(1L).build();
        List<PeopleUnitManager> pums = List.of(peopleUnitManager);
        List<PeopleUnitManagerResponseDTO> pumDTOs = List.of(peopleUnitManagerDTO);

        when(macroPeopleUnitRepository.findMacroPeopleUnitByName(macroPeopleUnitName)).thenReturn(Optional.of(macroPeopleUnit));
        when(peopleUnitRepository.findAllPeopleUnitByMacroPeopleUnitName(macroPeopleUnitName)).thenReturn(peopleUnits);
        when(hrRepository.findAllHrsByMacroPeopleUnitName(macroPeopleUnitName)).thenReturn(hrs);
        when(hrbpRepository.findAllHrbpsByMacroPeopleUnitName(macroPeopleUnitName)).thenReturn(hrbps);
        when(pumRepository.findAllPumsByMacroPeopleUnitName(macroPeopleUnitName)).thenReturn(pums);
        when(resourceManagerRepository.findResourceManagersByMacroPeopleUnitName(macroPeopleUnitName)).thenReturn(resourceManagers);

        when(peopleUnitMapper.toPeopleUnitDTO(peopleUnit)).thenReturn(peopleUnitDTO);
        when(userMapper.toHrDTO(hr)).thenReturn(hrDTO);
        when(userMapper.toHrbpDTO(hrbp)).thenReturn(hrbpDTO);
        when(peopleUnitManagerMapper.toPeopleUnitManagerDTO(peopleUnitManager)).thenReturn(peopleUnitManagerDTO);
        when(resourceManagerMapper.toResourceManagerDTO(resourceManager)).thenReturn(resourceManagerDTO);

        // When
        SpocsHrPumResponseDTO spocs = macroPeopleUnitService.getSpocsHrPumByMacroPeopleUnitName(macroPeopleUnitName);

        // Then
        assertEquals(macroPeopleUnitName, spocs.getMacroPeopleUnitName());
        assertEquals(peopleUnitDTOs, spocs.getPeopleUnits());
        assertEquals(hrDTOs, spocs.getHrs());
        assertEquals(hrbpDTOs, spocs.getHrbps());
        assertEquals(resourceManagerDTOs, spocs.getResourceManagers());
        assertEquals(pumDTOs, spocs.getPums());
    }
}