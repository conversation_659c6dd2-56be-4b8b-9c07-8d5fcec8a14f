package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.PeopleUnitManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.capgemini.trialperiodapi.mapper.PeopleUnitManagerMapper;
import com.capgemini.trialperiodapi.model.PeopleUnitManager;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.repository.PeopleUnitManagerRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

class PeopleUnitManagerServiceTest {

    @InjectMocks
    private PeopleUnitManagerService peopleUnitManagerService;

    @Mock
    private PeopleUnitManagerRepository peopleUnitManagerRepository;

    @Mock
    private PeopleUnitManagerMapper peopleUnitManagerMapper;

    @Mock
    private MacroPeopleUnitService macroPeopleUnitService;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Mock
    private IUserService userService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        this.peopleUnitManagerService = new PeopleUnitManagerService(
                macroPeopleUnitRepository,
                peopleUnitManagerMapper,
                macroPeopleUnitService,
                peopleUnitManagerRepository,
                messageSourceUtil,
                userService
        );
    }

    @Test
    void PeopleUnitManagerService_getPumDTOById_returnPumsOfGivenId() {
        // Given
        Long pumId = 2L;
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setId(pumId);

        PeopleUnitManagerResponseDTO peopleUnitManagerResponseDTO = PeopleUnitManagerResponseDTO.builder()
                .id(2L)
                .build();

        when(peopleUnitManagerRepository.findById(pumId))
                .thenReturn(Optional.of(peopleUnitManager));

        when(peopleUnitManagerMapper.toPeopleUnitManagerDTO(peopleUnitManager))
                .thenReturn(peopleUnitManagerResponseDTO);

        // When
        PeopleUnitManagerResponseDTO result = peopleUnitManagerService.getPumDTOById(pumId);

        // Then
        assertNotNull(result);
        assertEquals(pumId, result.getId());
        verify(peopleUnitManagerRepository).findById(pumId);
        verify(peopleUnitManagerMapper).toPeopleUnitManagerDTO(peopleUnitManager);
    }

    @Test
    void PeopleUnitManagerService_getPumDTOById_ThrowsException_IdNotfound() {
        // Given
        Long pumId = 2L;
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setId(pumId);

        when(peopleUnitManagerRepository.findById(pumId))
                .thenReturn(Optional.empty());

        // When
        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class,
                        () -> peopleUnitManagerService.getPumDTOById(pumId));
        //  Then
        assertEquals(messageSourceUtil.getMessageWithObject(
                "pum.by-id.not-found", pumId),
                exception.getMessage());
    }

    @Test
    void PeopleUnitManagerService_deletePumById_CalledDeleteSuccessfully() {
        // Given
        String pumEmail = "<EMAIL>";
        Long pumId = 1L;
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setId(pumId);
        peopleUnitManager.setEmail(pumEmail);

        when(peopleUnitManagerRepository.findPumByEmail(pumEmail))
                .thenReturn(Optional.of(peopleUnitManager));

        // When
        peopleUnitManagerService.deletePumByEmail(pumEmail);

        // Then
        verify(peopleUnitManagerRepository).deleteById(pumId);
    }

    @Test
    void PeopleUnitManagerService_deletePumById_ThrowsException_IdNotfound() {
        // Given
        Long pumId = 2L;
        String pumEmail = "<EMAIL>";
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setId(pumId);
        peopleUnitManager.setEmail(pumEmail);

        when(peopleUnitManagerRepository.findPumByEmail(pumEmail))
                .thenReturn(Optional.empty());

        // When & Then
        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class,
                        () -> peopleUnitManagerService.deletePumByEmail(pumEmail));
        assertEquals(messageSourceUtil.getMessageWithObject(
                "pum.by-id.not-found", pumId),
                exception.getMessage());
    }

    @Test
    void PeopleUnitManagerService_updatePum_CalledUpdatePumSuccessfully() {
        // Given
        Long pumId = 2L;
        PeopleUnitManager peopleUnitManager = new PeopleUnitManager();
        peopleUnitManager.setId(pumId);

        PeopleUnitManagerRequestDTO request = PeopleUnitManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .macroPeopleUnitNames(new HashSet<>())
                .build();

        when(peopleUnitManagerRepository.findById(pumId))
                .thenReturn(Optional.of(peopleUnitManager));

        when(peopleUnitManagerRepository.save(any(PeopleUnitManager.class)))
                .thenReturn(any(PeopleUnitManager.class));
        // When
        peopleUnitManagerService.updatePum(pumId, request);

        // Then
        verify(peopleUnitManagerRepository).save(any(PeopleUnitManager.class));
    }

    @Test
    void PeopleUnitManagerService_updatePum_ThrowsException_IdNotfound() {
        // Given
        Long pumId = 2L;

        PeopleUnitManagerRequestDTO request = PeopleUnitManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .macroPeopleUnitNames(new HashSet<>())
                .build();

        when(peopleUnitManagerRepository.findById(pumId))
                .thenReturn(Optional.empty());

        // When & Then
        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class,
                        () -> peopleUnitManagerService.updatePum(pumId, request));
        assertEquals(messageSourceUtil.getMessageWithObject(
                "pum.by-id.not-found", pumId),
                exception.getMessage());
    }

    @Test
    void PeopleUnitManagerService_updatePum_ThrowsException_EmailAlreadyExist() {
        // Given
        Long pumId = 2L;
        String existEmail = "<EMAIL>";

        PeopleUnitManager peopleUnitManagerToUpdate = new PeopleUnitManager();
        peopleUnitManagerToUpdate.setId(pumId);
        peopleUnitManagerToUpdate.setEmail("<EMAIL>");

        PeopleUnitManager existPeopleUnitManagerByEmail = new PeopleUnitManager();
        existPeopleUnitManagerByEmail.setId(3L);
        existPeopleUnitManagerByEmail.setEmail(existEmail);

        PeopleUnitManagerRequestDTO request = PeopleUnitManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email(existEmail)
                .macroPeopleUnitNames(new HashSet<>())
                .build();

        when(peopleUnitManagerRepository.findById(pumId))
                .thenReturn(Optional.of(peopleUnitManagerToUpdate));
        when(peopleUnitManagerRepository.findPumByEmail(request.getEmail()))
                .thenReturn(Optional.of(existPeopleUnitManagerByEmail));

        // When & Then
        IllegalArgumentException exception =
                assertThrows(IllegalArgumentException.class,
                        () -> peopleUnitManagerService.updatePum(pumId, request));

        assertEquals(messageSourceUtil.getMessageWithObject(
                "pum.by-email.already-exists", request.getEmail()),
                exception.getMessage());
    }
}