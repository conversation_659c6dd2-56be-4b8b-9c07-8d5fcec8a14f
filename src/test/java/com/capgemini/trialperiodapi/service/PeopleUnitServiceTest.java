package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.PeopleUnitRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.mapper.PeopleUnitMapper;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.repository.PeopleUnitRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class PeopleUnitServiceTest {

    @Spy
    @InjectMocks
    PeopleUnitService peopleUnitService;

    @Mock
    PeopleUnitRepository peopleUnitRepository;


    @Mock
    MacroPeopleUnitService macroPeopleUnitService;

    @Mock
    HrService hrService;

    @Mock
    UserService userService;

    @Mock
    MessageSourceUtil messageSourceUtil;

    @Mock
    PeopleUnitManagerService pumService;

    @Mock
    PeopleUnitMapper peopleUnitMapper;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void PeopleUnitService_getPeopleUnitByName_returnsPeopleUnitByName() {
        // Given
        String peopleUnitName = "PeopleUnit 1";
        PeopleUnit peopleUnit = new PeopleUnit();
        peopleUnit.setName(peopleUnitName);

        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.of(peopleUnit));

        // When
        var expectedPeopleUnit = peopleUnitService.getPeopleUnitByName(peopleUnitName);

        // Then
        assertNotNull(expectedPeopleUnit);
        assertEquals(peopleUnitName, expectedPeopleUnit.getName());
        verify(peopleUnitRepository).findPeopleUnitByName(peopleUnitName);
    }

    @Test
    void PeopleUnitService_getPeopleUnitByName_ThrowsEntityNotFoundExceptionCausedByPeopleUnitNotFound() {
        // Given
        String peopleUnitName = "PeopleUnit 1";

        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> peopleUnitService.getPeopleUnitByName(peopleUnitName));
    }

    @Disabled("Needs review : connectedUser is null")
    void PeopleUnitService_searchPeopleUnitNamesByKeyword_returnsPeopleUnitNamesByKeyword() {
        // Given
        String keyword = "testKeyword";
        List<String> expectedPeopleUnitNames = List.of("peopleUnit1", "peopleUnit2");

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(1L)
                .username("junit")
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);
        when(peopleUnitRepository.findPeopleUnitNamesByKeyword(keyword))
                .thenReturn(expectedPeopleUnitNames);

        // When
        List<String> resultPeopleUnitNames = peopleUnitService
                .searchPeopleUnitNamesByKeyword(keyword, principal);

        // Then
        assertEquals(expectedPeopleUnitNames, resultPeopleUnitNames);
        verify(peopleUnitRepository).findPeopleUnitNamesByKeyword(keyword);
    }

    @Disabled("Needs review : connectedUser is null")
    void PeopleUnitService_searchPeopleUnitNamesByKeyword_EmptyKeyword() {
        // Given
        String keyword = "";
        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        AppUser connectedUser = AppUser.builder()
                .id(1L)
                .username("junit")
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .role(Role.HRBP)
                .build();

        when(principal.getPrincipal()).thenReturn(connectedUser);

        // When
        List<String> resultPeopleUnitNames = peopleUnitService
                .searchPeopleUnitNamesByKeyword(keyword, principal);

        // Then
        assertEquals(List.of(), resultPeopleUnitNames);
    }

    @Test
    void PeopleUnitService_getAllPeopleUnits_returnsAllPeopleUnitsDTOs() {
        // Given
        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName("macroPeopleUnit");

        PeopleUnit peopleUnit = new PeopleUnit();
        peopleUnit.setMacroPeopleUnit(macroPeopleUnit);

        List<PeopleUnit> allPeopleUnits = List.of(peopleUnit);

        PeopleUnitResponseDTO peopleUnitResponseDTO = PeopleUnitResponseDTO.builder().build();

        when(peopleUnitRepository.findAll()).thenReturn(allPeopleUnits);
        when(peopleUnitMapper.toPeopleUnitDTO(peopleUnit)).thenReturn(peopleUnitResponseDTO);

        // When
        List<PeopleUnitResponseDTO> resultPeopleUnits = peopleUnitService.getAllPeopleUnits();

        // Then
        assertNotNull(resultPeopleUnits);
        assertEquals(allPeopleUnits.size(), resultPeopleUnits.size());
        verify(peopleUnitRepository).findAll();
    }

    @Test
    void PeopleUnitService_updatePeopleUnit_PeopleUnitUpdatedFromRequestSuccessfully() {
        // Given
        String peopleUnitName = "People Unit Name";
        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder()
                .name("name")
                .build();
        PeopleUnit peopleUnit = PeopleUnit.builder().name(peopleUnitName).build();
        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.of(peopleUnit));

        when(peopleUnitRepository.findPeopleUnitByName("name"))
                .thenReturn(Optional.empty());
        // When
        peopleUnitService.updatePeopleUnit(peopleUnitName, request);

        // Then
        verify(peopleUnitRepository).save(any(PeopleUnit.class));
    }

    @Disabled("Needs PowerMockito to mock private method (which has vulnerabilities)")
    void PeopleUnitService_saveMacroPractice_CalledSaveSuccessfully() {
        // Given
        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder().build();

        when(peopleUnitRepository.findPeopleUnitByName(request.getName()))
                .thenReturn(Optional.empty());

        // When
        peopleUnitService.savePeopleUnit(request);

        // Then
        verify(peopleUnitRepository).save(any(PeopleUnit.class));
    }

    @Test
    void PeopleUnitService_saveMacroPractice_ThrowsException_NameExists() {
        // Given
        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder().
                name("Macro name")
                .build();

        when(peopleUnitRepository.findPeopleUnitByName(request.getName()))
                .thenReturn(Optional.of(new PeopleUnit()));

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> peopleUnitService.savePeopleUnit(request));
    }

    @Test
    void PeopleUnitService_updatePeopleUnit_CalledUpdatePeopleUnitSuccessfully() {
        // Given
        String peopleUnitName = "People Unit Name";

        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder()
                .name("Request PU   Name").build();
        PeopleUnit peopleUnit = PeopleUnit.builder().build();

        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.of(peopleUnit));

        when(peopleUnitRepository.findPeopleUnitByName(request.getName()))
                .thenReturn(Optional.empty());

        // When
        peopleUnitService.updatePeopleUnit(peopleUnitName, request);

        // Then
        verify(peopleUnitRepository).save(any(PeopleUnit.class));
    }

    @Test
    void PeopleUnitService_updatePeopleUnit_ThrowsException_IdNotFound() {
        // Given
        String peopleUnitName = "People Unit Name";

        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder().build();

        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> peopleUnitService.updatePeopleUnit(peopleUnitName, request));
    }

    @Test
    void PeopleUnitService_updatePeopleUnit_ThrowsException_NameExists() {
        // Given
        String existPeopleUnitName = "People Unit Name";
        String updatePeopleUnitName = "Update People Unit Name";

        PeopleUnit existPeopleUnit = PeopleUnit.builder()
                .name(existPeopleUnitName)
                .build();

        PeopleUnitRequestDTO request = PeopleUnitRequestDTO.builder()
                .name(existPeopleUnitName)
                .build();

        when(peopleUnitRepository.findPeopleUnitByName(existPeopleUnitName))
                .thenReturn(Optional.of(existPeopleUnit));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> peopleUnitService.updatePeopleUnit(updatePeopleUnitName, request));
    }

    @Test
    void PeopleUnitService_deletePeopleUnitByName_CalledUpdatePeopleUnitSuccessfully() {
        // Given
        String peopleUnitName = "People Unit Name";
        Long peopleUnitId = 1L;
        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit
                .builder()
                .peopleUnits(Set.of(new PeopleUnit(), new PeopleUnit()))
                .build();
        PeopleUnit peopleUnit = PeopleUnit.builder()
                .id(peopleUnitId)
                .name(peopleUnitName)
                .macroPeopleUnit(macroPeopleUnit)
                .build();

        when(peopleUnitRepository.findPeopleUnitByName(peopleUnitName))
                .thenReturn(Optional.of(peopleUnit));
        when(peopleUnitRepository.findPeopleUnitById(peopleUnitId))
                .thenReturn(Optional.of(peopleUnit));

        // When
        peopleUnitService.deletePeopleUnitByName(peopleUnitName);

        // Then
        verify(peopleUnitRepository).deletePeopleUnitById(peopleUnitId);
    }

    @Test
    void PeopleUnitService_deletePeopleUnitById_ThrowsExceptionCausedByIdNotfound() {
        // Given
        Long peopleUnitId = 1L;

        when(peopleUnitRepository.findById(peopleUnitId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> peopleUnitService.deletePeopleUnitById(peopleUnitId));
    }
}