package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ProjectManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import com.capgemini.trialperiodapi.mapper.ProjectManagerMapper;
import com.capgemini.trialperiodapi.model.ProjectManager;
import com.capgemini.trialperiodapi.repository.ProjectManagerRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import java.util.HashSet;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ProjectManagerServiceTest {

    @Spy
    @InjectMocks
    ProjectManagerService projectManagerService;

    @Mock
    ProjectManagerRepository projectManagerRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private ProjectService projectService;

    @Mock
    private ProjectManagerMapper projectManagerMapper;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void ProjectManagerService_saveProjectManager_ProjectManagerSavedSuccessfully() {
        // Given
        ProjectManagerRequestDTO request = ProjectManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .projectNames(new HashSet<>())
                .build();

        // When
        projectManagerService.saveProjectManager(request);

        // Then
        verify(projectManagerRepository)
                .save(any(ProjectManager.class));
    }

    @Test
    void ProjectManagerService_saveProjectManager_throwIllegalArgumentException() {
        // Given
        String email = "<EMAIL>";
        String firstname = "firstname";
        String lastname = "lastname";

        ProjectManagerRequestDTO request = ProjectManagerRequestDTO.builder()
                .firstname(firstname)
                .lastname(lastname)
                .email(email)
                .build();

        ProjectManager projectManager = new ProjectManager();

        when(projectManagerRepository.findProjectManagerByEmail(email))
                .thenReturn(Optional.of(projectManager));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectManagerService.saveProjectManager(request));

    }

    @Test
    void ProjectManagerService_updateProjectManager_throwIllegalArgumentExceptionCausedByEmailAlreadyExists() {
        // Given
        String email = "<EMAIL>";
        Long projectManagerId = 1L;

        ProjectManager projectManager = new ProjectManager();
        projectManager.setId(2L);

        ProjectManagerRequestDTO request = ProjectManagerRequestDTO.builder()
                .email(email)
                .build();

        when(projectManagerRepository.findProjectManagerByEmail(email))
                .thenReturn(Optional.of(projectManager));
        when(projectManagerRepository.findById(anyLong()))
                .thenReturn(Optional.of(new ProjectManager()));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectManagerService.updateProjectManager(projectManagerId, request));

    }

    @Test
    void ProjectManagerService_getProjectManagerById_returnsProjectManagerById() {
        // Given
        Long projectManagerId = 1L;

        ProjectManagerResponseDTO projectManagerResponseDTO = new ProjectManagerResponseDTO();
        ProjectManager projectManager = new ProjectManager();
        projectManager.setId(projectManagerId);

        when(projectManagerRepository.findById(projectManagerId)).thenReturn(Optional.of(projectManager));
        when(projectManagerMapper.toProjectManagerResponseDTO(projectManager))
                .thenReturn(projectManagerResponseDTO);

        // When
        ProjectManagerResponseDTO returnedProjectManager =
                projectManagerService.getProjectManagerDTOById(projectManagerId);

        // Then
        assertNotNull(returnedProjectManager);
        verify(projectManagerRepository).findById(projectManagerId);
    }

    @Test
    void ProjectManagerService_getProjectManagerById_throwIllegalArgumentExceptionCausedByProjectManagerNotFound() {
        // Given
        Long projectManagerId = 1L;

        when(projectManagerRepository.findById(projectManagerId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectManagerService.getProjectManagerById(projectManagerId));
    }

    @Test
    void ProjectManagerService_getProjectManagerByEmail_returnsProjectManagerByEmail() {
        // Given
        String email = "<EMAIL>";

        ProjectManager projectManager = new ProjectManager();
        projectManager.setEmail(email);

        when(projectManagerRepository.findProjectManagerByEmail(email))
                .thenReturn(Optional.of(projectManager));

        // When
        var expectedProjectManager = projectManagerService.getProjectManagerByEmail(email);

        // Then
        assertNotNull(expectedProjectManager);
        assertEquals(email, expectedProjectManager.getEmail());
        verify(projectManagerRepository).findProjectManagerByEmail(email);
    }

    @Test
    void ProjectManagerService_getProjectManagerByEmail_throwIllegalArgumentExceptionCausedByProjectManagerNotFound() {
        // Given
        String email = "<EMAIL>";

        when(projectManagerRepository.findProjectManagerByEmail(email))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> projectManagerService.getProjectManagerByEmail(email));
    }

    @Test
    void ProjectManagerService_deleteProjectManagerById_callUpdateMethod() {
        // Given
        String projectManagerEmail = "<EMAIL>";

        ProjectManager projectManager = new ProjectManager();
        projectManager.setEmail(projectManagerEmail);

        when(projectManagerRepository.findProjectManagerByEmail(projectManagerEmail))
                .thenReturn(Optional.of(projectManager));

        // When
        projectManagerService.deleteProjectManagerByEmail(projectManagerEmail);

        // Then
        verify(projectManagerRepository).deleteProjectManagerByEmail(projectManagerEmail);
    }

    @Test
    void ProjectManagerService_update_throwsIllegalArgumentExceptionProjectManagerByIdNotFound() {
        // Given
        Long projectManagerId = 1L;

        ProjectManagerRequestDTO request = ProjectManagerRequestDTO.builder().build();

        when(projectManagerRepository.findById(projectManagerId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectManagerService.updateProjectManager(projectManagerId, request));
    }
}