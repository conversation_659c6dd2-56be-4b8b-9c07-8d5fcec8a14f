package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ProjectRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ProjectResponseDTO;
import com.capgemini.trialperiodapi.mapper.ProjectMapper;
import com.capgemini.trialperiodapi.model.PeopleUnit;
import com.capgemini.trialperiodapi.model.Project;
import com.capgemini.trialperiodapi.model.ProjectManager;
import com.capgemini.trialperiodapi.repository.ProjectManagerRepository;
import com.capgemini.trialperiodapi.repository.ProjectRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ProjectServiceTest {
    @Spy
    @InjectMocks
    ProjectService projectService;

    @Mock
    ProjectRepository projectRepository;

    @Mock
    IProjectManagerService projectManagerService;

    @Mock
    ProjectManagerRepository projectManagerRepository;

    @Mock
    IPeopleUnitService peopleUnitService;

    @Mock
    ProjectMapper projectMapper;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void ProjectService_getProjectByName_returnsProjectByName() {
        // Given
        String name = "TestProject";
        Project project = Project.builder().name(name).build();
        when(projectRepository.findProjectByName(name))
                .thenReturn(Optional.of(project));
        // When
        var expectedProject = projectService.getProjectByName(name);

        // Then
        assertNotNull(expectedProject);
        assertEquals(name, expectedProject.getName());
        verify(projectRepository).findProjectByName(name);
    }

    @Test
    void ProjectService_getProjectByName_throwEntityNotFoundException() {
        // Given
        String name = "TestProject";
        when(projectRepository.findProjectByName(name))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> projectService.getProjectByName(name));
    }

    @Disabled("Need a review")
    void ProjectService_getAllProjects_returnsListOfProjects() {
        // Given
        String keyword = "someKeyword";
        var projects = List.of(new Project());

        Pageable pageable = PageRequest.of(0, 10);
        Page<Project> projectPage = new PageImpl<>(projects, pageable, projects.size());

        when(projectRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(projectPage);

        // When
        Page<ProjectResponseDTO> expectedProjectPage =
                projectService.getAllProjects(keyword, pageable);

        // Then
        assertNotNull(expectedProjectPage);
        assertEquals(projectPage, expectedProjectPage);
    }

    @Test
    void ProjectService_saveProject_saveProjectSuccessfully() {
        // Given
        String projectName = "Project Name";
        ProjectRequestDTO request = ProjectRequestDTO.builder()
                .name(projectName)
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .build();

        when(projectRepository.findProjectByName(projectName))
                .thenReturn(Optional.empty());
        when(peopleUnitService.getPeopleUnitByName(anyString()))
                .thenReturn(peopleUnit);
        // When
        projectService.saveProject(request);

        // Then
        verify(projectRepository).save(any(Project.class));
    }

    @Test
    void ProjectService_saveProject_throwsIllegalArgumentException() {
        // Given
        String projectName = "Project Name";
        ProjectRequestDTO request = ProjectRequestDTO.builder().name(projectName).build();
        Project project = Project.builder().build();

        when(projectRepository.findProjectByName(projectName))
                .thenReturn(Optional.of(project));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectService.saveProject(request));
    }

    @Test
    void ProjectService_updateProject_projectUpdatedSuccessfully() {
        // Given
        String projectName = "Project Name";
        String email = "<EMAIL>";
        ProjectManager projectManager = new ProjectManager();
        projectManager.setEmail(email);
        projectManager.setProjects(new HashSet<>(Set.of(new Project())));

        ProjectRequestDTO request = ProjectRequestDTO.builder()
                .projectManagerEmails(Set.of(email))
                .build();

        when(projectManagerRepository.findProjectManagerByEmail(anyString()))
                .thenReturn(Optional.of(projectManager));
        when(projectManagerService.getProjectManagerByEmail(email))
                .thenReturn(projectManager);
        when(projectRepository.findProjectByName(projectName))
                .thenReturn(Optional.of(new Project()));
        // When
        projectService.updateProject(projectName, request);

        // Then
        verify(projectRepository).save(any(Project.class));

        // assignProjectManagerToProject still not tested
    }

    @Test
    void ProjectService_updateProject_throwsIllegalArgumentExceptionProjectNameAlreadyExists() {
        // Given
        String requestProjectName = "Request Project Name";
        String orlProjectName = "Old Project Name";
        Long existProjectId = 2L;

        ProjectRequestDTO request = ProjectRequestDTO.builder().name(requestProjectName).build();
        Project project = Project.builder().id(existProjectId).name(requestProjectName).build();

        when(projectRepository.findProjectByName(requestProjectName))
                .thenReturn(Optional.of(project));

        when(projectRepository.findById(anyLong()))
                .thenReturn(Optional.of(new Project()));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> projectService.updateProject(orlProjectName, request));
    }

    @Test
    void ProjectService_deleteProject_deleteProjectSuccessfully() {
        // Given
        String projectName = "Project To Delete";
        Long projectId = 1L;
        Project project = Project.builder()
                .id(projectId)
                .name(projectName)
                .collaborators(new HashSet<>())
                .build();
        when(projectRepository.findProjectByName(projectName))
                .thenReturn(Optional.of(project));

        // When
        projectService.deleteProjectByProjectName(projectName);

        // Then
        verify(projectRepository).deleteById(projectId);
    }

    @Test
    void ProjectService_searchProjectNamesByKeyword_repositoryCalled() {
        // Given
        String keyword = "someKeyWord";

        // When
        projectService.searchProjectNamesByKeyword(keyword);

        // Then
        verify(projectRepository).findProjectNamesByKeyword(keyword);
    }
}