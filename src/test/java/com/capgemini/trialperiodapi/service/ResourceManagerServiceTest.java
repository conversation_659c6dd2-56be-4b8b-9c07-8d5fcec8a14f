package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.request.ResourceManagerRequestDTO;
import com.capgemini.trialperiodapi.dto.response.ResourceManagerResponseDTO;
import com.capgemini.trialperiodapi.mapper.ResourceManagerMapper;
import com.capgemini.trialperiodapi.model.MacroPeopleUnit;
import com.capgemini.trialperiodapi.model.ResourceManager;
import com.capgemini.trialperiodapi.repository.MacroPeopleUnitRepository;
import com.capgemini.trialperiodapi.repository.ResourceManagerRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ResourceManagerServiceTest {

    @InjectMocks
    private ResourceManagerService resourceManagerService;

    @Mock
    private ResourceManagerRepository resourceManagerRepository;

    @Mock
    private MacroPeopleUnitRepository macroPeopleUnitRepository;

    @Mock
    private ResourceManagerMapper resourceManagerMapper;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private MacroPeopleUnitService macroPeopleUnitService;

    @Mock
    private IUserService userService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        resourceManagerService = new ResourceManagerService(
                resourceManagerRepository,
                macroPeopleUnitRepository,
                macroPeopleUnitService,
                resourceManagerMapper,
                messageSourceUtil,
                userService);
    }

    @Test
    void ResourceManagerService_saveResourceManager_ResourceManagerSavedSuccessfully() {
        // Given
        ResourceManagerRequestDTO resourceManagerRequestDTO = ResourceManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .build();

        // When
        resourceManagerService.saveResourceManager(resourceManagerRequestDTO);

        // Then
        verify(resourceManagerRepository).save(any(ResourceManager.class));
    }

    @Test
    void ResourceManagerService_getResourceManagerByEmail_returnResourceManager() {
        // Given
        String email = "<EMAIL>";
        ResourceManager expectedResourceManager = new ResourceManager();
        expectedResourceManager.setId(1L);
        expectedResourceManager.setEmail(email);


        when(resourceManagerRepository.findResourceManagerByEmail(email))
                .thenReturn(Optional.of(expectedResourceManager));

        // When
        ResourceManager actualResourceManager = resourceManagerService.getResourceManagerByEmail(email);

        // Then
        assertEquals(expectedResourceManager, actualResourceManager);
        verify(resourceManagerRepository).findResourceManagerByEmail(email);
    }

    @Test
    void ResourceManagerService_searchResourceManagersByKeyword_returnListResourceManagerResponseDTO() {
        // Given
        String keyword = "test";
        ResourceManager resourceManager1 = new ResourceManager();
        resourceManager1.setId(1L);

        ResourceManager resourceManager2 = new ResourceManager();
        resourceManager1.setId(2L);

        ResourceManagerResponseDTO responseDTO1 = ResourceManagerResponseDTO.builder()
                .id(1L)
                .build();
        ResourceManagerResponseDTO responseDTO2 = ResourceManagerResponseDTO.builder()
                .id(2L)
                .build();
        List<ResourceManagerResponseDTO> expectedList = List.of(responseDTO1, responseDTO2);

        when(resourceManagerRepository.findResourceManagersByKeyword(keyword))
                .thenReturn(List.of(resourceManager1, resourceManager2));
        when(resourceManagerMapper.toResourceManagerDTO(resourceManager1))
                .thenReturn(responseDTO1);
        when(resourceManagerMapper.toResourceManagerDTO(resourceManager2))
                .thenReturn(responseDTO2);

        // When
        List<ResourceManagerResponseDTO> resultedList = resourceManagerService.filterResourceManagers(keyword);

        // Then
        assertNotNull(resultedList);
        assertEquals(expectedList, resultedList);
        verify(resourceManagerRepository).findResourceManagersByKeyword(keyword);
        verify(resourceManagerMapper).toResourceManagerDTO(resourceManager1);
        verify(resourceManagerMapper).toResourceManagerDTO(resourceManager2);
    }

    @Test
    void ResourceManagerService_deleteResourceManagerByEmail_ResourceManagerDeletedSuccessfully() {
        // Given
        Long resourceManagerId = 1L;
        String resourceManagerEmail = "<EMAIL>";
        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setId(resourceManagerId);
        resourceManager.setEmail(resourceManagerEmail);
        resourceManager.setMacroPeopleUnits(new HashSet<>());

        when(resourceManagerRepository.findResourceManagerByEmail(resourceManagerEmail))
                .thenReturn(Optional.of(resourceManager));

        // When
        resourceManagerService.deleteResourceManagerByEmail(resourceManagerEmail);

        // Then
        verify(resourceManagerRepository).findResourceManagerByEmail(resourceManagerEmail);
        verify(resourceManagerRepository).deleteById(resourceManagerId);
    }

    @Test
    void ResourceManagerService_getResourceManagerDtoById_returnResourceManagerDto() {
        // Given
        Long resourceManagerId = 1L;
        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setId(resourceManagerId);

        ResourceManagerResponseDTO responseDTO = ResourceManagerResponseDTO.builder()
                .id(resourceManagerId)
                .build();

        when(resourceManagerRepository.findById(resourceManagerId))
                .thenReturn(Optional.of(resourceManager));

        when(resourceManagerMapper.toResourceManagerDTO(resourceManager))
                .thenReturn(responseDTO);

        // When
        ResourceManagerResponseDTO actualResponseDTO = resourceManagerService.getResourceManagerDtoById(resourceManagerId);

        // Then
        assertNotNull(actualResponseDTO);
        assertEquals(responseDTO, actualResponseDTO);
        verify(resourceManagerRepository).findById(resourceManagerId);
        verify(resourceManagerMapper).toResourceManagerDTO(resourceManager);
    }

    @Test
    void ResourceManagerService_updateResourceManager_ResourceManagerUpdatedSuccessfully() {
        // Given
        Long updatedId = 1L;
        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setId(1L);

        MacroPeopleUnit macroPeopleUnit = new MacroPeopleUnit();
        macroPeopleUnit.setName("Unit1");

        ResourceManagerRequestDTO resourceManagerRequestDTO = ResourceManagerRequestDTO.builder()
                .firstname("firstname")
                .lastname("lastname")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("Unit1"))
                .build();

        when(resourceManagerRepository.findById(updatedId))
                .thenReturn(Optional.of(resourceManager));
        when(resourceManagerRepository.findResourceManagerByEmail("<EMAIL>"))
                .thenReturn(Optional.empty());
        when(macroPeopleUnitService.getMacroPeopleUnitByName("Unit1"))
                .thenReturn(macroPeopleUnit);

        // When
        resourceManagerService.updateResourceManager(updatedId, resourceManagerRequestDTO);

        // Then
        verify(resourceManagerRepository).findById(updatedId);
        verify(resourceManagerRepository).findResourceManagerByEmail("<EMAIL>");
        verify(macroPeopleUnitRepository).save(macroPeopleUnit);
        verify(resourceManagerRepository).save(resourceManager);

        assertEquals(resourceManagerRequestDTO.getFirstname(), resourceManager.getFirstname());
        assertEquals(resourceManagerRequestDTO.getLastname(), resourceManager.getLastname());
        assertEquals(resourceManagerRequestDTO.getEmail(), resourceManager.getEmail());
    }

    @Test
    void ResourceManagerService_updateResourceManager_throwsIllegalArgumentException_causedByIdNotFound() {
        // Given
        Long nonExistingId = 1L;
        ResourceManagerRequestDTO resourceManagerRequestDTO = ResourceManagerRequestDTO.builder()
                .firstname("new firstname")
                .lastname("new lastname")
                .email("<EMAIL>")
                .macroPeopleUnitNames(Set.of("Unit1"))
                .build();

        when(resourceManagerRepository.findById(nonExistingId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> resourceManagerService.updateResourceManager(nonExistingId, resourceManagerRequestDTO));
    }

    @Test
    void ResourceManagerService_updateResourceManager_throwsIllegalArgumentException_causedByEmailAlreadyExists() {
        // Given
        Long updatedId = 1L;
        ResourceManager existingResourceManager = new ResourceManager();
        existingResourceManager.setId(updatedId);
        existingResourceManager.setEmail("<EMAIL>");

        ResourceManager anotherResourceManager = new ResourceManager();
        anotherResourceManager.setId(2L);
        anotherResourceManager.setEmail("<EMAIL>");

        ResourceManagerRequestDTO resourceManagerRequestDTO = ResourceManagerRequestDTO.builder()
                .firstname("new firstname")
                .lastname("new lastname")
                .email("<EMAIL>")
                .build();

        when(resourceManagerRepository.findById(updatedId))
                .thenReturn(Optional.of(existingResourceManager));
        when(resourceManagerRepository.findResourceManagerByEmail("<EMAIL>"))
                .thenReturn(Optional.of(anotherResourceManager));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> resourceManagerService.updateResourceManager(updatedId, resourceManagerRequestDTO));
    }
}