package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.model.*;
import com.capgemini.trialperiodapi.repository.CollaboratorRepository;
import com.capgemini.trialperiodapi.repository.TrialPeriodRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class TrialPeriodServiceTest {

    @InjectMocks
    private TrialPeriodService trialPeriodService;

    @Mock
    private EmailService emailService;

    @Mock
    private TrialPeriodRepository trialPeriodRepository;

    @Mock
    private PeopleUnitService peopleUnitService;

    @Mock
    private CollaboratorRepository collaboratorRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void TrialPeriodService_saveTrialPeriod_trialPeriodSuccessfullySaved() {
        // Given
        Collaborator collaborator = Collaborator.builder().id(1L).build();

        TrialPeriod trialPeriod = TrialPeriod.builder()
                .firstTrialPeriodEndDate(LocalDate.of(2024, 8, 1))
                .secondTrialPeriodEndDate(LocalDate.of(2024, 11, 1))
                .firstTrialPeriodNotificationDate(LocalDate.of(2024, 7, 15))
                .secondTrialPeriodNotificationDate(LocalDate.of(2024, 10, 15))
                .collaborator(collaborator)
                .build();

        // When
        trialPeriodRepository.save(trialPeriod);

        // Then
        verify(trialPeriodRepository).save(trialPeriod);
    }

    @Disabled("Needs a review")
    void TrialPeriodService_withFilters_returnSpecificationForHRBP() {
        // Given
        List<PeopleUnit> hrbpPeopleUnits = List.of(PeopleUnit.builder().id(1L).build());

        Hrbp connectedUser = Hrbp.builder()
                .id(1L)
                .username("oldUsername")
                .email("<EMAIL>")
                .firstname("old firstname")
                .lastname("old lastname")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);

        when(principal.getPrincipal()).thenReturn(connectedUser);

        when(peopleUnitService.getPeopleUnitsByConnectedUser(connectedUser)).thenReturn(hrbpPeopleUnits);

        // When
        Specification<TrialPeriod> specification =
                trialPeriodService.withFilters(any(Principal.class));

        // Then
        assertNotNull(specification);
    }

    @Test
    void TrialPeriodService_getRecipientsByTrialPeriods_returnOnlyPeopleUnitManagers() {
        // Given
        PeopleUnitManager pum = new PeopleUnitManager();
        pum.setId(1L);

        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setId(1L);

        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .pums(Set.of(pum))
                .resourceManagers(Set.of(resourceManager))
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .macroPeopleUnit(macroPeopleUnit).build();

        ProjectManager projectManager = new ProjectManager();
        pum.setId(1L);

        Project project = Project.builder()
                .id(1L)
                .projectManagers(Set.of(projectManager)).build();

        Collaborator collaborator1 = Collaborator.builder()
                .id(1L)
                .assignmentStatus(AssignmentStatus.SHADOW)
                .peopleUnit(peopleUnit)
                .project(project).build();

        Collaborator collaborator2 = Collaborator.builder()
                .id(2L)
                .assignmentStatus(AssignmentStatus.OPPORTUNITY_0)
                .peopleUnit(peopleUnit)
                .project(project).build();

        List<TrialPeriod> trialPeriods = List.of(
                TrialPeriod.builder().id(1L).collaborator(collaborator1).build(),
                TrialPeriod.builder().id(2L).collaborator(collaborator2).build());

        // When
        Set<DecisionMaker> decisionMakers =
                trialPeriodService.getRecipientsByTrialPeriods(trialPeriods);

        // Then
        assertEquals(2, decisionMakers.size());
        assertThat(decisionMakers).allSatisfy(decisionMaker -> assertThat(decisionMaker)
                        .isInstanceOfAny(PeopleUnitManager.class, ResourceManager.class));
    }

    @Test
    void TrialPeriodService_getRecipientsByTrialPeriods_returnOnlyProjectManagers() {
        // Given
        PeopleUnitManager pum = new PeopleUnitManager();
        pum.setId(1L);

        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .pums(Set.of(pum)).build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .macroPeopleUnit(macroPeopleUnit).build();

        ProjectManager projectManager = new ProjectManager();
        pum.setId(1L);

        Project project = Project.builder()
                .id(1L)
                .projectManagers(Set.of(projectManager)).build();

        Collaborator collaborator1 = Collaborator.builder()
                .id(1L)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .peopleUnit(peopleUnit)
                .project(project).build();

        List<TrialPeriod> trialPeriods = List.of(
                TrialPeriod.builder().id(1L).collaborator(collaborator1).build());

        // When
        Set<DecisionMaker> decisionMakers =
                trialPeriodService.getRecipientsByTrialPeriods(trialPeriods);

        // Then
        assertEquals(1, decisionMakers.size());
        assertThat(decisionMakers)
                .allSatisfy(decisionMaker -> assertThat(decisionMaker)
                        .isInstanceOf(ProjectManager.class));
    }

    @Test
    void TrialPeriodService_getRecipientsByTrialPeriods_returnProjectManagersAndPUMs() {
        // Given
        PeopleUnitManager pum = new PeopleUnitManager();
        pum.setId(1L);

        ResourceManager resourceManager = new ResourceManager();
        resourceManager.setId(1L);

        MacroPeopleUnit macroPeopleUnit = MacroPeopleUnit.builder()
                .pums(Set.of(pum))
                .resourceManagers(Set.of(resourceManager))
                .build();

        PeopleUnit peopleUnit = PeopleUnit.builder()
                .macroPeopleUnit(macroPeopleUnit).build();

        ProjectManager projectManager = new ProjectManager();
        pum.setId(2L);

        Project project = Project.builder()
                .id(1L)
                .projectManagers(Set.of(projectManager)).build();

        Collaborator collaborator1 = Collaborator.builder()
                .id(1L)
                .assignmentStatus(AssignmentStatus.SHADOW)
                .peopleUnit(peopleUnit)
                .project(project).build();

        Collaborator collaborator2 = Collaborator.builder()
                .id(2L)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .peopleUnit(peopleUnit)
                .project(project).build();

        List<TrialPeriod> trialPeriods = List.of(
                TrialPeriod.builder().id(1L).collaborator(collaborator1).build(),
                TrialPeriod.builder().id(2L).collaborator(collaborator2).build());

        // When
        Set<DecisionMaker> decisionMakers =
                trialPeriodService.getRecipientsByTrialPeriods(trialPeriods);

        // Then
        assertEquals(3, decisionMakers.size());
        assertThat(decisionMakers).allSatisfy(decisionMaker -> assertThat(decisionMaker)
                .isInstanceOfAny(PeopleUnitManager.class, ResourceManager.class, ProjectManager.class));
    }

    @Test
    void TrialPeriodService_isProjectBelongsToRecipient_returnTrue() {
        // Given
        ProjectManager recipients = new ProjectManager();
        Project project = Project.builder()
                .projectManagers(Set.of(recipients))
                .build();

        Collaborator collaborator = Collaborator.builder()
                .id(1L)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .project(project)
                .build();

        project.setCollaborators(Set.of(collaborator));

        // When
        boolean isProjectBelongsToRecipient =
                trialPeriodService.isProjectBelongsToRecipient(recipients, project);

        // Then
        assertTrue(isProjectBelongsToRecipient);
    }

    @Test
    void TrialPeriodService_isProjectBelongsToRecipient_returnFalse() {
        // Given
        ProjectManager recipients = new ProjectManager();
        Project project = Project.builder()
                .projectManagers(Set.of(recipients))
                .build();

        Collaborator collaborator = Collaborator.builder()
                .id(1L)
                .assignmentStatus(AssignmentStatus.FIRM_PROJECT)
                .project(project)
                .build();

        project.setCollaborators(Set.of(collaborator));

        // When
        boolean isProjectBelongsToRecipient =
                trialPeriodService.isProjectBelongsToRecipient(recipients, project);

        // Then
        assertTrue(isProjectBelongsToRecipient);
    }
}