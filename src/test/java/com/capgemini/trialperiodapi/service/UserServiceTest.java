package com.capgemini.trialperiodapi.service;

import com.capgemini.trialperiodapi.dto.response.UserResponseDTO;
import com.capgemini.trialperiodapi.dto.request.UserRequestDTO;
import com.capgemini.trialperiodapi.mapper.UserMapper;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.service.auth.IVerificationTokenService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith(MockitoExtension.class)
@ExtendWith(SpringExtension.class)
class UserServiceTest {
    @InjectMocks
    private UserService underTest;

    @Mock
    private UserRepository userRepository;

    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private IVerificationTokenService verificationTokenService;

    @Mock
    private EmailService emailService;

    @Mock
    private UserMapper userMapper;

    @BeforeEach
    void setUp() {
        openMocks(this);
        underTest = new UserService(verificationTokenService, messageSourceUtil, verificationTokenRepository, userRepository, userMapper);
    }

    @Test
    void UserService_getUserById_succeed() {
        // Given
        Long userId = 1L;
        AppUser user = new AppUser();
        user.setId(userId);

        when(userRepository.findUserById(userId)).thenReturn(Optional.of(user));

        // When
        AppUser retrievedUser = underTest.getUserById(userId);

        // Then
        assertNotNull(retrievedUser);
        assertEquals(userId, retrievedUser.getId());
    }

    @Test
    void UserService_getUserById_ThrowsEntityNotFoundException() {
        // Given
        Long userId = 1L;
        when(userRepository.findUserById(userId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> underTest.getUserById(userId));
    }

    @Test
    void UserService_saveUserVerificationToken_succeed() {
        // Given
        String token = "tokenTest";
        String code = "123456";
        AppUser user = AppUser.builder().id(1L).build();

        when(verificationTokenService.generateActivationCode(6))
                .thenReturn(code);

        // When
        underTest.saveUserVerificationToken(user, token);

        // Then
        verify(verificationTokenService).generateActivationCode(6);
        verify(verificationTokenRepository).save(any(VerificationToken.class));
    }

    @Test
    void UserService_revokeAllUserTokens_succeed() {
        // Given
        AppUser user = new AppUser();
        user.setId(1L);

        VerificationToken token1 = new VerificationToken();
        token1.setRevoked(false);
        token1.setExpired(false);

        VerificationToken token2 = new VerificationToken();
        token2.setRevoked(false);
        token2.setExpired(false);

        user.setTokens(List.of(token1, token2));

        // When
        when(verificationTokenRepository.findValidVerificationTokensByUserId(user.getId()))
                .thenReturn(List.of(token1, token2));

        underTest.revokeAllUserTokens(user);

        // Then
        assertTrue(user.getTokens().get(0).isExpired() && user.getTokens().get(0).isRevoked());
        assertTrue(user.getTokens().get(1).isExpired() && user.getTokens().get(1).isRevoked());
    }

    @Test
    void UserService_getUserByUsername_returnUser() {
        // Given
        AppUser user = mock(AppUser.class);
        String username = "junit";

        when(userRepository.findUserByUsername(anyString()))
                .thenReturn(Optional.ofNullable(user));

        // When
        AppUser returnUser = underTest.getUserByUsername(username);

        // Then
        Assertions.assertEquals(user, returnUser);
    }

    @Test
    void UserService_getUserByUsername_ThrowsEntityNotFoundException() {
        // Given
        String username = "junit";

        when(userRepository.findUserByUsername(anyString()))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> underTest.getUserByUsername(username));
    }

    @Test
    void UserService_getUserByEmail_returnUser() {
        // Given
        AppUser user = mock(AppUser.class);
        String email = "<EMAIL>";

        when(userRepository.findUserByEmail(anyString()))
                .thenReturn(Optional.ofNullable(user));

        // When
        AppUser returnUser = underTest.getUserByEmail(email);

        // Then
        assertEquals(user, returnUser);
    }

    @Test
    void UserService_getUserByEmail_ThrowsEntityNotFoundException() {
        // Given
        String email = "<EMAIL>";

        when(userRepository.findUserByEmail(anyString()))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> underTest.getUserByEmail(email));
    }

    @Test
    void UserService_deleteUserAccount_ReturnVoid() {
        // Given
        Long userId = 1L;
        AppUser user = AppUser.builder().id(1L).build();

        VerificationToken token = new VerificationToken();
        token.setUser(user);

        when(userRepository.findUserById(any())).thenReturn(Optional.ofNullable(user));
        doReturn(Collections.singletonList(token)).when(verificationTokenRepository).findAllVerificationTokensByUserId(userId);
        doNothing().when(verificationTokenRepository).deleteAll(any());

        // When & Then
        assertAll(() -> underTest.deleteUserAccount(userId));
    }

    @Test
    void UserService_deleteUserAccount_EntityNotFoundException() {
        // Given
        Long userId = 1L;
        when(userRepository.findUserById(userId))
                .thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class,
                () -> underTest.deleteUserAccount(userId));
    }
    @Test
    void UserService_updateConnectedUser_updatesSuccessfully() {
        // Given
        String newFirstname = "newFirstName";
        String newLastname = "newLastName";
        String newUsername = "newUsername";
        String newEmail = "<EMAIL>";

        UserRequestDTO request = UserRequestDTO.builder()
                .firstname(newFirstname)
                .lastname(newLastname)
                .username(newUsername)
                .email(newEmail)
                .build();

        AppUser connectedUser = AppUser.builder()
                .id(1L)
                .username("oldUsername")
                .email("<EMAIL>")
                .firstname("old firstname")
                .lastname("old lastname")
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        when(principal.getPrincipal()).thenReturn(connectedUser);

        // When
        underTest.updateConnectedUser(request, principal);

        // Then
        assertEquals(newFirstname, connectedUser.getFirstname());
        assertEquals(newLastname, connectedUser.getLastname());
        assertEquals(newUsername, connectedUser.getUsername());
        assertEquals(newEmail, connectedUser.getEmail());
        verify(userRepository).save(any(AppUser.class));
    }

    @Disabled("Needs a review")
    void UserService_getConnectedUser_returnsConnectedUser() {
        // Given
        Long connectedUserId = 1L;
        String connectedUserName = "junit";
        String email = "<EMAIL>";

        AppUser connectedUser = AppUser.builder()
                .id(connectedUserId)
                .username(connectedUserName)
                .email(email)
                .role(Role.ADMIN)
                .build();

        UserResponseDTO expectedUserDTO = UserResponseDTO.builder()
                .id(connectedUserId)
                .username(connectedUserName)
                .email(email)
                .build();

        Authentication principal = mock(UsernamePasswordAuthenticationToken.class);
        when(principal.getPrincipal()).thenReturn(connectedUser);

        when(userRepository.findUserById(connectedUserId))
                .thenReturn(Optional.of(connectedUser));
        when(userMapper.toHrbpDTO(connectedUser))
                .thenReturn(expectedUserDTO);

        // When
        UserResponseDTO actualUserDTO = underTest.getConnectedUser(principal);

        // Then
        assertEquals(expectedUserDTO, actualUserDTO);
        verify(userRepository).findUserById(connectedUserId);
        verify(userMapper).toHrbpDTO(connectedUser);
    }

    @Test
    void UserService_validateUser_ThrowsIllegalArgumentException_WhenUsernameAlreadyExists() {
        // Given
        String username = "junit";
        AppUser user = new AppUser();
        user.setUsername(username);

        String email = "<EMAIL>";

        when(userRepository.findUserByUsername(username))
                .thenReturn(Optional.of(user));

        // When & Then
        assertThrows(IllegalArgumentException.class,
                () -> underTest.validateUser(username, email));
    }

    @Test
    void UserService_validateUser_ThrowsIllegalArgumentException_WhenEmailAlreadyExists() {
        // Given
        String username = "junit";

        String email = "<EMAIL>";
        AppUser user = new AppUser();
        user.setEmail(email);

        when(userRepository.findUserByEmail(email)).thenReturn(Optional.of(user));

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> underTest.validateUser(username, email));
    }

}