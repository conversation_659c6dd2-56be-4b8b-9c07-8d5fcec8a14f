package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.AuthenticationRequestDTO;
import com.capgemini.trialperiodapi.dto.request.RegisterRequestDTO;
import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.EmailTemplateName;
import com.capgemini.trialperiodapi.model.auth.Role;
import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.service.EmailService;
import com.capgemini.trialperiodapi.service.UserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "app.frontend-activation-url=http://frontend-activation-url.com"
})
class AuthenticationServiceTest {

    @InjectMocks
    private AuthenticationService authenticationService;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private VerificationTokenService tokenService;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private ServletOutputStream outputStream;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private UserRepository userRepository;

    @Mock
    private HttpServletResponse response;

    @Mock
    private HttpServletRequest request;

    @Mock
    private EmailService emailService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private UserService userService;

    @Mock
    private JwtService jwtService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        authenticationService = new AuthenticationService(authenticationManager, tokenService, messageSourceUtil, passwordEncoder, userRepository, emailService, userService, jwtService);

        AppUser user = new AppUser();
        user.setUsername("testUser");
        user.setEnabled(false);
    }

    @Value("${app.frontend-activation-url}")
    private String activationUrl;

    @Test
    void AuthenticationService_register_userRegisteredSuccessfully() {
        // Given
        RegisterRequestDTO registerRequest = RegisterRequestDTO.builder()
                .username("johndoe")
                .email("<EMAIL>")
                .password("Password.0000")
                .build();

        AppUser user = AppUser.builder()
                .email("<EMAIL>")
                .username("johndoe")
                .build();

        String accessToken = "accessToken";
        String refreshToken = "refreshToken";
        String activationCode = "activationCode";

        doNothing().when(userService).validateUser(anyString(), anyString());
        when(userRepository.saveAndFlush(any(AppUser.class))).thenReturn(user);
        when(userService.getUserByEmail(registerRequest.getEmail())).thenReturn(user);
        when(jwtService.generateAccessToken(any(AppUser.class))).thenReturn(accessToken);
        when(jwtService.generateRefreshToken(any(AppUser.class))).thenReturn(refreshToken);
        doNothing().when(userService).saveUserVerificationToken(any(AppUser.class), anyString());
        when(passwordEncoder.encode(anyString())).thenReturn(anyString());
        when(tokenService.getCodeByToken(accessToken)).thenReturn(activationCode);

        // When
        AuthenticationResponseDTO response = authenticationService.register(registerRequest);

        // Then
        assertNotNull(response);
        assertEquals(accessToken, response.getAccessToken());
        assertEquals(refreshToken, response.getRefreshToken());

        verify(userService).validateUser(registerRequest.getUsername(), registerRequest.getEmail());
        verify(userRepository).saveAndFlush(any(AppUser.class));
        verify(passwordEncoder).encode(registerRequest.getPassword());
        verify(jwtService).generateAccessToken(any(AppUser.class));
        verify(jwtService).generateRefreshToken(any(AppUser.class));
        verify(emailService).sendActivationAccountEmail(
                eq(user.getEmail()),
                eq(user.getUsername()),
                eq(EmailTemplateName.ACTIVATE_ACCOUNT),
                eq(activationUrl),
                eq(activationCode)
        );
    }

    @Test
    void AuthenticationService_register_UsernameOrEmailAlreadyExists() {
        // Given
        RegisterRequestDTO registerRequest = RegisterRequestDTO.builder()
                .firstname("John")
                .lastname("Doe")
                .username("johndoe")
                .email("<EMAIL>")
                .build();

        String user_already_exists = "User already exists";

        doThrow(new IllegalArgumentException(user_already_exists))
                .when(userService)
                .validateUser(registerRequest.getUsername(), registerRequest.getEmail());

        // When
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> authenticationService.register(registerRequest)
        );

        // Then
        assertEquals(user_already_exists, exception.getMessage());
        verify(userRepository, never()).saveAndFlush(any(AppUser.class));
    }

    @Test
    void AuthenticationService_authenticate_userAuthenticatedSuccessfully() {
        // Given
        AuthenticationRequestDTO authRequest = AuthenticationRequestDTO.builder()
                .username("testUser")
                .password("testPassword")
                .build();

        AppUser user = AppUser.builder()
                .role(Role.HRBP)
                .build();

        String accessToken = "accessToken";
        String refreshToken = "refreshToken";

        Authentication auth = new UsernamePasswordAuthenticationToken(authRequest.getUsername(), authRequest.getPassword());
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenReturn(auth);

        when(userService.getUserByUsername(authRequest.getUsername())).thenReturn(user);
        when(jwtService.generateAccessToken(user)).thenReturn(accessToken);
        when(jwtService.generateRefreshToken(user)).thenReturn(refreshToken);

        // When
        AuthenticationResponseDTO response = authenticationService.authenticate(authRequest);

        // Then
        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(userService).getUserByUsername(authRequest.getUsername());
        verify(jwtService).generateAccessToken(user);
        verify(jwtService).generateRefreshToken(user);
        verify(userService).revokeAllUserTokens(user);
        verify(userService).saveUserVerificationToken(user, accessToken);

        assertEquals(accessToken, response.getAccessToken());
        assertEquals(refreshToken, response.getRefreshToken());
        assertEquals(Role.HRBP, response.getRole());
    }

    @Test
    void AuthenticationService_authenticate_InvalidCredentials() {
        // When
        AuthenticationRequestDTO authRequest = AuthenticationRequestDTO.builder()
                .username("testUser")
                .password("testPassword")
                .build();

        String bad_credentials = "Bad credentials";

        doThrow(new RuntimeException(bad_credentials))
                .when(authenticationManager)
                .authenticate(any(UsernamePasswordAuthenticationToken.class));

        // Given
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> authenticationService.authenticate(authRequest));

        // Then
        assertEquals(bad_credentials, exception.getMessage());
        verify(userService, never()).getUserByUsername(anyString());
        verify(jwtService, never()).generateAccessToken(any(AppUser.class));
    }

    @Test
    void AuthenticationService_refreshToken_InvalidToken() throws IOException {
        // Given
        String invalidRefreshToken = "invalidRefreshToken";
        String username = "testUser";

        AppUser user = AppUser.builder()
                .role(Role.HRBP)
                .build();

        when(request.getHeader(HttpHeaders.AUTHORIZATION)).thenReturn("Bearer " + invalidRefreshToken);
        when(jwtService.extractUsername(invalidRefreshToken)).thenReturn(username);
        when(userService.getUserByUsername(username)).thenReturn(user);
        when(jwtService.isTokenValid(invalidRefreshToken, user)).thenReturn(false);

        // When
        authenticationService.refreshToken(request, response);

        // Then
        verify(userService, never()).revokeAllUserTokens(user);
        verify(jwtService, never()).generateAccessToken(user);
        verify(objectMapper, never()).writeValue(isA(ServletOutputStream.class), isA(AuthenticationResponseDTO.class));
    }

    @Test
    void AuthenticationService_refreshToken_TokenRefreshedSuccessfully() throws IOException {
        // Given
        String username = "testUser";
        String validRefreshToken = "validRefreshToken";
        String newAccessToken = "newAccessToken";

        AppUser user = AppUser.builder()
                .role(Role.HRBP)
                .build();

        when(request.getHeader(HttpHeaders.AUTHORIZATION)).thenReturn("Bearer " + validRefreshToken);
        when(jwtService.extractUsername(validRefreshToken)).thenReturn(username);
        when(userService.getUserByUsername(username)).thenReturn(user);
        when(jwtService.isTokenValid(validRefreshToken, user)).thenReturn(true);
        when(jwtService.generateAccessToken(user)).thenReturn(newAccessToken);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When
        authenticationService.refreshToken(request, response);

        // Then
        verify(jwtService).extractUsername(validRefreshToken);
        verify(userService).getUserByUsername(username);
        verify(jwtService).isTokenValid(validRefreshToken, user);
        verify(userService).revokeAllUserTokens(user);
        verify(userService).saveUserVerificationToken(user, newAccessToken);
        //TODO: mocking ObjectMapper doesn't work
        //verify(objectMapper).writeValue(response.getOutputStream(), expectedResponse);
    }

    @Test
    void AuthenticationService_refreshToken_InvalidAuthorizationHeader() {
        // Given
        when(request.getHeader(HttpHeaders.AUTHORIZATION)).thenReturn(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () ->
                authenticationService.refreshToken(request, response));
        verify(jwtService, never()).extractUsername(anyString());
    }

    @Test
    void AuthenticationService_verifyEmail_ThrowsIllegalArgumentExceptionCausedByBlankCode() {
        // Given
        String blankCode = " ";

        when(messageSourceUtil.getMessage("exception.verification-code.blank"))
                .thenReturn("Verification code is blank");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            authenticationService.verifyEmail(blankCode);
        });
        assertEquals("Verification code is blank", exception.getMessage());
        verify(messageSourceUtil).getMessage("exception.verification-code.blank");
    }

    @Test
    void testVerifyEmail_verifyEmail_ThrowsIllegalStateExceptionCausedByUserAlreadyEnabled() {
        // Given
        String validToken = "validToken";
        String validCode = "validCode";

        AppUser user = AppUser.builder()
                .isEnabled(true)
                .role(Role.HRBP)
                .build();

        VerificationToken verificationToken = VerificationToken.builder()
                .user(user)
                .build();

        when(tokenService.getTokenByCode(validCode)).thenReturn(validToken);
        when(tokenService.getVerificationTokenByToken(validToken)).thenReturn(verificationToken);
        when(userService.getUserByUsername(user.getUsername())).thenReturn(user);
        when(messageSourceUtil.getMessage("user.email-already-verified")).thenReturn("User email already verified");

        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            authenticationService.verifyEmail(validCode);
        });

        assertEquals("User email already verified", exception.getMessage());
        verify(messageSourceUtil).getMessage("user.email-already-verified");
    }

    @Test
    void testVerifyEmail_verifyEmail_ThrowsExpiredJwtExceptionCausedByTokenExpired() {
        // Given
        String validToken = "validToken";
        String validCode = "validCode";

        AppUser user = AppUser.builder()
                .role(Role.HRBP)
                .build();

        VerificationToken verificationToken = VerificationToken.builder()
                .user(user)
                .build();

        when(tokenService.getTokenByCode(validCode)).thenReturn(validToken);
        when(tokenService.getVerificationTokenByToken(validToken)).thenReturn(verificationToken);
        when(userService.getUserByUsername(user.getUsername())).thenReturn(user);
        when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(false);
        when(messageSourceUtil.getMessage("verification-token.invalid")).thenReturn("Token is invalid or expired");

        // When
        ExpiredJwtException exception = assertThrows(ExpiredJwtException.class, () ->
                authenticationService.verifyEmail(validCode));

        // Then
        assertEquals("Token is invalid or expired", exception.getMessage());
        verify(messageSourceUtil).getMessage("verification-token.invalid");
    }

    @Test
    void testVerifyEmail_verifyEmail_UserEnabledSuccessfully() {
        // Given
        String validToken = "validToken";
        String validCode = "validCode";

        AppUser user = AppUser.builder()
                .username("username")
                .role(Role.HRBP)
                .build();

        VerificationToken verificationToken = VerificationToken.builder()
                .user(user)
                .build();

        when(tokenService.getTokenByCode(validCode)).thenReturn(validToken);
        when(tokenService.getVerificationTokenByToken(validToken)).thenReturn(verificationToken);
        when(userService.getUserByUsername(user.getUsername())).thenReturn(user);
        when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(true);

        // When
        authenticationService.verifyEmail(validCode);

        // Then
        assertTrue(user.isEnabled());
        verify(userRepository).save(user);
    }
}