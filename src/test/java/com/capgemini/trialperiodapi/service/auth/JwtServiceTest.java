package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "app.jwt-secret-key=e0c352481de147d859946af7610bbe1c1626d4e939d828295977491197b67914",
        "app.access-token-expiration=86400000",
        "app.refresh-token-expiration=604800000"
})
class JwtServiceTest {
    @InjectMocks
    private JwtService jwtService;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private MessageSource messageSource;

    private String secretKey = "e0c352481de147d859946af7610bbe1c1626d4e939d828295977491197b67914";
    private long accessTokenExpiration = 1000 * 60 * 15; // 15 minutes
    private long refreshTokenExpiration = 1000 * 60 * 60 * 24 * 7; // 7 days

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(jwtService, "secretKey", secretKey);
        ReflectionTestUtils.setField(jwtService, "accessTokenExpiration", accessTokenExpiration);
        ReflectionTestUtils.setField(jwtService, "refreshTokenExpiration", refreshTokenExpiration);
        messageSourceUtil = new MessageSourceUtil(messageSource);
    }

    @Test
    void JwtService_generateAccessToken_AccessTokenGeneratedSuccessfully() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        // When
        String token = jwtService.generateAccessToken(userDetails);

        // Then
        assertNotNull(token);
        verify(userDetails, times(3)).getUsername();
    }

    @Test
    void JwtService_generateRefreshToken_RefreshTokenGeneratedSuccessfully() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        // When
        String refreshToken = jwtService.generateRefreshToken(userDetails);

        // Then
        assertNotNull(refreshToken);
        verify(userDetails, times(3)).getUsername();
    }

    @Test
    void JwtService_isTokenValid_ValidToken() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        String token = jwtService.generateAccessToken(userDetails);

        // When
        boolean isValid = jwtService.isTokenValid(token, userDetails);

        // Then
        assertTrue(isValid);
        verify(userDetails, times(5)).getUsername();
    }

    @Test
    void JwtService_isTokenValid_ExpiredToken() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        String expiredToken = Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24)) // issued 1 day ago
                .setExpiration(new Date(System.currentTimeMillis() - 1000 * 60 * 60)) // expired 1 hour ago
                .signWith(Keys.hmacShaKeyFor(secretKey.getBytes()), SignatureAlgorithm.HS256)
                .compact();

        // When
        boolean isValid = jwtService.isTokenValid(expiredToken, userDetails);

        // Then
        assertFalse(isValid);
        verify(userDetails, times(2)).getUsername();
    }

    @Test
    void JwtService_isTokenExpired_ExpiredToken() {
        // Given
        String expiredToken = Jwts.builder()
                .setSubject("testuser")
                .setIssuedAt(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24)) // issued 1 day ago
                .setExpiration(new Date(System.currentTimeMillis() - 1000 * 60 * 60)) // expired 1 hour ago
                .signWith(Keys.hmacShaKeyFor(secretKey.getBytes()), SignatureAlgorithm.HS256)
                .compact();

        // When & Then
        assertThrows(IllegalStateException.class, () -> jwtService.isTokenExpired(expiredToken));
    }

    @Test
    void JwtService_isTokenExpired_ValidToken() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        String token = jwtService.generateAccessToken(userDetails);

        // When
        boolean isExpired = jwtService.isTokenExpired(token);

        // Then
        assertFalse(isExpired);
    }

    @Test
    void JwtService_isTokenValid_ExpiredJwtExceptionThrown() {
        // Given
        String expiredToken = Jwts.builder()
                .setSubject("testuser")
                .setIssuedAt(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24)) // issued 1 day ago
                .setExpiration(new Date(System.currentTimeMillis() - 1000 * 60 * 60)) // expired 1 hour ago
                .signWith(Keys.hmacShaKeyFor(secretKey.getBytes()), SignatureAlgorithm.HS256)
                .compact();

        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        // When
        boolean isValid = jwtService.isTokenValid(expiredToken, userDetails);

        // Then
        assertFalse(isValid);
    }

    @Test
    void JwtService_extractUsername_UsernameExtractedSuccessfully() {
        // Given
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetails.getUsername()).thenReturn("testuser");

        String token = jwtService.generateAccessToken(userDetails);

        // When
        String username = jwtService.extractUsername(token);

        // Then
        assertEquals("testuser", username);
    }
}