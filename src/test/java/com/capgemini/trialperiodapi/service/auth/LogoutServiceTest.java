package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LogoutServiceTest {

    @InjectMocks
    private LogoutService logoutService;

    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Test
    void LogoutService_logout_RevokesToken() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Authentication authentication = mock(Authentication.class);

        String jwt = "valid-jwt-token";
        when(request.getHeader("Authorization")).thenReturn("Bearer " + jwt);
        VerificationToken storedToken = new VerificationToken();
        when(verificationTokenRepository.findVerificationTokenByToken(jwt)).thenReturn(Optional.of(storedToken));

        logoutService.logout(request, response, authentication);

        verify(verificationTokenRepository).save(argThat(argument -> argument == storedToken && argument.isRevoked() && argument.isExpired()));
    }

    @Test
    void LogoutService_logout_TokenNotFound() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Authentication authentication = mock(Authentication.class);
        String jwt = "valid-jwt-token";

        when(request.getHeader("Authorization")).thenReturn("Bearer " + jwt);
        when(verificationTokenRepository.findVerificationTokenByToken(anyString())).thenReturn(Optional.empty());
        when(messageSourceUtil.getMessage("verification-token.not-found"))
                .thenReturn("Le jeton de vérification est introuvable");

        assertThrows(EntityNotFoundException.class,
                () -> logoutService.logout(request, response, authentication));
    }

    @Test
    void LogoutService_logout_SkipsIfNoAuthorizationHeader() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Authentication authentication = mock(Authentication.class);

        when(request.getHeader("Authorization")).thenReturn(null);

        logoutService.logout(request, response, authentication);

        verify(verificationTokenRepository, never()).findVerificationTokenByToken(anyString());
        verify(verificationTokenRepository, never()).save(any());
    }

    @Test
    void LogoutService_logout_SkipsIfInvalidAuthorizationHeader() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Authentication authentication = mock(Authentication.class);

        when(request.getHeader("Authorization")).thenReturn("InvalidHeaderFormat");

        logoutService.logout(request, response, authentication);

        verify(verificationTokenRepository, never()).findVerificationTokenByToken(anyString());
        verify(verificationTokenRepository, never()).save(any());
    }
}