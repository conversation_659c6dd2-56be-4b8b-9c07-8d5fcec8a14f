package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.dto.request.ChangePasswordRequestDTO;
import com.capgemini.trialperiodapi.dto.request.ResetPasswordRequestDTO;
import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.model.auth.PasswordResetToken;
import com.capgemini.trialperiodapi.repository.PasswordResetTokenRepository;
import com.capgemini.trialperiodapi.repository.UserRepository;
import com.capgemini.trialperiodapi.service.EmailService;
import com.capgemini.trialperiodapi.service.UserService;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.TestPropertySource;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@TestPropertySource(properties = {
        "app.frontend-reset-password-url=http://localhost:4200/reset-password"
})
class PasswordServiceTest {

    @Value("${app.frontend-reset-password-url}")
    private String resetPasswordUrl;

    @InjectMocks
    private PasswordService passwordService;

    @Mock
    PasswordResetTokenRepository passwordTokenRepository;

    @Mock
    VerificationTokenService tokenService;

    @Mock
    MessageSourceUtil messageSourceUtil;

    @Mock
    PasswordEncoder passwordEncoder;

    @Mock
    UserRepository userRepository;

    @Mock
    EmailService emailService;

    @Mock
    UserService userService;

    @Mock
    private AppUser appUser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        passwordService = new PasswordService(passwordTokenRepository, tokenService, messageSourceUtil, passwordEncoder, userRepository, emailService, userService);
    }


    @Test
    void PasswordService_changePassword_Success() {
        // Given
        AppUser user = mock(AppUser.class);

        ChangePasswordRequestDTO passwordRequest = new ChangePasswordRequestDTO();
        passwordRequest.setCurrentPassword("oldPassword");
        passwordRequest.setNewPassword("newPassword");
        passwordRequest.setConfirmPassword("newPassword");

        Authentication authentication = new UsernamePasswordAuthenticationToken(user, null);

        when(user.getPassword()).thenReturn("oldPasswordHash");
        when(passwordEncoder.matches("oldPassword", "oldPasswordHash")).thenReturn(true);
        when(passwordEncoder.matches("newPassword", "oldPasswordHash")).thenReturn(false);
        when(passwordEncoder.encode("newPassword")).thenReturn("newPasswordHash");

        // When
        passwordService.changePassword(passwordRequest, authentication);

        // Then
        verify(user).setPassword("newPasswordHash");
        verify(userRepository).save(user);
    }

    @Test
    void PasswordService_changePassword_WrongCurrentPassword() {
        // Given
        AppUser user = mock(AppUser.class);

        ChangePasswordRequestDTO passwordRequest = new ChangePasswordRequestDTO();
        passwordRequest.setCurrentPassword("wrongPassword");
        passwordRequest.setNewPassword("newPassword");
        passwordRequest.setConfirmPassword("newPassword");

        Authentication authentication = new UsernamePasswordAuthenticationToken(user, null);

        when(user.getPassword()).thenReturn("oldPasswordHash");
        when(passwordEncoder.matches("wrongPassword", "oldPasswordHash")).thenReturn(false);
        when(messageSourceUtil.getMessage("password.wrong")).thenReturn("Wrong password");

        // When & Then
        BadCredentialsException exception = assertThrows(BadCredentialsException.class, () ->
                passwordService.changePassword(passwordRequest, authentication));
        assertEquals("Wrong password", exception.getMessage());
    }

    @Test
    void PasswordService_changePassword_PasswordsNotMatching() {
        // Given
        ChangePasswordRequestDTO passwordRequest = new ChangePasswordRequestDTO();
        passwordRequest.setCurrentPassword("oldPassword");
        passwordRequest.setNewPassword("newPassword");
        passwordRequest.setConfirmPassword("differentPassword");

        AppUser user = mock(AppUser.class);

        Authentication authentication = new UsernamePasswordAuthenticationToken(user, null);

        when(user.getPassword()).thenReturn("oldPassword");
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(messageSourceUtil.getMessage("password.not-match")).thenReturn("Password not match");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                passwordService.changePassword(passwordRequest, authentication));
        assertEquals("Password not match", exception.getMessage());
    }

    @Test
    void PasswordService_changePassword_SimilarUpdatedPassword() {
        // Given
        ChangePasswordRequestDTO passwordRequest = new ChangePasswordRequestDTO();
        passwordRequest.setCurrentPassword("oldPassword");
        passwordRequest.setNewPassword("oldPassword");
        passwordRequest.setConfirmPassword("oldPassword");

        AppUser user = mock(AppUser.class);

        Authentication authentication = new UsernamePasswordAuthenticationToken(user, null);

        when(user.getPassword()).thenReturn("oldPassword");
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(messageSourceUtil.getMessage("password.similar-update")).thenReturn("Old password is the same as the new one");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                passwordService.changePassword(passwordRequest, authentication));
        assertEquals("Old password is the same as the new one", exception.getMessage());
    }

    @Test
    void PasswordService_forgotPassword_Succeed() {
        // Given
        AppUser user = new AppUser();
        user.setEmail("<EMAIL>");
        user.setUsername("testUser");

        when(userService.getUserByEmail("<EMAIL>")).thenReturn(user);
        when(tokenService.generateActivationCode(6)).thenReturn("123456");
        when(messageSourceUtil.getMessage("mail.subject.reset-password")).thenReturn("Reset password subject");

        // When
        passwordService.forgotPassword("<EMAIL>", "sessionPassword");

        // Then
        verify(emailService).sendForgotPasswordEmail(
                eq("<EMAIL>"),
                eq("testUser"),
                eq(resetPasswordUrl),
                eq("123456"),
                anyString(),
                eq("sessionPassword")
        );
    }

    @Test
    void PasswordService_forgotPassword_EmailNotFound() {
        // Given
        String email = "<EMAIL>";

        when(userService.getUserByEmail(email)).thenReturn(null);
        when(messageSourceUtil.getMessageWithObject("user.by-email.not-found", email)).thenReturn("Email not found");

        // When & Then
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                passwordService.forgotPassword(email, "sessionPassword"));
        assertEquals("Email not found", exception.getMessage());
    }

    @Test
    void PasswordService_forgotPassword_BlankEmail() {
        // Given
        String email = "";

        when(userService.getUserByEmail(email)).thenReturn(null);
        when(messageSourceUtil.getMessage("password.forgot.required-email")).thenReturn("Email is blank");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                passwordService.forgotPassword(email, "sessionPassword"));
        assertEquals("Email is blank", exception.getMessage());
    }

    @Test
    void PasswordService_generatePasswordResetTokenForUser_NewTokenGenerated() {
        // Given
        AppUser user = new AppUser();
        user.setId(1L);

        when(passwordTokenRepository.findPasswordResetTokenByUserId(1L)).thenReturn(Optional.empty());
        when(tokenService.generateActivationCode(6)).thenReturn("123456");

        // When
        String token = passwordService.generatePasswordResetTokenForUser(user);

        // Then
        assertEquals("123456", token);
        verify(passwordTokenRepository).save(any(PasswordResetToken.class));
    }

    @Test
    void PasswordService_generatePasswordResetTokenForUser_UpdateExistingToken() {
        // Given
        AppUser user = new AppUser();
        user.setId(1L);

        PasswordResetToken oldToken = new PasswordResetToken();
        oldToken.setExpirationTime(new Date());
        oldToken.setUser(user);

        when(passwordTokenRepository.findPasswordResetTokenByUserId(1L)).thenReturn(Optional.of(oldToken));
        when(tokenService.generateActivationCode(6)).thenReturn("123456");

        // When
        String token = passwordService.generatePasswordResetTokenForUser(user);

        // Then
        assertEquals("123456", token);
        verify(passwordTokenRepository).save(any(PasswordResetToken.class));
    }

    @Test
    void PasswordService_resetPassword_Success() {
        // Given
        String oldPasswordHash = "oldPasswordHash";
        String newPasswordHash = "newPasswordHash";
        String newPassword = "newPassword";
        String validCode = "validCode";

        AppUser userMock = mock(AppUser.class);
        userMock.setPassword(oldPasswordHash);

        Date validExpirationDate = new Date(System.currentTimeMillis() + 3600000);

        PasswordResetToken token = new PasswordResetToken();
        token.setExpirationTime(validExpirationDate);
        token.setUser(userMock);

        ResetPasswordRequestDTO passwordRequest = new ResetPasswordRequestDTO();
        passwordRequest.setNewPassword(newPassword);
        passwordRequest.setConfirmPassword(newPassword);

        when(passwordTokenRepository.findPasswordResetTokenByCode(validCode)).thenReturn(token);
        when(passwordEncoder.matches(newPassword, oldPasswordHash)).thenReturn(false);
        when(passwordEncoder.encode(newPassword)).thenReturn(newPasswordHash);

        // When
        passwordService.resetPassword(passwordRequest, validCode);

        // Then
        verify(userMock).setPassword(newPasswordHash);
        verify(userRepository).save(userMock);
    }

    @Test
    void PasswordService_resetPassword_BlankCode() {
        // Given,
        String blankCode = "";

        // When & Then
        assertThrows(IllegalArgumentException.class, () ->
                passwordService.resetPassword(any(ResetPasswordRequestDTO.class), blankCode));
    }

    @Test
    void PasswordService_resetPassword_TokenExpired() {
        // Given
        PasswordResetToken token = mock(PasswordResetToken.class);

        when(token.getExpirationTime()).thenReturn(new Date(System.currentTimeMillis() - 10000)); // expired
        when(passwordTokenRepository.findPasswordResetTokenByCode("expiredCode")).thenReturn(token);
        when(messageSourceUtil.getMessage("password-token.expired")).thenReturn("Password token expired");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                passwordService.resetPassword(new ResetPasswordRequestDTO(), "expiredCode"));
        assertEquals("Password token expired", exception.getMessage());
    }

    @Test
    void PasswordService_resetPassword_SimilarUpdatedPassword() {
        // Given
        String code = "code";
        Date validExpirationDate = new Date(System.currentTimeMillis() + 3600000);
        String similarPassword = "similarPassword";

        AppUser user = new AppUser();
        user.setPassword(similarPassword);

        ResetPasswordRequestDTO passwordRequest = new ResetPasswordRequestDTO();
        passwordRequest.setNewPassword(similarPassword);
        passwordRequest.setConfirmPassword(similarPassword);

        PasswordResetToken token = new PasswordResetToken();
        token.setExpirationTime(validExpirationDate);
        token.setUser(user);

        when(passwordTokenRepository.findPasswordResetTokenByCode(code)).thenReturn(token);
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(messageSourceUtil.getMessage("password.similar-update")).thenReturn("Old password is the same as the new one");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                passwordService.resetPassword(passwordRequest, code));
        assertEquals("Old password is the same as the new one", exception.getMessage());
    }

    @Test
    void PasswordService_resetPassword_PasswordNotMatch() {
        // Given
        String code = "code";
        Date validExpirationDate = new Date(System.currentTimeMillis() + 3600000);

        ResetPasswordRequestDTO passwordRequest = new ResetPasswordRequestDTO();
        passwordRequest.setNewPassword("newPassword");
        passwordRequest.setConfirmPassword("newNotMatchPassword");

        PasswordResetToken token = new PasswordResetToken();
        token.setExpirationTime(validExpirationDate);

        when(passwordTokenRepository.findPasswordResetTokenByCode(code)).thenReturn(token);
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(messageSourceUtil.getMessage("password.not-match")).thenReturn("Password not match");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                passwordService.resetPassword(passwordRequest, code));
        assertEquals("Password not match", exception.getMessage());
    }

    @Test
    void PasswordService_getValidPasswordResetTokenByCode_CodeIsNull() {
        // Given
        String code = null;
        String exceptionMessage = "Verification code is blank";
        
        // When
        when(messageSourceUtil.getMessage("exception.verification-code.blank")).thenReturn(exceptionMessage);

        // Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                passwordService.getValidPasswordResetTokenByCode(code));
        assertEquals(exceptionMessage, exception.getMessage());
    }

    @Test
    void PasswordService_getValidPasswordResetTokenByCode_TokenNotFound() {
        // Given
        when(passwordTokenRepository.findPasswordResetTokenByCode("invalidCode")).thenReturn(null);
        when(messageSourceUtil.getMessage("password-token.not-found")).thenReturn("Password token not found");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                passwordService.getValidPasswordResetTokenByCode("invalidCode"));
        assertEquals("Password token not found", exception.getMessage());
    }

    @Test
    void PasswordService_getValidPasswordResetTokenByCode_TokenExpired() {
        // Given
        PasswordResetToken token = mock(PasswordResetToken.class);
        when(passwordTokenRepository.findPasswordResetTokenByCode("validCode")).thenReturn(token);
        when(token.getExpirationTime()).thenReturn(new Date(System.currentTimeMillis() - 10000)); // expired
        when(messageSourceUtil.getMessage("password-token.expired")).thenReturn("Password token is expired");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                passwordService.getValidPasswordResetTokenByCode("validCode"));
        assertEquals("Password token is expired", exception.getMessage());
    }

    @Test
    void PasswordService_getValidPasswordResetTokenByCode_Succeed() {
        // Given
        PasswordResetToken token = mock(PasswordResetToken.class);
        when(passwordTokenRepository.findPasswordResetTokenByCode("validCode")).thenReturn(token);
        when(token.getExpirationTime()).thenReturn(new Date(System.currentTimeMillis() + 10000));

        // When
        PasswordResetToken result = passwordService.getValidPasswordResetTokenByCode("validCode");

        // Then
        assertNotNull(result);
        assertEquals(token, result);
    }

    @Test
    void PasswordService_isPasswordExpired_PrincipalNull() {
        // Given
        Principal principal = null;
        
        // When
        boolean passwordExpired = passwordService.isPasswordExpired(principal);
        
        // Then
        assertFalse(passwordExpired);
    }

    @Test
    void PasswordService_isPasswordExpired_PasswordExpired() {
        // Given
        UsernamePasswordAuthenticationToken authenticationToken = mock(UsernamePasswordAuthenticationToken.class);
        when(authenticationToken.getPrincipal()).thenReturn(appUser);
        when(appUser.getLastPasswordUpdatedOn()).thenReturn(LocalDateTime.now().minusDays(100));

        // When
        boolean result = passwordService.isPasswordExpired(authenticationToken);

        // Then
        assertTrue(result); // Must return true because password expired (more Then 90 days)
    }

    @Test
    void PasswordService_isPasswordExpired_PasswordNotExpired() {
        // Given
        UsernamePasswordAuthenticationToken authenticationToken = mock(UsernamePasswordAuthenticationToken.class);
        when(authenticationToken.getPrincipal()).thenReturn(appUser);
        when(appUser.getLastPasswordUpdatedOn()).thenReturn(LocalDateTime.now().minusDays(60));

        // When
        boolean result = passwordService.isPasswordExpired(authenticationToken);

        // Then
        assertFalse(result);
    }

    @Test
    void PasswordService_isPasswordExpiringSoon_PasswordExpiringSoon() {
        // Given
        UsernamePasswordAuthenticationToken authenticationToken = mock(UsernamePasswordAuthenticationToken.class);
        when(authenticationToken.getPrincipal()).thenReturn(appUser);
        when(appUser.getLastPasswordUpdatedOn()).thenReturn(LocalDateTime.now().minusDays(80));

        // When
        boolean result = passwordService.isPasswordExpiringSoon(authenticationToken);

        // Then
        assertTrue(result); // Must return true as password expires soon (more Then 75 days)
    }
}