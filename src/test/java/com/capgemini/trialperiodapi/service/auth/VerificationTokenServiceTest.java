package com.capgemini.trialperiodapi.service.auth;

import com.capgemini.trialperiodapi.model.auth.VerificationToken;
import com.capgemini.trialperiodapi.repository.VerificationTokenRepository;
import com.capgemini.trialperiodapi.util.MessageSourceUtil;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class VerificationTokenServiceTest {
    @Mock
    private VerificationTokenRepository verificationTokenRepository;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @InjectMocks
    private VerificationTokenService verificationTokenService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void VerificationTokenServiceTest_getVerificationTokenByToken_TokenNotFound() {
        when(verificationTokenRepository.findVerificationTokenByToken("invalidToken"))
                .thenReturn(Optional.empty());
        when(messageSourceUtil.getMessage("verification-token.not-found"))
                .thenReturn("Verification token not found");

        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () -> {
            verificationTokenService.getVerificationTokenByToken("invalidToken");
        });

        assertEquals("Verification token not found", exception.getMessage());
        verify(verificationTokenRepository).findVerificationTokenByToken("invalidToken");
    }

    @Test
    void VerificationTokenServiceTest_getVerificationTokenByToken_TokenFound() {
        VerificationToken token = new VerificationToken();
        when(verificationTokenRepository.findVerificationTokenByToken("validToken"))
                .thenReturn(Optional.of(token));

        VerificationToken result = verificationTokenService.getVerificationTokenByToken("validToken");

        assertNotNull(result);
        assertEquals(token, result);
        verify(verificationTokenRepository).findVerificationTokenByToken("validToken");
    }

    @Test
    void VerificationTokenServiceTest_getTokenByCode_CodeNotFound() {
        when(verificationTokenRepository.findVerificationTokenByCode("invalidCode"))
                .thenReturn(Optional.empty());
        when(messageSourceUtil.getMessage("verification-token.not-found"))
                .thenReturn("Verification token not found");

        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () -> {
            verificationTokenService.getTokenByCode("invalidCode");
        });

        assertEquals("Verification token not found", exception.getMessage());
        verify(verificationTokenRepository).findVerificationTokenByCode("invalidCode");
    }

    @Test
    void VerificationTokenServiceTest_getTokenByCode_CodeFound() {
        VerificationToken token = new VerificationToken();
        token.setToken("validToken");
        when(verificationTokenRepository.findVerificationTokenByCode("validCode"))
                .thenReturn(Optional.of(token));

        String result = verificationTokenService.getTokenByCode("validCode");

        assertEquals("validToken", result);
        verify(verificationTokenRepository).findVerificationTokenByCode("validCode");
    }

    @Test
    void VerificationTokenServiceTest_getCodeByToken_TokenNotFound() {
        when(verificationTokenRepository.findVerificationTokenByToken("invalidToken"))
                .thenReturn(Optional.empty());
        when(messageSourceUtil.getMessage("verification-token.not-found"))
                .thenReturn("Verification token not found");

        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () -> {
            verificationTokenService.getCodeByToken("invalidToken");
        });

        assertEquals("Verification token not found", exception.getMessage());
        verify(verificationTokenRepository).findVerificationTokenByToken("invalidToken");
    }

    @Test
    void VerificationTokenServiceTest_getCodeByToken_TokenFound() {
        VerificationToken token = new VerificationToken();
        token.setCode("validCode");
        when(verificationTokenRepository.findVerificationTokenByToken("validToken"))
                .thenReturn(Optional.of(token));

        String result = verificationTokenService.getCodeByToken("validToken");

        assertEquals("validCode", result);
        verify(verificationTokenRepository).findVerificationTokenByToken("validToken");
    }

    @Test
    void VerificationTokenServiceTest_generateActivationCode_Succeed() {
        int length = 6;
        String generatedCode = verificationTokenService.generateActivationCode(length);

        assertNotNull(generatedCode);
        assertEquals(length, generatedCode.length());
        assertTrue(generatedCode.matches("\\d+"));
    }
}