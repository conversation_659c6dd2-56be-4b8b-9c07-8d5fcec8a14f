package com.capgemini.trialperiodapi.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;

class DateUtilTest {
    @InjectMocks
    private DateUtil dateUtil;

    private DateTimeFormatter dateFormatter;
    private DateTimeFormatter dateTimeFormatter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    }

    @Test
    void testParseDate_ValidDate() {
        String dateStr = "2024-10-23";
        LocalDate result = dateUtil.parseDate(dateStr, dateFormatter);
        assertNotNull(result);
        assertEquals(LocalDate.of(2024, 10, 23), result);
    }

    @Test
    void testParseDate_EmptyString() {
        String dateStr = "";
        LocalDate result = dateUtil.parseDate(dateStr, dateFormatter);
        assertNull(result);
    }

    @Test
    void testParseDate_NullString() {
        LocalDate result = dateUtil.parseDate(null, dateFormatter);
        assertNull(result);
    }

    @Test
    void testParseDate_InvalidDateFormat() {
        String dateStr = "23-10-2024"; // Mauvais format
        assertThrows(DateTimeParseException.class, () -> dateUtil.parseDate(dateStr, dateFormatter));
    }

    @Test
    void testParseDateTime_ValidDateTime() {
        String dateTimeStr = "2024-10-23T12:00:00";
        LocalDateTime result = dateUtil.parseDateTime(dateTimeStr, dateTimeFormatter);
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2024, 10, 23, 12, 0), result);
    }

    @Test
    void testParseDateTime_ValidDate() {
        String dateStr = "2024-10-23";
        LocalDateTime result = dateUtil.parseDateTime(dateStr, dateFormatter);
        assertNotNull(result);
        assertEquals(LocalDate.of(2024, 10, 23).atStartOfDay(), result);
    }

    @Test
    void testParseDateTime_EmptyString() {
        String dateStr = "";
        LocalDateTime result = dateUtil.parseDateTime(dateStr, dateTimeFormatter);
        assertNull(result);
    }

    @Test
    void testParseDateTime_InvalidDateFormat() {
        String dateStr = "invalid-date-time";
        LocalDateTime result = dateUtil.parseDateTime(dateStr, dateTimeFormatter);
        assertNull(result);  // Dans ce cas, la méthode attrape l'exception et renvoie null
    }

    @Test
    void testParseToLocalDate_ValidDate() {
        String dateStr = "2024-10-23";
        LocalDateTime result = dateUtil.parseToLocalDate(dateStr, dateFormatter);
        assertNotNull(result);
        assertEquals(LocalDate.of(2024, 10, 23).atStartOfDay(), result);
    }

    @Test
    void testParseToLocalDate_InvalidDate() {
        String dateStr = "invalid-date";
        LocalDateTime result = dateUtil.parseToLocalDate(dateStr, dateFormatter);
        assertNull(result); // Dans ce cas, la méthode attrape l'exception et renvoie null
    }

    @Test
    void testParseToLocalDateTime_ValidDateTime() {
        String dateTimeStr = "2024-10-23T12:00:00";
        LocalDateTime result = dateUtil.parseToLocalDateTime(dateTimeStr, dateTimeFormatter);
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2024, 10, 23, 12, 0), result);
    }

    @Test
    void testParseToLocalDateTime_InvalidDateTime() {
        String dateTimeStr = "invalid-date-time";
        LocalDateTime result = dateUtil.parseToLocalDateTime(dateTimeStr, dateTimeFormatter);
        assertNull(result); // Dans ce cas, la méthode attrape l'exception et renvoie null
    }
}