package com.capgemini.trialperiodapi.util;

import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class FileUtilTest {
    @InjectMocks
    private FileUtil fileUtil;

    @Mock
    private MessageSourceUtil messageUtil;

    @Mock
    private MultipartFile multipartFile;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private void mockMessageUtil(String key, String message) {
        when(messageUtil.getMessage(key)).thenReturn(message);
    }

    @Test
    void FileUtil_checkFileExtension_ValidCsvExtension() {
        when(multipartFile.getOriginalFilename()).thenReturn("file.csv");

        fileUtil.checkFileExtension(multipartFile);

        verify(multipartFile).getOriginalFilename(); // Vérifie que le fichier est bien vérifié
    }

    @Test
    void FileUtil_checkFileExtension_InvalidExtension() {
        when(multipartFile.getOriginalFilename()).thenReturn("file.txt");
        mockMessageUtil("exception.file.csv-format", "Invalid file format");

        assertThrows(IllegalArgumentException.class, () -> fileUtil.checkFileExtension(multipartFile));

        verify(multipartFile).getOriginalFilename();
    }

    @Test
    void FileUtil_checkFileExtension_NoExtension() {
        when(multipartFile.getOriginalFilename()).thenReturn("file");
        mockMessageUtil("exception.file.csv-format", "Invalid file format");

        assertThrows(IllegalArgumentException.class, () -> fileUtil.checkFileExtension(multipartFile));

        verify(multipartFile).getOriginalFilename();
    }

    @Disabled("match.getName() return ISO")
    void FileUtil_checkFileEncoding_ValidUtf8Encoding() throws Exception {
        byte[] fileBytes = "test content".getBytes(StandardCharsets.UTF_8);
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(fileBytes));

        CharsetMatch charsetMatch = mock(CharsetMatch.class);
        when(charsetMatch.getName()).thenReturn(StandardCharsets.UTF_8.name());

        CharsetDetector detector = mock(CharsetDetector.class);
        when(detector.detect()).thenReturn(charsetMatch);

        fileUtil.checkFileEncoding(multipartFile);

        verify(multipartFile).getInputStream();
    }

    @Test
    void FileUtil_checkFileEncoding_InvalidEncoding() throws Exception {
        byte[] fileBytes = "test content".getBytes(StandardCharsets.UTF_8);
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(fileBytes));

        CharsetMatch charsetMatch = mock(CharsetMatch.class);
        when(charsetMatch.getName()).thenReturn("ISO-8859-1");

        CharsetDetector detector = mock(CharsetDetector.class);
        when(detector.detect()).thenReturn(charsetMatch);

        mockMessageUtil("exception.file.invalid-encoding", "Invalid encoding");

        assertThrows(IllegalArgumentException.class, () -> fileUtil.checkFileEncoding(multipartFile));

        verify(multipartFile).getInputStream();
    }

    @Test
    void FileUtil_checkFileEncoding_IOExceptionThrown() throws Exception {
        when(multipartFile.getInputStream()).thenThrow(new IOException("File read error"));

        mockMessageUtil("exception.file.encoding-validation", "File encoding validation failed");

        assertThrows(IllegalArgumentException.class, () -> fileUtil.checkFileEncoding(multipartFile));

        verify(multipartFile).getInputStream();
    }
}