package com.capgemini.trialperiodapi.util;

import com.capgemini.trialperiodapi.model.auth.AppUser;
import com.capgemini.trialperiodapi.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class StringUtilTest {

    @InjectMocks
    private StringUtil stringUtil;

    @Mock
    private MessageSourceUtil messageSourceUtil;

    @Mock
    private UserRepository userRepository;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    // Test pour extractEmailFromCsvColumn
    @Test
    void testExtractEmailFromCsvColumn_ValidEmail() {
        String line = "<PERSON> <<EMAIL>>";
        String result = stringUtil.extractEmailFromCsvColumn(line);
        assertEquals("<EMAIL>", result);
    }

    @Test
    void testExtractEmailFromCsvColumn_InvalidEmail() {
        String line = "Invalid Email";
        when(messageSourceUtil.getMessage("exception.file.extract-email-failed")).thenReturn("Email extraction failed");

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            stringUtil.extractEmailFromCsvColumn(line);
        });
        assertEquals("Email extraction failed", thrown.getMessage());
    }

    // Test pour extractFirstnameFromCsvColumn
    @Test
    void testExtractFirstnameFromCsvColumn_ValidInput() {
        String line = "Doe, John <<EMAIL>>";
        String result = stringUtil.extractFirstnameFromCsvColumn(line);
        assertEquals("John", result);
    }

    @Test
    void testExtractFirstnameFromCsvColumn_InvalidInput() {
        String line = "Invalid Name";
        when(messageSourceUtil.getMessage("exception.file.extract-first-name-failed")).thenReturn("First name extraction failed");

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            stringUtil.extractFirstnameFromCsvColumn(line);
        });
        assertEquals("First name extraction failed", thrown.getMessage());
    }

    // Test pour extractLastnameFromCsvColumn
    @Test
    void testExtractLastnameFromCsvColumn_ValidInput() {
        String line = "Doe, John <<EMAIL>>";
        String result = stringUtil.extractLastnameFromCsvColumn(line);
        assertEquals("Doe", result);
    }

    @Test
    void testExtractLastnameFromCsvColumn_InvalidInput() {
        String line = "Invalid Name";
        when(messageSourceUtil.getMessage("exception.file.extract-last-name-failed")).thenReturn("Last name extraction failed");

        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            stringUtil.extractLastnameFromCsvColumn(line);
        });
        assertEquals("Last name extraction failed", thrown.getMessage());
    }

    // Test pour generateUsernameFromFirstnameAndLastname
    @Test
    void testGenerateUsernameFromFirstnameAndLastname_UsernameDoesNotExist() {
        String firstname = "John";
        String lastname = "Doe";
        when(userRepository.findUserByUsername("jDoe")).thenReturn(Optional.empty());

        String result = stringUtil.generateUsernameFromFirstnameAndLastname(firstname, lastname);
        assertEquals("JDoe", result);
    }

    @Test
    void testGenerateUsernameFromFirstnameAndLastname_UsernameExists() {
        String firstname = "John";
        String lastname = "El Doe";
        when(userRepository.findUserByUsername(anyString())).thenReturn(Optional.of(new AppUser()));

        String result = stringUtil.generateUsernameFromFirstnameAndLastname(firstname, lastname);
        assertEquals("John.El-Doe", result);
    }

    // Tests pour extractFirstname and extractLastname
    @Test
    void testExtractFirstname_ValidInput() {
        String fullName = "Doe, John";
        String result = StringUtil.extractFirstname(fullName);
        assertEquals("John", result);
    }

    @Test
    void testExtractLastname_ValidInput() {
        String fullName = "Doe, John";
        String result = StringUtil.extractLastname(fullName);
        assertEquals("Doe", result);
    }
}