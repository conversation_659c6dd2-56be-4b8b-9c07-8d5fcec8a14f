package cucumber;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationManager;

public class RollbackTestExecutionListener extends AbstractTestExecutionListener {

        @Override
        public void beforeTestMethod(TestContext testContext) throws Exception {
            super.beforeTestMethod(testContext);
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                TransactionStatus transactionStatus = TransactionAspectSupport.currentTransactionStatus();
                transactionStatus.setRollbackOnly();
            }
        }

    @Override
    public void afterTestMethod(TestContext testContext) throws Exception {
        super.afterTestMethod(testContext);
        // Clean up the transaction after the test method execution
        TransactionSynchronizationManager.clear();
    }
    }
