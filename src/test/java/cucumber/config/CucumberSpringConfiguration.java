package cucumber.config;

import com.capgemini.trialperiodapi.TrialPeriodApiApplication;
import com.capgemini.trialperiodapi.service.auth.JwtService;
import io.cucumber.spring.CucumberContextConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Class to use spring application context while running cucumber
 */
@CucumberContextConfiguration
@SpringBootTest(classes = {TrialPeriodApiApplication.class, RestTemplateConfig.class, JwtService.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class CucumberSpringConfiguration {


}