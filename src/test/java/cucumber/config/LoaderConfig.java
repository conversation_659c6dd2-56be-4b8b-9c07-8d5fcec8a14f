package cucumber.config;

import cucumber.runner.CucumberTestRunner;
import org.springframework.test.context.ContextConfiguration;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
public class LoaderConfig {

    private static Properties properties = new Properties();

    static {
        try (InputStream input = new FileInputStream("src/test/resources/config.properties")) {
            properties.load(input);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    public static String getProperty(String key) {
        return properties.getProperty(key);
    }
}
