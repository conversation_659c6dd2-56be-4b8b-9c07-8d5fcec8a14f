package cucumber.config;

import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import cucumber.runner.CucumberTestRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
public class TestContext {

    @Autowired
    public static RestTemplate restTemplate=new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";

    private static String tokenContext;

    // Method to get the token, generating it if necessary
    public static String getTokenContext() {
        if (tokenContext == null) {
            try {
                tokenContext = generateTokenContext();
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return tokenContext;
    }

    // Simulated method to generate or fetch the tokenContext
    private static String generateTokenContext() throws JsonProcessingException {
        if (tokenContext == null || tokenContext.isEmpty()) {
            String url = BASE_URL + "/api/v1/auth/authenticate";
            String requestBody = "{ \"username\": \"\", \"password\": \"\" }";

            String adminUsername = LoaderConfig.getProperty("admin.username");
            String adminPassword = LoaderConfig.getProperty("admin.password");
            ObjectMapper objectMapper = new ObjectMapper();

            // Parse the JSON string into a JsonNode
            JsonNode jsonNode = objectMapper.readTree(requestBody);

            // Modify the attribute (e.g., "username")
            ((ObjectNode) jsonNode).put("username", adminUsername);
            // Modify the attribute (e.g., "password")
            ((ObjectNode) jsonNode).put("password", adminPassword);

            // Convert back to JSON string
            String newRequestBody = objectMapper.writeValueAsString(jsonNode);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            HttpEntity<String> request = new HttpEntity<>(newRequestBody, headers);
            try {
                ResponseEntity<AuthenticationResponseDTO> authResponse = restTemplate.postForEntity(url, request, AuthenticationResponseDTO.class);
                tokenContext = "Bearer " + authResponse.getBody().getAccessToken();
            } catch (HttpClientErrorException e) {
                e.printStackTrace();
            }
        }
        return tokenContext;
    }

}



