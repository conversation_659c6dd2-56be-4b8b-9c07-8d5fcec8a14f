package cucumber.steps;

import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.model.auth.Role;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.TestContext;
import cucumber.config.TransactionConfig;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.TransactionStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class, TransactionConfig.class})
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.properties")
public class AuthenticationSteps {
    private ResponseEntity<AuthenticationResponseDTO> responseAuthentication;
    private ResponseEntity<Void> response;
    private ResponseEntity<Role> responseRole;
    private HttpClientErrorException exception;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private TransactionStatus txStatus;

    private String extractToken(ResponseEntity<AuthenticationResponseDTO> responseBody) {
        return responseBody.getBody().getAccessToken();
    }


    @Given("the application is running")
    public void the_application_is_running() {
        System.out.println("Application is running on " + BASE_URL);
    }


    @When("I send a POST request to {string} with body")
    public void i_send_a_post_request_to_with_body(String endpoint, String requestBody) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + requestBody);
        responseAuthentication = restTemplate.postForEntity(url, request, AuthenticationResponseDTO.class);
    }

    @Then("the response status should be {int}")
    public void the_response_status_should_be(Integer statusCode) {
        assertEquals(statusCode, responseAuthentication.getStatusCodeValue());
    }

    @Then("the response should contain {string}")
    public void the_response_should_contain(String expectedContent) {
        assertNotNull(responseAuthentication.getBody().getAccessToken());
        assertNotNull(responseAuthentication.getBody().getRefreshToken());
    }


    @When("I send a POST request to {string} with {string}")
    public void iSendAPOSTRequestToWith(String endpoint, String code) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint + "/" + code;
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending POST request to " + url + " with code: " + code);
        // response = restTemplate.postForEntity(url, request, Map.class);


    }

    @When("I send a POST request to {string} to refresh token")
    public void iSendAPOSTRequestToToRefreshToken(String endpoint) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending POST request to " + url);
        response = restTemplate.postForEntity(url, request, Void.class);
    }

    @And("I send a GET request to {string} with token")
    public void iSendAPOSTRequestToWithToken(String endpoint) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending POST request to " + url);
        responseRole = restTemplate.exchange(url, HttpMethod.GET, request, Role.class);

    }

    @And("the response is the role")
    public void theResponseIsTheRole() {
        System.out.println("the role is " + responseRole.getBody());
    }

    @And("the response is {string}")
    public void theResponseIs(String role) {
        System.out.println("the role is " + responseRole.getBody());
        assertEquals(role, responseRole.getBody().name());

    }
}
