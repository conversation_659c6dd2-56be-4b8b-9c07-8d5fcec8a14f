package cucumber.steps;

import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import cucumber.config.LoaderConfig;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
@TestExecutionListeners(listeners = {WithSecurityContextTestExecutionListener.class})
public class HrbpSteps {

    private ResponseEntity<Map<String, String>> responseMap;
    private HttpClientErrorException exception;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");


    @Given("the token to test hrbp is not null")
    public void theTokenToTestHrbpIsNotNull() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);

    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a HRBP POST request to {string} with body")
    public void iSendAHRBPPOSTRequestToWithBody(String endpoint, String requestBody) throws JsonProcessingException {

        String sessionPassword = LoaderConfig.getProperty("session.password");
        ObjectMapper objectMapper = new ObjectMapper();

        // Parse the JSON string into a JsonNode
        JsonNode jsonNode = objectMapper.readTree(requestBody);

        // Modify the attribute (e.g., "name")
        ((ObjectNode) jsonNode).put("connectedUserPassword", sessionPassword);

        // Convert back to JSON string
        String newRequestBody = objectMapper.writeValueAsString(jsonNode);

        System.out.println(newRequestBody);
        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(newRequestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + newRequestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.POST,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });
    }


    @Then("the POST hrbp response status should be {int}")
    public void thePOSTHrbpResponseStatusShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }
}
