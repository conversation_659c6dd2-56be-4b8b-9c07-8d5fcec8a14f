package cucumber.steps;

import com.capgemini.trialperiodapi.dto.response.MacroPeopleUnitResponseDTO;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.LoaderConfig;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
@Transactional
@TestExecutionListeners(listeners = {WithSecurityContextTestExecutionListener.class})
public class MacroPeopleUnitSteps {

    private ResponseEntity<MacroPeopleUnitResponseDTO> response;
    private ResponseEntity<List<String>> responseListe;
    private ResponseEntity<Map<String, String>> responseMap;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String URL_MacroPU = "/api/v1/macro-people-units";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");

    @Given("the token to test MacroPeopleUnit is not null")
    public void theTokenToTestMacroPeopleUnitIsNotNull() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);
    }

    @When("I send a GET request with macroPeopleUnitName {string}")
    public void iSendAGETRequestWithMacroPeopleUnitName(String macroPeopleUnitName) {
        {
            String url = BASE_URL + URL_MacroPU + "/" + macroPeopleUnitName;

            HttpHeaders headers = new HttpHeaders();
            String testContext = TestContext.getTokenContext();
            headers.set("Authorization", testContext);
            headers.set("Content-Type", "application/json");

            HttpEntity<String> request = new HttpEntity<>(headers);
            System.out.println("Sending GET request to " + url);
            response = restTemplate.exchange(url, HttpMethod.GET, request, MacroPeopleUnitResponseDTO.class);


        }
    }

    @Then("the GET response status to find macroPeopleUnitName by name should be {int}")
    public void theGETResponseStatusToFindMacroPeopleUnitNameByNameShouldBe(int statusCode) {
        System.out.println("GET Body :" + response.getBody().getName());
        assertEquals(statusCode, response.getStatusCode().value());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET search request with {string}")
    public void iSendAGETSearchRequestWith(String keyword) {

        URI url = UriComponentsBuilder.fromHttpUrl(BASE_URL + URL_MacroPU + "/search")
                .queryParam("keyword", keyword)
                .build()
                .toUri();


        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<?> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        responseListe = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<List<String>>() {
                });

    }

    @Then("the GET response to search macroPeopleUnit should be not null")
    public void theGETResponseToSearchMacroPeopleUnitShouldBeNotNull() {
        assertNotNull(responseListe.getBody());
        System.out.println("GET Body :" + responseListe.getBody());
    }

    @When("I send a MacroPeopleUnit POST request with body")
    public void iSendAMacroPeopleUnitPOSTRequestWithBody(String requestBody) {

        String url = BASE_URL + URL_MacroPU;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.POST,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });


    }

    @Then("the POST response to create MacroPeopleUnit status should be {int}")
    public void thePOSTResponseToCreateMacroPeopleUnitStatusShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @When("I send a PUT request with macroPeopleUnitName {string} and body")
    public void iSendAPUTRequestWithMacroPeopleUnitNameAndBody(String macroPeopleUnitName, String requestBody) {

        String url = BASE_URL + URL_MacroPU + "/" + macroPeopleUnitName;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending PUT request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.PUT,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the PUT response status to modify macroPeopleUnit should be {int}")
    public void thePUTResponseStatusToModifyMacroPeopleUnitShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @When("I send a DELETE request with macroPeopleUnitName {string}")
    public void iSendADELETERequestWithMacroPeopleUnitName(String macroPeopleUnitName) {

        String url = BASE_URL + URL_MacroPU + "/" + macroPeopleUnitName;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending DELETE request to " + url + " with macroPeopleUnitName: " + macroPeopleUnitName);
        responseMap = restTemplate.exchange(url,
                HttpMethod.DELETE,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the DELETE response status to remove macroPeopleUnit should be {int}")
    public void theDELETEResponseStatusToRemoveMacroPeopleUnitShouldBe(int statusCode) {
        System.out.println("GET Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }
}
