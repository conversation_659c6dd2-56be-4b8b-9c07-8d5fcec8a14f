package cucumber.steps;

import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitManagerResponseDTO;
import cucumber.config.LoaderConfig;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.Before;
import io.cucumber.java.BeforeAll;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
@TestExecutionListeners(listeners = {WithSecurityContextTestExecutionListener.class})
public class PUMSteps {

    private ResponseEntity<PeopleUnitManagerResponseDTO> response;
    private ResponseEntity<List<PeopleUnitManagerResponseDTO>> responseListe;
    private ResponseEntity<Map<String, String>> responseMap;
    private HttpClientErrorException exception;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");

    @Given("the token to test PUM is not null")
    public void theTokenIsNotNull() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET request to {string} with {string}")
    public void iSendAGETRequestToWith(String endpoint, String pumId) {
        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint + "/" + pumId;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        response = restTemplate.exchange(url, HttpMethod.GET, request, PeopleUnitManagerResponseDTO.class);


    }

    @Then("the GET response status should be {int}")
    public void theGETResponseStatusShouldBe(int statusCode) {
        System.out.println("GET Body :" + response.getBody().getLastname());
        assertEquals(statusCode, response.getStatusCode().value());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET search request to {string} with {string}")
    public void iSendAGETSearchRequestToWith(String endpoint, String keyword) {
        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        //String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint ;
        URI url = UriComponentsBuilder.fromHttpUrl(BASE_URL + endpoint)
                .queryParam("keyword", keyword)
                .build()
                .toUri();

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();

        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<?> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        responseListe = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<List<PeopleUnitManagerResponseDTO>>() {
                });

    }

    @Then("the GET response list should be not null")
    public void theGETResponseListShouldBeNotNull() {
        assertNotNull(responseListe.getBody());
        System.out.println("GET Body :" + responseListe.getBody());
    }


    @When("I send a PUM POST request to {string} with body")
    public void iSendAPOSTRequestToWithWithBody(String endpoint, String requestBody) {
        {
            String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;
            HttpHeaders headers = new HttpHeaders();
            String testContext = TestContext.getTokenContext();
            headers.set("Authorization", testContext);
            headers.set("Content-Type", "application/json");
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            System.out.println("Sending POST request to " + url + " with body: " + requestBody);
            responseMap = restTemplate.exchange(url,
                    HttpMethod.POST,
                    request, new ParameterizedTypeReference<Map<String, String>>() {
                    });
        }
    }

    @Then("the POST response status should be {int}")
    public void thePOSTResponseStatusShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a DELETE request to {string} with pumID {string}")
    public void iSendADELETERequestToWith(String endpoint, String pumId) {
        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        String url = (endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint) + "/" + pumId;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending DELETE request to " + url + " with pumId: " + pumId);
        responseMap = restTemplate.exchange(url,
                HttpMethod.DELETE,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the DELETE response status should be {int}")
    public void theDELETEResponseStatusShouldBe(int codeStatus) {
        System.out.println("DELETE Response code Status : " + codeStatus);
        assertEquals(codeStatus, responseMap.getStatusCode().value());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a PUT request to {string} with pumID {string} and")
    public void iSendAPUTRequestToWithPumIDAnd(String endpoint, String pumID, String requestBody) {
        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        String url = (endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint) + "/" + pumID;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending PUT request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.PUT,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the PUT response status should be {int}")
    public void thePUTResponseStatusShouldBe(int codeStatus) {
        System.out.println("DELETE Response code Status : " + codeStatus);
        assertEquals(codeStatus, responseMap.getStatusCode().value());
    }
}
