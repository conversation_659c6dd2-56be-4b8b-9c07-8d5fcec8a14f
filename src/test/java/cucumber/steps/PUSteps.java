package cucumber.steps;

import com.capgemini.trialperiodapi.dto.response.AuthenticationResponseDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import com.capgemini.trialperiodapi.dto.response.SpocsHrPumResponseDTO;
import cucumber.config.LoaderConfig;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
@Transactional
@TestExecutionListeners(listeners = {WithSecurityContextTestExecutionListener.class})
@ActiveProfiles("test")
public class PUSteps {

    private ResponseEntity<PeopleUnitResponseDTO> response;
    private ResponseEntity<SpocsHrPumResponseDTO> responseSpocsHrPumResponseDTO;
    private ResponseEntity<List<SpocsHrPumResponseDTO>> responseListe;
    private ResponseEntity<Map<String, String>> responseMap;
    private ResponseEntity<List<String>> responseListeString;
    private HttpClientErrorException exception;


    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String URL_PU = "/api/v1/people-units";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");

    @Given("the token to test PU is not null")
    public void theTokenIsNotNull() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a PU GET request as {string}")
    public void iSendAPUGETRequestAs(String username) {

        String url = BASE_URL + URL_PU;

        System.out.println("Sending GET request to " + url);

        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<?> request = new HttpEntity<>(headers);

        try {
            responseListe = restTemplate.exchange(url, HttpMethod.GET, request,
                    new ParameterizedTypeReference<List<SpocsHrPumResponseDTO>>() {
                    });
        } catch (HttpClientErrorException e) {
            System.out.println("GET request failed: " + e.getStatusCode() + " - " + e.getResponseBodyAsString());
        }
    }

    @Then("the PU GET response status should be {int}")
    public void thePUGETResponseStatusShouldBe(int statutCode) {
        System.out.println("GET RESPONSE Body :" + responseListe.getBody());
        assertEquals(statutCode, responseListe.getStatusCode().value());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET search PU request to {string} with {string}")
    public void iSendAGETSearchPURequestToWith(String endpoint, String keyword) {
        URI url = UriComponentsBuilder.fromHttpUrl(BASE_URL + URL_PU + endpoint)
                .queryParam("keyword", keyword)
                .build()
                .toUri();

        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<?> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        responseListeString = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<List<String>>() {
                });
    }

    @Then("the GET response list PU should be not null")
    public void theGETResponseListPUShouldBeNotNull() {
        assertNotNull(responseListeString.getBody());
        System.out.println("GET Body :" + responseListeString.getBody());

    }

    @When("I send a GET request to find PU with {string}")
    public void iSendAGETRequestToFindPUWith(String peopleUnitId) {
        String url = BASE_URL + URL_PU + "/" + peopleUnitId;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        response = restTemplate.exchange(url, HttpMethod.GET, request, PeopleUnitResponseDTO.class);

        System.out.println(response);
    }

    @Then("the GET request to find PU response status should be {int}")
    public void theGETRequestToFindPUResponseStatusShouldBe(int statusCode) {
        System.out.println("GET Body :" + response.getBody().getName());
        assertEquals(statusCode, response.getStatusCode().value());
    }


    @When("I send a DELETE request  with PU name {string}")
    public void iSendADELETERequestWithPUName(String namePU) {

        String url = BASE_URL + URL_PU + "/" + namePU;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending DELETE request to " + url + " with namePU: " + namePU);
        responseMap = restTemplate.exchange(url,
                HttpMethod.DELETE,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @When("I send a PU POST request with body")
    public void iSendAPUPOSTRequestWithBody(String requestBody) {

        String url = BASE_URL + URL_PU;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.POST,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the POST response status  for POST should be {int}")
    public void thePOSTResponseStatusForPOSTShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @When("I send a PUT request to with idPU {string} and")
    public void iSendAPUTRequestToWithIdPUAnd(String idPU, String requestBody) {

        String url = BASE_URL + URL_PU + "/" + idPU;
        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.PUT,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the PUT response status for PUT PU should be {int}")
    public void thePUTResponseStatusForPUTPUShouldBe(int statusCode) {

        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @Then("the DELETE response status status for DELETE PU should be {int}")
    public void theDELETEResponseStatusStatusForDELETEPUShouldBe(int statusCode) {

        System.out.println("POST RESPONSE Body :" + responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @When("I send a GET request to get PU with macroPeopleUnitName {string}")
    public void iSendAGETRequestToGetPUWithMacroPeopleUnitName(String macroPeopleUnitName) {
        {
            String url = BASE_URL + URL_PU + "/" + macroPeopleUnitName + "/details";

            HttpHeaders headers = new HttpHeaders();
            String testContext = TestContext.getTokenContext();
            headers.set("Authorization", testContext);
            headers.set("Content-Type", "application/json");

            HttpEntity<String> request = new HttpEntity<>(headers);
            System.out.println("Sending GET request to " + url);
            responseSpocsHrPumResponseDTO = restTemplate.exchange(url, HttpMethod.GET, request, SpocsHrPumResponseDTO.class);

            System.out.println(response);
        }
    }

    @Then("the GET request to find PU response status with macroPeopleUnitName should be {int}")
    public void theGETRequestToFindPUResponseStatusWithMacroPeopleUnitNameShouldBe(int statusCode) {
        System.out.println("POST RESPONSE Body :" + responseSpocsHrPumResponseDTO.getBody());
        assertEquals(statusCode, responseSpocsHrPumResponseDTO.getStatusCode().value());

    }
}
