package cucumber.steps;


import com.capgemini.trialperiodapi.dto.response.ProjectManagerResponseDTO;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.LoaderConfig;
import cucumber.config.PageDeserializer;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
public class ProjectManagerSteps {

    private ResponseEntity<PageDeserializer<ProjectManagerResponseDTO>> response;
    private ResponseEntity<ProjectManagerResponseDTO> responseProjectManagerResponseDTO;
    private ResponseEntity<List<ProjectManagerResponseDTO>> responseProjectManagers;
    private ResponseEntity<Map<String, String>> responseMap;

    private HttpClientErrorException exception;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");

    @Given("projectManager application is running")
    public void projectManager_application_is_running() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);
    }

    @When("I send a POST request to {string} with request body")
    public void iSendAPOSTRequestToWithRequestBody(String endpoint, String requestBody) {

        String url = BASE_URL + endpoint;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending POST request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.POST,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @When("I send a Get request to {string} with query parameters")
    public void i_send_a_Get_request_to_with_query_parameters(String endpoint, Map<String, String> parameters) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        StringBuilder urlWithParams = new StringBuilder(url);
        if (!parameters.isEmpty()) {
            urlWithParams.append("?");
            parameters.forEach((key, value) -> urlWithParams.append(key).append("=").append(value).append("&"));
            urlWithParams.deleteCharAt(urlWithParams.length() - 1);
        }
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending  " + request);

        response = restTemplate.exchange(urlWithParams.toString(),
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<PageDeserializer<ProjectManagerResponseDTO>>() {
                });
    }

    @When("I send a Get request to {string} with {long}")
    public void I_send_a_Get_request_to(String endpoint, Long projectManagerId) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint + "/" + projectManagerId;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");
        HttpEntity<String> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        System.out.println("Sending  " + testContext);

        responseProjectManagerResponseDTO = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                ProjectManagerResponseDTO.class);

    }

    @When("I send a GET request to {string} with query parameter")
    public void i_send_a_GET_request_to_with_query_parameter(String endpoint, List<String> keywords) {
        String url = endpoint.startsWith("http") ? endpoint : BASE_URL + endpoint;

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        String keyword = keywords.get(0);

        url = url + "?keyword=" + keyword;

        HttpEntity<String> request = new HttpEntity<>(headers);

        responseProjectManagers = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<List<ProjectManagerResponseDTO>>() {
                }
        );
    }

    @Then("projectManager response status should be {int}")
    public void projectManager_response_status_should_be(int status) {
        System.out.println("Response status should: " + status);
        assertEquals(status, response.getStatusCode().value());
    }

    @Then("the response should contain a list of project managers")
    public void the_response_should_contain_a_list_of_project_managers() {
        Page<ProjectManagerResponseDTO> projectManagers = (PageDeserializer<ProjectManagerResponseDTO>) response.getBody();
        assertNotNull(projectManagers);
    }

    @Then("the response should contain the Project Manager details")
    public void the_response_should_contain_the_Project_Manager_details() {
        assertNotNull(responseProjectManagerResponseDTO.getBody());
        ProjectManagerResponseDTO projectManager = responseProjectManagerResponseDTO.getBody();
        assertNotNull(projectManager.getId());
    }


    @Then("the response map project manager status should be {int}")
    public void theResponseMapProjectManagerStatusShouldBe(int statusCode) {
        assertNotNull(responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());
    }

    @Then("the response projectManager status should be {int}")
    public void theResponseProjectManagerStatusShouldBe(int statusCode) {
        assertNotNull(responseProjectManagerResponseDTO.getBody());
        assertEquals(statusCode, responseProjectManagerResponseDTO.getStatusCode().value());
    }

    @And("the response is a list of project manager should be {int}")
    public void theResponseIsAListOfProjectManagerShouldBe(int statusCode) {
        assertNotNull(responseProjectManagers.getBody());
        assertEquals(statusCode, responseProjectManagers.getStatusCode().value());
    }
}
