package cucumber.steps;

import com.capgemini.trialperiodapi.dto.request.UserRequestDTO;
import com.capgemini.trialperiodapi.dto.response.PeopleUnitResponseDTO;
import cucumber.config.CucumberSpringConfiguration;
import cucumber.config.LoaderConfig;
import cucumber.config.PageDeserializer;
import cucumber.config.TestContext;
import cucumber.runner.CucumberTestRunner;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {CucumberSpringConfiguration.class, CucumberTestRunner.class})
@Transactional
@TestExecutionListeners(listeners = {WithSecurityContextTestExecutionListener.class})
@ActiveProfiles("test")
@RequiredArgsConstructor
public class UserSteps {

    private ResponseEntity<UserRequestDTO> response;
    private ResponseEntity<PageDeserializer<UserRequestDTO>> responsePage;
    private ResponseEntity<List<UserRequestDTO>> responseListe;
    private ResponseEntity<Map<String, String>> responseMap;
    private ResponseEntity<List<PeopleUnitResponseDTO>> responseListePeopleUnitResponseDTO;

    @Autowired
    private static RestTemplate restTemplate = new RestTemplate();

    private static final String BASE_URL = "http://localhost:8081";
    private static final String URL_USER = "/api/v1/users";
    private static final String ADMIN_USER_NAME = "abounass";
    private static final String ADMIN_PASSWORD = LoaderConfig.getProperty("admin.password");


    @Given("the token to test User request is not null")
    public void theTokenIsNotNull() {
        String testContext = TestContext.getTokenContext();
        System.out.println("The token : " + testContext);
        assertNotNull(testContext);
    }


    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET connected User")
    public void iSendAGETConnectedUser() {
        URI url = UriComponentsBuilder.fromHttpUrl(BASE_URL + URL_USER + "/current-user")
                .build()
                .toUri();

        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<?> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        response = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                UserRequestDTO.class);
    }

    @Then("the User GET response status should be {int}")
    public void theUserGETResponseStatusShouldBe(int statusCode) {
        assertNotNull(response.getBody());
        assertEquals(statusCode, response.getStatusCode().value());

    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a GET to find connected user people units")
    public void iSendAGETToFindConnectedUserPeopleUnits() {
        URI url = UriComponentsBuilder.fromHttpUrl(BASE_URL + URL_USER + "/current-user/people-units")
                .build()
                .toUri();

        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<?> request = new HttpEntity<>(headers);
        System.out.println("Sending GET request to " + url);
        responseListePeopleUnitResponseDTO = restTemplate.exchange(url,
                HttpMethod.GET,
                request,
                new ParameterizedTypeReference<List<PeopleUnitResponseDTO>>() {
                });
    }

    @Then("the User GET response list poeple unit status should be {int}")
    public void theUserGETResponseListPoepleUnitStatusShouldBe(int statusCode) {
        assertNotNull(responseListePeopleUnitResponseDTO.getBody());
        assertEquals(statusCode, responseListePeopleUnitResponseDTO.getStatusCode().value());
        System.out.println("GET Body :" + responseListePeopleUnitResponseDTO.getBody().get(0).getName());
    }

    @WithMockUser(username = ADMIN_USER_NAME, roles = {"ADMIN"})
    @When("I send a PUT request to update connected user with body")
    public void iSendAPUTRequestToUpdateConnectedUserWithBody(String requestBody) {

        String url = BASE_URL + URL_USER;

        // Simulate the Principal
        User user = new User(ADMIN_USER_NAME, ADMIN_PASSWORD, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities()));

        HttpHeaders headers = new HttpHeaders();
        String testContext = TestContext.getTokenContext();
        headers.set("Authorization", testContext);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        System.out.println("Sending PUT request to " + url + " with body: " + requestBody);
        responseMap = restTemplate.exchange(url,
                HttpMethod.PUT,
                request, new ParameterizedTypeReference<Map<String, String>>() {
                });

    }

    @Then("the PUT response status for PUT connected user request should be {int}")
    public void thePUTResponseStatusForPUTConnectedUserRequestShouldBe(int statusCode) {
        assertNotNull(responseMap.getBody());
        assertEquals(statusCode, responseMap.getStatusCode().value());

    }

}
