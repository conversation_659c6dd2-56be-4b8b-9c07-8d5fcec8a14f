# Configuration pour les tests
spring.profiles.active=test

# Base de données en mémoire pour les tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate pour H2
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Désactiver Liquibase pour les tests
spring.liquibase.enabled=false

# Configuration de l'application pour les tests
app.name=Trial Period Test
app.admin-username=testadmin
app.admin-email=<EMAIL>

# JWT pour les tests (clé plus longue)
app.jwt-secret-key=test-secret-key-for-testing-minimum-32-characters-long
app.access-token-expiration=86400000
app.refresh-token-expiration=604800000
app.reset-password-token-expiration=1440000

# URLs frontend pour les tests
app.frontend-activation-url=http://localhost:4200/verify-email
app.frontend-reset-password-url=http://localhost:4200/reset-password
app.frontend-login-url=http://localhost:4200/login

# Email pour les tests (désactivé)
spring.mail.host=localhost
spring.mail.port=25
app.email-server-host=localhost
app.email-server-protocol=smtp
app.email-server-port=25

# Autres configurations
app.allowed-origins=http://localhost:4200
app.registration-allowed=true

# Jasypt simplifié pour les tests
jasypt.encryptor.password=test
jasypt.encryptor.algorithm=PBEWithMD5AndDES

# Logging réduit pour les tests
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
