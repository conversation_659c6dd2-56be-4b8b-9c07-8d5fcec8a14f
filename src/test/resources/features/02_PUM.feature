Feature: Test PUM requests

  Sc<PERSON>rio: POST create pum
    Given the token to test <PERSON>UM is not null
    When I send a PUM POST request to "/api/v1/pums" with body
    """
           {
               "firstname" : "Firstname PUM" ,
               "lastname" : "Lastname PUM" ,
               "email" : "<EMAIL>",
               "macroPeopleUnitNames": []
           }
      """
    Then the POST response status should be 201

  @ignore
  Scenario: POST create a second pum
    Given the token to test PUM is not null
    When I send a PUM POST request to "/api/v1/pums" with body
    """
           {
               "firstname" : "Firstname PUM2" ,
               "lastname" : "Lastname PUM2" ,
               "email" : "<EMAIL>",
               "macroPeopleUnitNames": []
           }
      """
    Then the POST response status should be 201

  Scenario: Get pum by id
    Given the token to test <PERSON>UM is not null
    When I send a GET request to "/api/v1/pums" with "1"
    Then the GET response status should be 200

  Scenario: Get pum by search
    Given the token to test <PERSON>UM is not null
    When I send a GET search request to "/api/v1/pums/search" with "PUM"
    Then the GET response list should be not null

  Scenario: PUT pum
    Given the token to test <PERSON>UM is not null
    When I send a PUT request to "/api/v1/pums" with pumID "2" and
    """
           {
               "firstname" : "Firstname PUM3" ,
               "lastname" : "Lastname PUM3" ,
               "email" : "<EMAIL>",
               "macroPeopleUnitNames": []
           }
      """
    Then the PUT response status should be 200

  Scenario: DELETE pum
    Given the token to test PUM is not null
    When I send a DELETE request to "/api/v1/pums" with pumID "<EMAIL>"
    Then the DELETE response status should be 200


