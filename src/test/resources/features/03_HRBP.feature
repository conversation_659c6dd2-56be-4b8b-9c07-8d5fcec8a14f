Feature: Test HRBP requests

  <PERSON><PERSON><PERSON>: POST create Hrbp
    Given the token to test hrbp is not null
    When I send a HRBP POST request to "/api/v1/hrbps" with body
    """
           {
                "firstname": "firstname test hrbp",
                "lastname": "lastname test hrbp",
                "username": "hrbp",
                "email": "<EMAIL>",
                "macroPeopleUnitNames": [],
                "connectedUserPassword": "*****"
           }
      """
    Then the POST hrbp response status should be 200
