Feature: Test HR requests

  Sc<PERSON>rio: POST create Hr
    Given the token to test hr is not null
    When I send a HR POST request to "/api/v1/hrs" with body
    """
           {
                "firstname": "firstname test hr",
                "lastname": "lastname test hr",
                "username": "hr_test",
                "email": "<EMAIL>",
                "macroPeopleUnitNames": [],
                 "connectedUserPassword": "*****"
           }
      """
    Then the POST hr response status should be 201
