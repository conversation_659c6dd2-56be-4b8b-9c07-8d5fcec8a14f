Feature: Test MacroPeopleUnit requests

  Sc<PERSON>rio: POST create macroPeopleUnit
    Given the token to test MacroPeopleUnit is not null
    When I send a MacroPeopleUnit POST request with body
    """
           {
               "name" : "<PERSON>ro" ,
               "peopleUnitNames": [],
               "hrEmails":["<EMAIL>"],
               "hrbpEmails":["<EMAIL>"],
               "pumEmails":["<EMAIL>"]

           }
      """
    Then the POST response to create MacroPeopleUnit status should be 201

  Scenario: POST create a second macroPeopleUnit
    Given the token to test MacroPeopleUnit is not null
    When I send a MacroPeopleUnit POST request with body
    """
           {
               "name" : "Macro3" ,
               "peopleUnitNames": [],
               "hrEmails":["<EMAIL>"],
               "hrbpEmails":["<EMAIL>"],
               "pumEmails":["<EMAIL>"]

           }
      """
    Then the POST response to create MacroPeopleUnit status should be 201

  Scenario: Get macroPeopleUnit by name
    Given the token to test MacroPeopleUnit is not null
    When I send a GET request with macroPeopleUnitName "Macro3"
    Then the GET response status to find macroPeopleUnitName by name should be 201

  Scenario: Get macroPeopleUnit by search
    Given the token to test MacroPeopleUnit is not null
    When I send a GET search request with "Macro3"
    Then the GET response to search macroPeopleUnit should be not null

  Scenario: PUT macroPeopleUnit
    Given the token to test MacroPeopleUnit is not null
    When I send a PUT request with macroPeopleUnitName "Macro3" and body
    """
           {
               "name" : "Macro2" ,
               "peopleUnitNames": [],
               "hrEmails":["<EMAIL>"],
               "hrbpEmails":["<EMAIL>"],
               "pumEmails":["<EMAIL>"]

           }
      """
    Then the PUT response status to modify macroPeopleUnit should be 200

  Scenario: DELETE macroPeopleUnit
    Given the token to test MacroPeopleUnit is not null
    When I send a DELETE request with macroPeopleUnitName "Macro"
    Then the DELETE response status to remove macroPeopleUnit should be 200