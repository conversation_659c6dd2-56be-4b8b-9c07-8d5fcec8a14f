Feature: Test PU requests

  Scenario: POST create pu
    Given the token to test PU is not null
    When I send a PU POST request with body
    """
           {
               "name" : "PU_TEST" ,
               "macroPeopleUnitName": "Macro2"
           }
      """
    Then the POST response status  for POST should be 201

  Scenario: POST create a second pu
    Given the token to test PU is not null
    When I send a PU POST request with body
    """
           {
               "name" : "PU_TEST2" ,
               "macroPeopleUnitName": "Macro2"
           }
      """
    Then the POST response status  for POST should be 201

  Scenario: Get all PU
    Given the token to test PU is not null
    When I send a PU GET request as "abounass"
    Then the PU GET response status should be 200


  Scenario: Get PU by search
    Given the token to test PU is not null
    When I send a GET search PU request to "/search" with "PU_TEST"
    Then the GET response list PU should be not null


  Scenario: PUT pu
    Given the token to test PU is not null
    When I send a PUT request to with idPU "PU_TEST" and
  """
           {
               "name" : "PU" ,
               "macroPeopleUnitName": "Macro2"
           }
      """
    Then the PUT response status for PUT PU should be 200

  Scenario: DELETE PU
    Given the token to test PU is not null
    When I send a DELETE request  with PU name "PU_TEST2"
    Then the DELETE response status status for DELETE PU should be 200