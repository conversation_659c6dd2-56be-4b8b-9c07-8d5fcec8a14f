Feature: Test USER requests

  Scenario: PUT user
    Given the token to test User request is not null
    When I send a PUT request to update connected user with body
    """
           {
                "firstname":"jam<PERSON>",
                "lastname": "oulhaj",
                "username": "abounass",
                "email": "abdessamad.bounas<PERSON><EMAIL>"
           }
      """
    Then the PUT response status for PUT connected user request should be 200

  Scenario: Get connected User
    Given the token to test User request is not null
    When I send a GET connected User
    Then the User GET response status should be 200

  Scenario: Get Connected User People Units
    Given the token to test User request is not null
    When I send a GET to find connected user people units
    Then the User GET response list poeple unit status should be 200

