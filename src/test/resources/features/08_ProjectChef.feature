Feature: Project Manager Management

  Scenario: Create a new projectManager
    Given projectManager application is running
    When I send a POST request to "/api/v1/project-managers" with request body
      """
      {
        "firstname": "firstname test projectManager",
        "lastname": "lastname test projectManager",
        "username": "projectManager",
        "email": "<EMAIL>",
        "projectNames": []
      }
      """
    Then the response map project manager status should be 200


  Scenario: Get all projectManagers
    Given projectManager application is running
    When  I send a Get request to "/api/v1/project-managers" with query parameters
      | page         | 0      |
      | itemsPerPage | 10     |
      | sortField    | CREATED_ON |
      | sortDirection | DESC   |
      | keyword      | test    |
    Then projectManager response status should be 200
    And the response should contain a list of project managers

  Scenario: Get projectManager by id
    Given projectManager application is running
    When I send a Get request to "/api/v1/project-managers" with 3
    Then the response projectManager status should be 200

  Scenario: Search projectManagers by keyword
    Given projectManager application is running
    When I send a GET request to "/api/v1/project-managers/search" with query parameter
      | keyword |
      | projectManager    |
    And the response is a list of project manager should be 200

