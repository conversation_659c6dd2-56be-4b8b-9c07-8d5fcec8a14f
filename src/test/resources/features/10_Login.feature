Feature: Test POST request

  Sc<PERSON>rio: Post to refresh-token
    Given the application is running
    When I send a POST request to "/api/v1/auth/authenticate" with body
      """
      {
         "username":"abounass",
         "password": "Password.0000"
      }
      """
    And I send a POST request to "/api/v1/auth/refresh-token" to refresh token
    Then the response status should be 200

  Scenario: Post to authenticate
    Given the application is running
    When I send a POST request to "/api/v1/auth/authenticate" with body
      """
      {
        "username":"abounass",
         "password": "Password.0000"
      }
      """
    Then the response status should be 200
    And the response should contain "token"


  Sc<PERSON>rio: Get role
    Given the application is running
    When I send a POST request to "/api/v1/auth/authenticate" with body
      """
      {
          "username":"abounass",
         "password": "Password.0000"
      }
      """
    And I send a GET request to "/api/v1/auth/role" with token
    Then the response status should be 200
    And the response is "ADMIN"

