2024-09-19T14:21:29.133+01:00  INFO 25084 --- [Trial Period] [main] c.c.t.TrialPeriodApiApplication          : Starting TrialPeriodApiApplication using Java 22 with PID 25084 (C:\BOUNASSEH\Projects\trial-period-api\target\classes started by abounass in C:\BOUNASSEH\Projects\trial-period-api)
2024-09-19T14:21:29.136+01:00  INFO 25084 --- [Trial Period] [main] c.c.t.TrialPeriodApiApplication          : The following 1 profile is active: "dev"
2024-09-19T14:21:31.210+01:00  INFO 25084 --- [Trial Period] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-09-19T14:21:31.412+01:00  INFO 25084 --- [Trial Period] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 192 ms. Found 14 JPA repository interfaces.
2024-09-19T14:21:31.756+01:00  INFO 25084 --- [Trial Period] [main] ptablePropertiesBeanFactoryPostProcessor : Post-processing PropertySource instances
2024-09-19T14:21:31.757+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2024-09-19T14:21:31.758+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2024-09-19T14:21:31.758+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2024-09-19T14:21:31.760+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-09-19T14:21:31.760+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2024-09-19T14:21:31.761+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-09-19T14:21:31.761+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [management.properties]' via location 'optional:management.properties' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-09-19T14:21:31.761+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [secrets.properties]' via location 'optional:secrets.properties' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-09-19T14:21:31.761+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-09-19T14:21:32.189+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.filter.DefaultLazyPropertyFilter   : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-09-19T14:21:32.205+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.r.DefaultLazyPropertyResolver      : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-09-19T14:21:32.208+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.d.DefaultLazyPropertyDetector      : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-09-19T14:21:32.696+01:00  INFO 25084 --- [Trial Period] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2024-09-19T14:21:32.714+01:00  INFO 25084 --- [Trial Period] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2024-09-19T14:21:32.715+01:00  INFO 25084 --- [Trial Period] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2024-09-19T14:21:32.863+01:00  INFO 25084 --- [Trial Period] [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2024-09-19T14:21:32.863+01:00  INFO 25084 --- [Trial Period] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3662 ms
2024-09-19T14:21:33.104+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.encryptor.DefaultLazyEncryptor     : String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-09-19T14:21:33.136+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2024-09-19T14:21:33.137+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2024-09-19T14:21:33.137+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2024-09-19T14:21:33.138+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2024-09-19T14:21:33.138+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-09-19T14:21:33.141+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2024-09-19T14:21:33.143+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.c.StringEncryptorBuilder           : Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2024-09-19T14:21:33.411+01:00  INFO 25084 --- [Trial Period] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2024-09-19T14:21:33.683+01:00  INFO 25084 --- [Trial Period] [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f1d1463
2024-09-19T14:21:33.688+01:00  INFO 25084 --- [Trial Period] [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2024-09-19T14:21:33.762+01:00  INFO 25084 --- [Trial Period] [main] o.f.c.internal.license.VersionPrinter    : Flyway Community Edition 9.22.3 by Redgate
2024-09-19T14:21:33.763+01:00  INFO 25084 --- [Trial Period] [main] o.f.c.internal.license.VersionPrinter    : See release notes here: https://rd.gt/416ObMi
2024-09-19T14:21:33.763+01:00  INFO 25084 --- [Trial Period] [main] o.f.c.internal.license.VersionPrinter    : 
2024-09-19T14:21:33.780+01:00  INFO 25084 --- [Trial Period] [main] org.flywaydb.core.FlywayExecutor         : Database: ********************************************* (PostgreSQL 16.0)
2024-09-19T14:21:33.786+01:00  WARN 25084 --- [Trial Period] [main] o.f.c.internal.database.base.Database    : Flyway upgrade recommended: PostgreSQL 16.0 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 15.
2024-09-19T14:21:33.834+01:00  INFO 25084 --- [Trial Period] [main] o.f.core.internal.command.DbValidate     : Successfully validated 2 migrations (execution time 00:00.035s)
2024-09-19T14:21:33.955+01:00  INFO 25084 --- [Trial Period] [main] o.f.core.internal.command.DbMigrate      : Current version of schema "public": 1
2024-09-19T14:21:33.961+01:00  INFO 25084 --- [Trial Period] [main] o.f.core.internal.command.DbMigrate      : Schema "public" is up to date. No migration necessary.
2024-09-19T14:21:34.138+01:00  INFO 25084 --- [Trial Period] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2024-09-19T14:21:34.268+01:00  INFO 25084 --- [Trial Period] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2024-09-19T14:21:34.350+01:00  INFO 25084 --- [Trial Period] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2024-09-19T14:21:34.834+01:00  INFO 25084 --- [Trial Period] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2024-09-19T14:21:34.951+01:00  WARN 25084 --- [Trial Period] [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2024-09-19T14:21:35.661+01:00  INFO 25084 --- [Trial Period] [main] o.hibernate.id.enhanced.TableGenerator   : HHH000398: Explicit segment value for id generator [hibernate_sequences.sequence_name] suggested; using default [default]
2024-09-19T14:21:35.895+01:00  WARN 25084 --- [Trial Period] [main] org.hibernate.orm.deprecation            : HHH90000021: Encountered deprecated setting [javax.persistence.schema-generation.scripts.action], use [jakarta.persistence.schema-generation.scripts.action] instead
2024-09-19T14:21:37.166+01:00  INFO 25084 --- [Trial Period] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2024-09-19T14:21:37.171+01:00  WARN 25084 --- [Trial Period] [main] org.hibernate.orm.deprecation            : HHH90000021: Encountered deprecated setting [javax.persistence.schema-generation.scripts.action], use [jakarta.persistence.schema-generation.scripts.action] instead
2024-09-19T14:21:37.179+01:00  WARN 25084 --- [Trial Period] [main] org.hibernate.orm.deprecation            : HHH90000021: Encountered deprecated setting [jakarta.persistence.schema-generation.scripts.create-target], use [javax.persistence.schema-generation.scripts.create-target] instead
2024-09-19T14:21:37.215+01:00  INFO 25084 --- [Trial Period] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-09-19T14:21:37.559+01:00  INFO 25084 --- [Trial Period] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2024-09-19T14:21:40.442+01:00  WARN 25084 --- [Trial Period] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-09-19T14:21:41.266+01:00  INFO 25084 --- [Trial Period] [main] c.c.t.config.SecurityConfig              : Request arrived ! Crossing securityFilterChain...
2024-09-19T14:21:41.281+01:00  INFO 25084 --- [Trial Period] [main] c.c.t.config.SecurityConfig              : SecurityFilterChain crossed successfully.
2024-09-19T14:21:41.334+01:00  INFO 25084 --- [Trial Period] [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@13bd90b4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1d5ba943, org.springframework.security.web.context.SecurityContextHolderFilter@662e689a, org.springframework.security.web.header.HeaderWriterFilter@2b1ad287, org.springframework.web.filter.CorsFilter@36f50eef, org.springframework.security.web.authentication.logout.LogoutFilter@346079da, com.capgemini.trialperiodapi.config.JwtAuthenticationFilter@714f3e27, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a6528fa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@14e2e4e2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@250feea4, org.springframework.security.web.session.SessionManagementFilter@4bc62244, org.springframework.security.web.access.ExceptionTranslationFilter@3d43297c, org.springframework.security.web.access.intercept.AuthorizationFilter@683e63b5]
2024-09-19T14:21:43.371+01:00  INFO 25084 --- [Trial Period] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8081 (http) with context path ''
2024-09-19T14:21:43.372+01:00  INFO 25084 --- [Trial Period] [main] u.j.c.RefreshScopeRefreshedEventListener : Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2024-09-19T14:21:43.374+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source systemProperties refreshed
2024-09-19T14:21:43.374+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source systemEnvironment refreshed
2024-09-19T14:21:43.374+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source random refreshed
2024-09-19T14:21:43.374+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [management.properties]' via location 'optional:management.properties' refreshed
2024-09-19T14:21:43.374+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [secrets.properties]' via location 'optional:secrets.properties' refreshed
2024-09-19T14:21:43.375+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' refreshed
2024-09-19T14:21:43.375+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2024-09-19T14:21:43.375+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2024-09-19T14:21:43.376+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2024-09-19T14:21:43.503+01:00  INFO 25084 --- [Trial Period] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8088 (http)
2024-09-19T14:21:43.504+01:00  INFO 25084 --- [Trial Period] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2024-09-19T14:21:43.504+01:00  INFO 25084 --- [Trial Period] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2024-09-19T14:21:43.564+01:00  INFO 25084 --- [Trial Period] [main] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2024-09-19T14:21:43.564+01:00  INFO 25084 --- [Trial Period] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 183 ms
2024-09-19T14:21:43.583+01:00  INFO 25084 --- [Trial Period] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 9 endpoint(s) beneath base path '/actuator'
2024-09-19T14:21:43.638+01:00  INFO 25084 --- [Trial Period] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8088 (http) with context path ''
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] u.j.c.RefreshScopeRefreshedEventListener : Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source servletContextInitParams refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source systemProperties refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source systemEnvironment refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source random refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [management.properties]' via location 'optional:management.properties' refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [secrets.properties]' via location 'optional:secrets.properties' refreshed
2024-09-19T14:21:43.639+01:00  INFO 25084 --- [Trial Period] [main] CachingDelegateEncryptablePropertySource : Property Source Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' refreshed
2024-09-19T14:21:43.640+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-09-19T14:21:43.640+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2024-09-19T14:21:43.640+01:00  INFO 25084 --- [Trial Period] [main] c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2024-09-19T14:21:43.657+01:00  INFO 25084 --- [Trial Period] [main] c.c.t.TrialPeriodApiApplication          : Started TrialPeriodApiApplication in 15.229 seconds (process running for 17.103)
2024-09-19T14:21:44.027+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring DispatcherServlet 'dispatcherServletRegistration'
2024-09-19T14:21:44.028+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServletRegistration'
2024-09-19T14:21:44.028+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2024-09-19T14:21:44.060+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:21:44.060+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:21:44.102+01:00  WARN 25084 --- [Trial Period] [registrationTask1] d.c.b.a.c.r.ApplicationRegistrator       : Failed to register application as Application(name=Trial Period, managementUrl=http://LMA001952.corp.capgemini.com:8088/actuator, healthUrl=http://LMA001952.corp.capgemini.com:8088/actuator/health, serviceUrl=http://LMA001952.corp.capgemini.com:8081/) at spring-boot-admin ([http://localhost:8088/instances]): 403 : [no body]. Further attempts are logged on DEBUG level
2024-09-19T14:21:53.781+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:21:53.781+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:03.795+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:03.796+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:13.791+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:13.792+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:23.794+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:23.794+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:33.803+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:33.805+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:43.788+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:43.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:22:53.795+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:22:53.795+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:03.785+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:03.785+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:13.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:13.794+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:23.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:23.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:33.787+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:33.787+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:43.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:43.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:23:53.790+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:23:53.790+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:03.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:03.790+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:13.792+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:13.792+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:23.787+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:23.787+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:33.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:33.794+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:43.784+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:43.784+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:24:53.784+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:24:53.786+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:03.780+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:03.780+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:13.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:13.789+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-2] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:23.782+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:23.782+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-3] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:33.781+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:33.781+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-4] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:43.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:43.794+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-5] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:25:53.780+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:25:53.781+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-6] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:26:03.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:26:03.793+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-7] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:26:13.778+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:26:13.779+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-8] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:26:23.795+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:26:23.795+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-9] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:26:33.791+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:26:33.791+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-10] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
2024-09-19T14:26:43.796+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Processing authentication for '/instances'
2024-09-19T14:26:43.796+01:00  INFO 25084 --- [Trial Period] [http-nio-8088-exec-1] c.c.t.config.JwtAuthenticationFilter     : Checking if the JWT exist
